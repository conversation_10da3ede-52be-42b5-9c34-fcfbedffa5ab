# macOS system files
.DS_Store

# Python virtual environments
.venv/
venv/
env/
ENV/
.env/
.ENV/

# Python bytecode and cache
__pycache__/
*.py[cod]
*$py.class

# Build and distribution artifacts
dist/
build/
*.egg-info/

# Test, coverage, and cache
.pytest_cache/
.ruff_cache/
.ropeproject/
.tox/
.cache
.coverage
.coverage.*
.hypothesis/
htmlcov/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# IDE/editor settings
.idea/
.vscode/
*.swp
*.swo

# Local development settings
.env
.env.local

# Project-specific
.windsurf/*
.augment-guidelines.md

# Workspace settings
ABB_v6.code-workspace

# Ignore all .DS_Store recursively
**/.DS_Store
