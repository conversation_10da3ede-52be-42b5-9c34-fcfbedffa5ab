# windsurf rules
.windsurfrules
.windsurf/

# MacOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Python virtual environments
venv/
env/
ENV/
.venv/
.env/
.ENV/
pyenv/

# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
dist/
build/
*.egg-info/
*.egg
.eggs/
wheels/
SHARE/
bin/
lib/
lib64/
sdist/
var/

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
coverage.json
*.cover
.hypothesis/
.pytest_cache/
pytest.ini

# Jupyter Notebook
.ipynb_checkpoints

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# Local development settings
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*

# Qt specific
*.qm
*.pro.user
*.pro.user.*
*.qbs.user
*.qbs.user.*
moc_*.cpp
qrc_*.cpp
ui_*.h
CMakeLists.txt.user*

# Project specific
# Temporary files created during processing
temp/
# Generated audiobooks
output/
# FFmpeg binaries if bundled
Resources/bin/

# Mypy cache
.mypy_cache/

# Ruff cache
.ruff_cache/

# Black formatter cache
.black/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt
.repomixignore

# VSCodeCounter
.VSCodeCounter
