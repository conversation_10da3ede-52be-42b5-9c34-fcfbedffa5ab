"""ProcessingWorker class for Audiobook Boss.
Handles audio processing in a separate thread.
"""

import os
import re
from typing import Dict, List, Optional

from PySide6.QtCore import QObject, QProcess, QProcessEnvironment, QTimer, Signal, Slot

from src.abb.ffmpeg_utils import (
    _get_executable_path,
    build_ffmpeg_command,
    build_ffmpeg_preview_command,
)


class ProcessingWorker(QObject):
    """Worker class for processing audiobooks in a separate thread.
    Inherits from QObject to support signals/slots and moveToThread.
    """

    progress_updated = Signal(int)
    progress = Signal(int)  # For backward compatibility
    status = Signal(str)
    processing_finished = Signal(str) 
    done = Signal()  # For backward compatibility
    processing_error = Signal(str)
    error = Signal(str)  # For backward compatibility

    def __init__(self, ffmpeg_path: str, parent=None) -> None:
        """Initialize the worker.

        Args:
            ffmpeg_path: Path to the FFmpeg executable
            parent: Optional parent QObject
        """
        super().__init__(parent)
        self.ffmpeg_path = ffmpeg_path
        self._process = None
        self._is_processing = False
        self._is_cancelled = False
        self._temp_files = []
        self._output_file = None
        self._kill_timer = None
        self._total_duration_seconds = 0.0

    def process(
        self,
        file_list: List[str],
        output_path: str,
        output_filename: str,
        metadata: Dict,
        settings: Dict,
        total_duration_seconds: float,
    ) -> None:
        """Process the audiobook files.

        Args:
            file_list: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            total_duration_seconds: Total duration of all input files in seconds.
        """
        if self._is_processing:
            self.error.emit("Processing already in progress")
            self.processing_error.emit("Processing already in progress")
            return
            
        self._is_processing = True
        try:
            self._is_cancelled = False
            self._temp_files = []
            self._total_duration_seconds = total_duration_seconds

            ffmpeg_exe_path = self.ffmpeg_path
            if not ffmpeg_exe_path or not os.path.isfile(ffmpeg_exe_path) or not os.access(ffmpeg_exe_path, os.X_OK):
                self.error.emit("Failed to start: FFmpeg executable not found or not executable.")
                return

            ffprobe_exe_path = _get_executable_path("ffprobe")

            cover_art_temp_path = metadata.get("cover_art_temp_path")
            if cover_art_temp_path and os.path.exists(cover_art_temp_path):
                self._temp_files.append(cover_art_temp_path)

            os.makedirs(output_path, exist_ok=True)
            self._output_file = os.path.join(output_path, output_filename)

            cmd = build_ffmpeg_command(
                input_files=file_list,
                output_file_full_path=self._output_file,
                metadata=metadata,
                settings=settings,
                ffmpeg_exe_path=ffmpeg_exe_path,
                ffprobe_exe_path=ffprobe_exe_path
            )

            if not cmd:
                self.error.emit("Failed to build FFmpeg command. Check input files and settings.")
                self._cleanup_temp_files()
                return

            self._run_process(cmd)

        except Exception as e:
            self._cleanup_temp_files()
            if self._output_file and os.path.exists(self._output_file):
                try:
                    os.remove(self._output_file)
                except OSError:
                    pass # Ignore error on deleting partial file
            self.error.emit(str(e))
            self.processing_error.emit(str(e))
        finally:
            self._is_processing = False

    @Slot()
    def start_full_process(self,
                          file_list: List[str],
                          output_path: str,
                          output_filename: str,
                          metadata: Dict,
                          settings: Dict,
                          total_duration_seconds: float) -> None:
        """Start full audiobook processing.
        This is a slot that can be called from another thread.
        
        Args:
            file_list: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            total_duration_seconds: Total duration of all input files in seconds
        """
        self.process(file_list, output_path, output_filename, metadata, settings, total_duration_seconds)

    @Slot()
    def start_preview_process(self,
                             input_file_path: str,
                             metadata: Dict,
                             settings: Dict,
                             temp_cover_path: Optional[str] = None,
                             duration_seconds: int = 30) -> None:
        """Start preview processing.
        This is a slot that can be called from another thread.
        
        Args:
            input_file_path: Path to the input audio file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            temp_cover_path: Optional path to a cover art image
            duration_seconds: Duration of the preview in seconds (default: 30)
        """
        if self._is_processing:
            self.error.emit("Processing already in progress")
            self.processing_error.emit("Processing already in progress")
            return
            
        self._is_processing = True
        try:
            self._is_cancelled = False
            self._temp_files = []
            self._total_duration_seconds = duration_seconds
            
            cmd = build_ffmpeg_preview_command(
                input_file_path=input_file_path,
                metadata=metadata,
                settings=settings,
                temp_cover_path=temp_cover_path,
                duration_seconds=duration_seconds
            )
            
            if not cmd:
                self.error.emit("Failed to build FFmpeg preview command.")
                self.processing_error.emit("Failed to build FFmpeg preview command.")
                self._is_processing = False
                return
            
            self._output_file = cmd[-1] # Output file is the last argument
            self._temp_files.append(self._output_file) # Preview output is temporary
            
            self._run_process(cmd)
            
        except Exception as e:
            self._cleanup_temp_files()
            if self._output_file and os.path.exists(self._output_file):
                try:
                    os.remove(self._output_file)
                except OSError:
                    pass # Ignore error on deleting partial file
            self.error.emit(str(e))
            self.processing_error.emit(str(e))
        finally:
            self._is_processing = False

    def cancel(self) -> None:
        """Cancel the current processing operation.
        Implements robust cancel logic: terminate → wait 2s → kill.
        """
        if not self._is_processing:
            return
            
        if not self._process:
            return

        self._is_cancelled = True
        self.status.emit("Cancelling...")
        self._process.terminate()

        self._kill_timer = QTimer()
        self._kill_timer.setSingleShot(True)
        self._kill_timer.timeout.connect(self._force_kill_process)
        self._kill_timer.start(2000)

    def _force_kill_process(self) -> None:
        """Force kill the process if it hasn't terminated after the timeout."""
        if self._process and self._process.state() != QProcess.NotRunning:
            try:
                self.status.emit("Force killing process...")
                self._process.kill()
                self._process.waitForFinished(1000)
            except Exception as e:
                self.status.emit(f"Error force killing process: {str(e)}")
        self._cleanup_after_cancel_or_error()

    def _cleanup_after_cancel_or_error(self) -> None:
        """Clean up after cancellation or error.
        This method handles both cancelled operations and process errors.
        """
        self._cleanup_temp_files()
        if self._output_file and os.path.exists(self._output_file):
            try:
                os.remove(self._output_file)
            except OSError as e:
                self.status.emit(f"Error deleting partial output file: {str(e)}")

        if self._process:
            try:
                self._process.close()
            except Exception as e:
                self.status.emit(f"Error closing process: {str(e)}")

        self.error.emit("Processing cancelled or failed")
        self.processing_error.emit("Processing cancelled or failed")

    def _process_finished(self, exit_code: int, exit_status: QProcess.ExitStatus) -> None:
        """Handle process completion.

        Args:
            exit_code: Process exit code
            exit_status: Process exit status
        """
        try:
            if self._is_cancelled:
                self._cleanup_after_cancel_or_error()
                return

            if exit_status == QProcess.NormalExit and exit_code == 0:
                self.progress.emit(100)
                self.progress_updated.emit(100)
                self.status.emit("Processing completed successfully.")
                self._cleanup_temp_files() # Only non-output temp files
                self.done.emit()
                self.processing_finished.emit(self._output_file or "")
            else:
                error_output = ""
                if self._process:
                    error_output = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace").strip()
                specific_error_message = f"FFmpeg failed (exit code {exit_code}, status {exit_status.name})"
                if error_output:
                    specific_error_message += f": {error_output}"
                else:
                    specific_error_message += "."
                self.error.emit(specific_error_message)
                self._cleanup_after_cancel_or_error() # Cleans up output file too

        except Exception as e:
            self.error.emit(f"Error handling process completion: {str(e)}")
            self._cleanup_after_cancel_or_error()
        finally:
            self._is_processing = False
            self._is_cancelled = False

    def _cleanup_temp_files(self) -> None:
        """Clean up any temporary files created during processing."""
        for temp_file in self._temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass
        self._temp_files = []

    def _run_process(self, cmd: List[str]) -> None:
        """Run the FFmpeg process and monitor its progress.

        Args:
            cmd: FFmpeg command as a list of strings
        """
        try:
            self._process = QProcess()
            self._process.setProcessEnvironment(QProcessEnvironment.systemEnvironment())
            self._process.readyReadStandardError.connect(self._read_process_output)
            self._process.finished.connect(self._process_finished)
            self._process.errorOccurred.connect(self._process_error_occurred)

            self.status.emit("Starting processing...")
            if not self._process.start(cmd[0], cmd[1:]):
                self.error.emit(f"Failed to start FFmpeg process: {self._process.errorString()}")
                self._cleanup_after_cancel_or_error()
                return

            if not self._process.waitForStarted(5000):
                self.error.emit(f"FFmpeg process failed to start: {self._process.errorString()}")
                self._cleanup_after_cancel_or_error()
                return

        except Exception as e:
            self.error.emit(f"Error starting FFmpeg process: {str(e)}")
            self._cleanup_after_cancel_or_error()

    def _read_process_output(self) -> None:
        """Read and parse the FFmpeg output to update progress and handle errors."""
        if not self._process:
            return

        try:
            stderr = bytes(self._process.readAllStandardError()).decode("utf-8", errors="replace")
            for line in stderr.splitlines():
                line = line.strip()
                if not line:
                    continue

                # Simple keyword check for errors, might need refinement
                if any(error_keyword in line.lower() for error_keyword in [
                    "error:", "failed:", "cannot", "invalid", "unsupported"
                ]):
                    self.status.emit(f"FFmpeg Potential Error: {line}")
                    # Consider if this should immediately trigger _process_error_occurred
                    # For now, it just emits a status. The exit code will determine final error.

                self.status.emit(line) # Emit raw line for logging/UI display
                progress_info = self._parse_ffmpeg_progress(line)
                if progress_info is not None:
                    self.progress.emit(progress_info)
                    self.progress_updated.emit(progress_info)

        except Exception as e:
            self.error.emit(f"Error processing FFmpeg output: {str(e)}")
            self._cleanup_after_cancel_or_error() # This path might lead to double error signals
            self.status.emit("An error occurred while processing FFmpeg output")

    def _process_error_occurred(self, error: QProcess.ProcessError) -> None:
        """Handle QProcess specific errors."""
        error_map_msg = {
            QProcess.FailedToStart: "Failed to start FFmpeg process",
            QProcess.Crashed: "FFmpeg process crashed",
            QProcess.Timedout: "FFmpeg process timed out",
            QProcess.FailedToStart: "Failed to start FFmpeg process",
            QProcess.Crashed: "FFmpeg process crashed",
            QProcess.Timedout: "FFmpeg process timed out",
            QProcess.WriteError: "Error writing to FFmpeg process",
            QProcess.ReadError: "Error reading from FFmpeg process",
            QProcess.UnknownError: "Unknown FFmpeg error"
        }.get(error, "An unknown QProcess error occurred")

        qprocess_error_string = "N/A"
        if self._process:
            qprocess_error_string = self._process.errorString()

        detailed_error_msg = f"QProcess Error: {error_map_msg}. Details: {qprocess_error_string}"
        self.error.emit(detailed_error_msg)
        self.processing_error.emit(detailed_error_msg)
        self.status.emit(f"Process error: {qprocess_error_string}")

        self._cleanup_after_cancel_or_error()
        # Note: _cleanup_temp_files() is called by _cleanup_after_cancel_or_error()

    def _parse_ffmpeg_progress(self, stderr_line: str) -> Optional[int]:
        """Parse FFmpeg stderr output to extract progress information.

        Args:
            stderr_line: FFmpeg stderr output line

        Returns:
            Progress percentage (0-100) or None if no progress info found
        """
        match = re.search(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})", stderr_line)
        if match:
            hours, minutes, seconds = int(match.group(1)), int(match.group(2)), int(match.group(3))
            # centiseconds = int(match.group(4)) # Not used for percentage
            current_time_seconds = (hours * 3600) + (minutes * 60) + seconds

            if self._total_duration_seconds > 0:
                percentage = (current_time_seconds / self._total_duration_seconds) * 100
                # Clamp to 0-100 as FFmpeg time can sometimes slightly exceed estimated total
                return max(0, min(100, int(percentage)))
            return None # Cannot calculate percentage if total_duration is unknown/zero
        return None
