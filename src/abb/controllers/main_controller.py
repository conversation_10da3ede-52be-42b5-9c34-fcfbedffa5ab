"""MainController for Audiobook Boss.

This controller orchestrates the services and provides a clean interface
for the UI components to interact with.
"""
import logging
import os
from pathlib import Path  # Added for Path object usage
from typing import Any, Dict, List, Optional

from PySide6.QtCore import QObject, Signal

from ..ffmpeg_utils import get_audio_properties
from ..services.file_service import FileService
from ..services.metadata_service import MetadataService
from ..services.path_service import PathService
from ..services.processing_service import ProcessingService
from ..services.settings_manager import SettingsManager


class MainController(QObject):
    """Controller that orchestrates the services and provides a clean interface
    for the UI components to interact with.
    
    This controller follows the MVC pattern, where it acts as the intermediary
    between the Model (services) and the View (UI components).
    """
    
    file_list_updated_signal = Signal(list)
    metadata_updated_signal = Signal(dict)
    cover_art_updated_signal = Signal(object)
    status_message_updated_signal = Signal(str)
    processing_started_signal = Signal()
    processing_progress_signal = Signal(int)
    processing_finished_signal = Signal(str)
    error_occurred_signal = Signal(str)
    setting_changed = Signal(str, object)
    selected_file_data_changed = Signal(dict) # Signal that emits a dictionary
    selected_file_properties_updated_signal = Signal(dict)  # New signal for file properties
    combined_size_updated_signal = Signal(str)  # New signal for combined size
    
    def __init__(self, settings_manager: SettingsManager):
        """Initialize the MainController with all required services."""
        super().__init__()
        self._logger = logging.getLogger("AudiobookBoss.MainController")
        self._file_service = FileService()
        self._metadata_service = MetadataService()
        self._processing_service = ProcessingService()
        self._settings_manager = settings_manager
        self._path_service = PathService()
        
        self._connect_service_signals()
        self._ensure_default_output_settings()
    
    def _connect_service_signals(self):
        """Connect to signals emitted by services."""
        self._file_service.files_changed.connect(self._on_files_changed)
        self._file_service.combined_size_changed_signal.connect(self.combined_size_updated_signal)
        # Phase 1B: Connect to consolidated metadata signals
        self._metadata_service.metadata_loaded.connect(self._on_metadata_loaded)
        self._metadata_service.metadata_updated.connect(self._on_metadata_updated)
        self._metadata_service.metadata_error.connect(self._on_metadata_error)
        self._processing_service.progress.connect(self.processing_progress_signal)
        self._processing_service.finished.connect(self.processing_finished_signal)
        self._processing_service.error.connect(self.error_occurred_signal)
        self._processing_service.status.connect(self.status_message_updated_signal)
        self._settings_manager.settings_changed.connect(self._on_setting_changed)
    
    def _ensure_default_output_settings(self):
        """Ensure output settings have proper defaults if not already set."""
        defaults = {
            "output_directory": str(Path.home() / "AudiobookBoss_Output"),
            "output_filename_pattern": 0,
            "output_bitrate": 64,
            "output_sample_rate": "auto",  # Default to auto pass-through
            "output_channels": 1,  # 1 = mono
            "output_create_subdirectory": True,
        }
        
        for key, default_value in defaults.items():
            if self._settings_manager.get_setting(key) is None:
                self._settings_manager.set_setting(key, default_value)
    
    def disconnect_service_signals(self):
        """Disconnects signals that were connected in _connect_service_signals().
        This is important for proper cleanup and to prevent signal-slot
        connections from persisting longer than needed, which can lead to
        unexpected behavior or memory leaks.
        """
        # Disconnect FileService signals
        if hasattr(self, '_file_service') and self._file_service:
            try:
                self._file_service.files_changed.disconnect(self._on_files_changed)
            except TypeError: # Signal not connected or already disconnected
                pass
            try:
                self._file_service.combined_size_changed_signal.disconnect(self.combined_size_updated_signal)
            except TypeError:
                pass

        # Disconnect MetadataService signals
        if hasattr(self, '_metadata_service') and self._metadata_service:
            try:
                self._metadata_service.metadata_loaded.disconnect(self._on_metadata_loaded)
            except TypeError:
                pass
            try:
                self._metadata_service.metadata_updated.disconnect(self._on_metadata_updated)
            except TypeError:
                pass
            try:
                self._metadata_service.metadata_error.disconnect(self._on_metadata_error)
            except TypeError:
                pass

        # Disconnect ProcessingService signals
        if hasattr(self, '_processing_service') and self._processing_service:
            try:
                self._processing_service.progress.disconnect(self.processing_progress_signal)
            except TypeError:
                pass
            try:
                self._processing_service.finished.disconnect(self.processing_finished_signal)
            except TypeError:
                pass
            try:
                self._processing_service.error.disconnect(self.error_occurred_signal)
            except TypeError:
                pass
            try:
                self._processing_service.status.disconnect(self.status_message_updated_signal)
            except TypeError:
                pass
        

        # Disconnect SettingsManager signals
        if hasattr(self, '_settings_manager') and self._settings_manager:
            try:
                self._settings_manager.settings_changed.disconnect(self._on_setting_changed)
            except TypeError:
                pass

    def _on_files_changed(self, files: List[str]):
        """Handle files_changed signal from FileService.
        
        Args:
            files: Updated list of files
        """
        self.file_list_updated_signal.emit(files)
        
        # Load metadata from first file if files were added and no metadata exists
        if files and self._metadata_service.current_metadata is None:
            first_file_path = files[0]
            self._metadata_service.extract_and_load_metadata(first_file_path)
    
    def _on_metadata_loaded(self, metadata: Dict[str, Any]):
        """Handle metadata_loaded signal from MetadataService.
        
        Args:
            metadata: Loaded metadata dictionary (may include cover_art_data)
        """
        self.metadata_updated_signal.emit(metadata)
        
        # Handle cover art data if present
        if "cover_art_data" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_data"))
    
    def _on_metadata_updated(self, metadata: Dict[str, Any]):
        """Handle metadata_updated signal from MetadataService.
        
        Args:
            metadata: Updated metadata dictionary (may include cover_art_path)
        """
        self.metadata_updated_signal.emit(metadata)
        
        # Handle cover art path if present
        if "cover_art_path" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_path"))
        # Handle cover art data if present
        elif "cover_art_data" in metadata:
            self.cover_art_updated_signal.emit(metadata.get("cover_art_data"))
    
    def _on_metadata_error(self, error_message: str):
        """Handle metadata_error signal from MetadataService.
        
        Args:
            error_message: Error message from metadata operations
        """
        self.error_occurred_signal.emit(f"Metadata error: {error_message}")
    
    def _on_setting_changed(self, key: str, value: Any):
        """Handle settings_changed signal from SettingsManager.
        
        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        self.setting_changed.emit(key, value)
    
    # File management methods
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the file list.
        
        Args:
            paths: List of file paths to add
            
        Returns:
            List of file paths that were actually added
        """
        if paths:
            self._settings_manager.set_setting("last_input_dir", os.path.dirname(paths[0]))
            # Settings manager auto-saves on update
        return self._file_service.add_files(paths)
    
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        
        Args:
            index: Index of the file to remove
        """
        self._file_service.remove_file(index)
    
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files according to the provided list of file paths.
        
        Args:
            new_order_paths: List of file paths representing the new order
        """
        self._file_service.reorder_files(new_order_paths)
    
    def get_files(self) -> List[str]:
        """Get the current list of files.
        
        Returns:
            List of file paths
        """
        return self._file_service.get_files()
        
    def clear_file_list(self) -> None:
        """Clear all files from the list."""
        current_files = self._file_service.get_files()
        for _ in range(len(current_files)):
            self._file_service.remove_file(0)
    
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata_service.extract_metadata(file_path)
    
    def update_metadata(self, field_key: str, new_value: Any) -> None:
        """Update a metadata field.
        
        Args:
            field_key: Key of the field that changed (raw from UI)
            new_value: New value for the field
        """
        field_key_mapping = {
            "title": "title", "author": "artist", "narrator": "narrator",
            "series": "series", "series_index": "series_position", "year": "year",
            "genre": "genre", "description": "description"
        }
        mapped_key = field_key_mapping.get(field_key, field_key)
        self.update_metadata_field(mapped_key, new_value)
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get a copy of the current metadata.
        
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata_service.get_metadata()

    def update_metadata_field(self, field_name: str, value: str):
        """Update a specific metadata field in the MetadataService.

        Args:
            field_name: The name of the metadata field to update.
            value: The new value for the field.
        """
        self._metadata_service.update_current_metadata(field_name, value)

    def update_cover_art(self, image_path: str):
        """Update the cover art path in the MetadataService.

        Args:
            image_path: The path to the new cover art image.
        """
        self._metadata_service.set_cover_art(image_path)
    
    def start_preview(self, input_file_path: str, metadata: Dict[str, Any], 
                     settings: Dict[str, Any], temp_cover_path: Optional[str] = None, 
                     duration_seconds: int = 30) -> None:
        """Start processing a preview of an audio file.
        
        Args:
            input_file_path: Path to the input audio file
            metadata: Metadata to apply to the preview
            settings: Processing settings
            temp_cover_path: Path to temporary cover art file (optional)
            duration_seconds: Duration of the preview in seconds
        """
        self._processing_service.process_preview(
            input_file_path, metadata, settings, temp_cover_path, duration_seconds
        )
    
    def start_process(self, input_files: List[str], output_path: str, 
                     output_filename: str, metadata: Dict[str, Any], 
                     settings: Dict[str, Any]) -> None:
        """Start processing audio files into a single output file.
        
        Args:
            input_files: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Metadata to apply to the output file
            settings: Processing settings
        """
        self.processing_started_signal.emit()
        self.status_message_updated_signal.emit("Starting processing...")
        self._processing_service.process_full(
            input_files, output_path, output_filename, metadata, settings
        )
    
    def cancel_processing(self) -> None:
        """Cancel the current processing operation."""
        self._processing_service.cancel()

    def calculate_output_path(self) -> str:
        """Calculate output path using path service based on settings and metadata."""
        home_dir = str(Path.home())
        base_dir = self._settings_manager.get_setting("output_directory", home_dir)
        metadata = self._metadata_service.get_metadata()
        use_subdirectory = self._settings_manager.get_setting("output_create_subdirectory", True)
        return self._path_service.calculate_output_path(base_dir, metadata, use_subdirectory)

    def generate_output_filename(self) -> str:
        """Generate output filename using path service based on pattern and metadata."""
        metadata = self._metadata_service.get_metadata()
        pattern_id = self._settings_manager.get_setting("output_filename_pattern", 0)
        return self._path_service.generate_output_filename(metadata, pattern_id)

    def get_output_path(self) -> str:
        """Calculate the output path based on settings and metadata."""
        # This method now calls the already moved internal method.
        return self.calculate_output_path()

    def get_output_filename(self) -> str:
        """Generate output filename based on selected pattern and metadata."""
        # This method now calls the already moved internal method.
        return self.generate_output_filename()

    def configure_audio_defaults(self, file_path: str) -> None:
        """Set default audio settings based on first file properties."""
        # The method 'set_default_bitrate_from_file' was originally in MainWindow
        # and has not been moved to MainController yet.
        # This is a placeholder implementation as per the subtask description.
        if hasattr(self, 'set_default_bitrate_from_file'):
            # This block will not be executed until set_default_bitrate_from_file is part of MainController
            self.set_default_bitrate_from_file(file_path)
        else:
            # TODO: Implement or move set_default_bitrate_from_file to MainController
            # For now, logging a warning as per code review suggestion.
            self._logger.warning(f"Method 'set_default_bitrate_from_file' not found in MainController. Called with: {file_path}")
            # Depending on requirements, this could be a log message or raise NotImplementedError
            pass
    
    # Settings methods
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a setting value by key.
        
        Args:
            key: The setting key to retrieve
            default: Value to return if key doesn't exist
            
        Returns:
            The setting value or default if key doesn't exist
        """
        return self._settings_manager.get_setting(key, default)
    
    def update_setting(self, key: str, value: Any) -> None:
        """Update a setting value.
        
        Args:
            key: The setting key to update
            value: The new value for the setting
        """
        self._settings_manager.set_setting(key, value)

    def update_output_setting(self, setting_name: str, value: Any) -> None:
        """Update a specific output setting.

        This method is intended to be called by the UI when an output setting changes.
        It delegates the update directly to the SettingsManager.

        Args:
            setting_name: The name of the output setting to update.
            value: The new value for the setting.
        """
        self._settings_manager.set_setting(setting_name, value)

    def get_all_output_settings(self) -> Dict[str, Any]:
        """Retrieve all relevant output settings from the SettingsManager.

        This method provides a consolidated dictionary of output settings,
        using default values if a setting is not explicitly found in the
        SettingsManager.

        Returns:
            A dictionary containing all current output settings.
        """
        return {
            "output_directory": self._settings_manager.get_setting("output_directory", str(Path.home() / "AudiobookBoss_Output")),
            "output_filename_pattern": self._settings_manager.get_setting("output_filename_pattern", 0),
            "output_bitrate": self._settings_manager.get_setting("output_bitrate", 64),
            "output_sample_rate": self._settings_manager.get_setting("output_sample_rate", "auto"),  # Default to auto pass-through
            "output_channels": self._settings_manager.get_setting("output_channels", 1),  # 1 = mono
            "output_create_subdirectory": self._settings_manager.get_setting("output_create_subdirectory", True),
        }
        
    def show_settings_dialog(self) -> None:
        """Show the settings dialog.
        
        This method creates and shows a SettingsDialog instance using the 
        current settings and processes the dialog results.
        """
        from ..ui.dialogs.settings_dialog import SettingsDialog
        
        settings = {key: self._settings_manager.get_setting(key) for key in 
                  ["last_input_dir", "last_output_dir", "default_bitrate", 
                   "default_channels", "use_subdirectory_pattern", "filename_pattern"]}
        
        settings_dialog = SettingsDialog(settings)
        
        if settings_dialog.exec_():
            updated_settings = settings_dialog.get_updated_settings()
            for key, value in updated_settings.items():
                self.update_setting(key, value)
                
            self.status_message_updated_signal.emit("Settings updated")
    
    def show_about_dialog(self) -> None:
        """Show the about dialog.
        
        This method creates and shows an AboutDialog instance.
        """
        from ..ui.dialogs.about_dialog import AboutDialog
        
        about_dialog = AboutDialog()
        about_dialog.exec_()
    
    def handle_file_selection_changed(self, selected_files: List[str]) -> None:
        """Handle file selection changes from the UI.
        
        Args:
            selected_files: List of selected file paths
        """
        if selected_files and len(selected_files) == 1:
            filepath = selected_files[0]
            # Extract properties using ffmpeg_utils
            properties, error = get_audio_properties(filepath)
            if error:
                self.selected_file_properties_updated_signal.emit({})
            else:
                self.selected_file_properties_updated_signal.emit(properties)
            # Optionally, also extract metadata as before
            self._metadata_service.extract_metadata(filepath)
            current_metadata = self._metadata_service.get_metadata()
            self.selected_file_data_changed.emit(current_metadata if current_metadata else {})
        else:
            self.selected_file_properties_updated_signal.emit({})
            self.selected_file_data_changed.emit({})
    
    def start_processing(self) -> None:
        """Start processing audiobook files.
        
        This method gathers all necessary data from services and initiates
        the audio processing. It implements Feature 1.7.4 from the plan.
        """
        # Get file list from FileService
        file_list = self._file_service.get_file_list()
        if not file_list:
            self.error_occurred_signal.emit("No files to process")
            self.status_message_updated_signal.emit("Error: No files selected for processing")
            return
        
        # Get metadata from MetadataService
        metadata = self._metadata_service.get_metadata()
        if not metadata:
            metadata = {}
        
        # Get cover art path
        cover_art_path = self._metadata_service.get_cover_art_path()
        
        # Get output settings from SettingsManager
        output_settings = {
            'output_directory': self._settings_manager.get_setting('output_directory'),
            'output_filename_pattern': self._settings_manager.get_setting('output_filename_pattern', 0),
            'use_subdirectory_pattern': self._settings_manager.get_setting('output_create_subdirectory', True),
            'output_bitrate': self._settings_manager.get_setting('output_bitrate', 64),
            'output_channels': self._settings_manager.get_setting('output_channels', 1),
        }
        
        # Handle sample rate - only include if not "auto"
        sample_rate = self._settings_manager.get_setting('output_sample_rate', 'auto')
        if sample_rate != 'auto' and sample_rate:
            output_settings['output_sample_rate'] = sample_rate
        
        # Emit processing started signal
        self.processing_started_signal.emit()
        self.status_message_updated_signal.emit("Starting audio processing...")
        
        # Start processing
        try:
            self._processing_service.start_processing(
                file_list=file_list,
                output_settings=output_settings,
                metadata=metadata,
                cover_art_path=cover_art_path
            )
        except Exception as e:
            self.error_occurred_signal.emit(f"Failed to start processing: {str(e)}")
            self.status_message_updated_signal.emit(f"Error: {str(e)}")