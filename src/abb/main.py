"""Main entry point for Audiobook Boss (ABB).
Bootstraps QApplication and launches MainWindow.
"""

import os
import sys

from PySide6.QtWidgets import QApplication

PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from src.abb.ffmpeg_utils import setup_ffmpeg_path  # noqa: E402
from src.abb.main_window import MainWindow  # noqa: E402


def main() -> None:
    """Application entry point."""
    setup_ffmpeg_path()

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    # The following block for venv check is proposed for removal due to potential issues.
    # venv_python = os.path.join(PROJECT_ROOT, ".venv", "bin", "python")
    # if sys.executable != os.path.realpath(venv_python) and os.path.exists(venv_python):
    #     pass

    main()
