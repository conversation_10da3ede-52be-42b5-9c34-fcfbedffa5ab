"""Validates requirements before starting audio processing."""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Tuple


class ProcessingValidator:
    """Validates all prerequisites for audio processing."""
    
    def __init__(self):
        """Initialize the validator."""
        pass
    
    def validate_all(
        self, 
        input_files: List[Path], 
        output_directory: Path,
        estimated_size_mb: float
    ) -> Tuple[bool, Optional[str]]:
        """Validate all processing requirements.
        
        Args:
            input_files: List of input file paths
            output_directory: Target output directory
            estimated_size_mb: Estimated output size in MB
            
        Returns:
            Tuple of (success, error_message)
        """
        # Check input files
        success, error = self._validate_input_files(input_files)
        if not success:
            return False, error
            
        # Check output directory
        success, error = self._validate_output_directory(output_directory)
        if not success:
            return False, error
            
        # Check disk space
        success, error = self._validate_disk_space(output_directory, estimated_size_mb)
        if not success:
            return False, error
            
        return True, None
    
    def _validate_input_files(self, input_files: List[Path]) -> <PERSON><PERSON>[bool, Optional[str]]:
        """Validate that all input files exist and are readable."""
        if not input_files:
            return False, "No input files provided"
            
        for file_path in input_files:
            if not file_path.exists():
                return False, f"Input file not found: {file_path}"
                
            if not file_path.is_file():
                return False, f"Not a file: {file_path}"
                
            if not os.access(file_path, os.R_OK):
                return False, f"File not readable: {file_path}"
                
        return True, None
    
    def _validate_output_directory(self, output_directory: Path) -> Tuple[bool, Optional[str]]:
        """Validate that output directory exists and is writable."""
        try:
            # Create directory if it doesn't exist
            output_directory.mkdir(parents=True, exist_ok=True)
            
            # Check if writable
            test_file = output_directory / ".write_test"
            try:
                test_file.touch()
                test_file.unlink()
            except Exception:
                return False, f"Output directory not writable: {output_directory}"
                
            return True, None
            
        except Exception as e:
            return False, f"Cannot create output directory: {e}"
    
    def _validate_disk_space(
        self, 
        output_directory: Path, 
        estimated_size_mb: float
    ) -> Tuple[bool, Optional[str]]:
        """Validate sufficient disk space (estimated_size * 1.2)."""
        try:
            # Get disk usage stats
            stat = shutil.disk_usage(output_directory)
            available_mb = stat.free / (1024 * 1024)
            
            # Require 20% buffer
            required_mb = estimated_size_mb * 1.2
            
            if available_mb < required_mb:
                return False, (
                    f"Insufficient disk space. "
                    f"Required: {required_mb:.1f} MB, "
                    f"Available: {available_mb:.1f} MB"
                )
                
            return True, None
            
        except Exception:
            # If we can't check disk space, warn but don't fail
            return True, None