"""Metadata utilities for Audiobook Boss.
Handles extraction of metadata from audio files using mutagen.
"""
from pathlib import Path
from typing import Any, Dict, Optional

import mutagen
from mutagen.id3 import ID3
from mutagen.mp4 import MP4
from PySide6.QtGui import QImage, QPixmap

_metadata_cache = {}


def extract_metadata(file_path: str) -> Dict[str, Any]:
    """Extract metadata from an audio file.
    
    Args:
        file_path: Path to the audio file
        
    Returns:
        Dictionary containing metadata fields
    """
    if file_path in _metadata_cache:
        return _metadata_cache[file_path]
    
    metadata = {
        "title": "",
        "artist": "",  # Author field in UI, maps to TPE1/©ART
        "album": "",
        "narrator": "",
        "year": "",
        "genre": "",
        "series": "",
        "series_position": "",
        "series_sort": "",
        "description": "",
        "cover_art": None,
    }
    
    try:
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == ".mp3":
            metadata = extract_mp3_metadata(file_path, metadata)
        elif file_ext in (".m4a", ".m4b", ".aac"):
            metadata = extract_mp4_metadata(file_path, metadata)
        
        metadata = apply_metadata_defaults(metadata)
        _metadata_cache[file_path] = metadata
        
    except Exception as e:
        print(f"Error extracting metadata from {file_path}: {e}")
    
    return metadata


def extract_mp3_metadata(file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Extract metadata from an MP3 file using ID3 tags.
    
    Args:
        file_path: Path to the MP3 file
        metadata: Existing metadata dictionary to update
        
    Returns:
        Updated metadata dictionary
    """
    try:
        audio = ID3(file_path)
        
        if "TIT2" in audio:
            metadata["title"] = str(audio["TIT2"])
        if "TPE1" in audio:
            metadata["artist"] = str(audio["TPE1"])
        if "TALB" in audio:
            metadata["album"] = str(audio["TALB"])
        if "TPE2" in audio: # Often Album Artist, used for Narrator here
            metadata["narrator"] = str(audio["TPE2"])
        if "TDRC" in audio: # Year
            metadata["year"] = str(audio["TDRC"]).split("-")[0]
        if "TCON" in audio:
            metadata["genre"] = str(audio["TCON"])
        
        # Custom/less common tags often used for audiobooks
        if "TXXX:SERIES" in audio:
            metadata["series"] = str(audio["TXXX:SERIES"])
        if "TXXX:SERIES-PART" in audio:
            metadata["series_position"] = str(audio["TXXX:SERIES-PART"])
        if "TXXX:SERIESSORT" in audio:
            metadata["series_sort"] = str(audio["TXXX:SERIESSORT"])
        if "COMM" in audio: # Comments
            metadata["description"] = str(audio["COMM"])
        
        metadata["cover_art"] = extract_cover_art_from_mp3(audio)
        
    except Exception as e:
        print(f"Error extracting MP3 metadata: {e}")
    
    return metadata


def extract_mp4_metadata(file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Extract metadata from an M4A/M4B/AAC file using MP4 tags.
    
    Args:
        file_path: Path to the M4A/M4B/AAC file
        metadata: Existing metadata dictionary to update
        
    Returns:
        Updated metadata dictionary
    """
    try:
        audio = MP4(file_path)
        
        if "©nam" in audio: # Title
            metadata["title"] = str(audio["©nam"][0])
        if "©ART" in audio: # Artist (Author)
            metadata["artist"] = str(audio["©ART"][0])
        if "©alb" in audio: # Album
            metadata["album"] = str(audio["©alb"][0])
        if "aART" in audio: # Album Artist, used for Narrator here
            metadata["narrator"] = str(audio["aART"][0])
        if "©day" in audio: # Year
            year_str = str(audio["©day"][0])
            metadata["year"] = year_str.split("-")[0] if "-" in year_str else year_str
        if "©gen" in audio: # Genre
            metadata["genre"] = str(audio["©gen"][0])
        
        # MP4 tags often used for audiobook series information
        if "©ser" in audio: # Series
            metadata["series"] = str(audio["©ser"][0])
        if "©pos" in audio: # Series Position (often part of a series)
            metadata["series_position"] = str(audio["©pos"][0])
        if "soaa" in audio: # Sort Album Artist (used for Series Sort here)
            metadata["series_sort"] = str(audio["soaa"][0])
        if "©des" in audio: # Description
            metadata["description"] = str(audio["©des"][0])
        
        metadata["cover_art"] = extract_cover_art_from_mp4(audio)
        
    except Exception as e:
        print(f"Error extracting MP4 metadata: {e}")
    
    return metadata


def extract_cover_art_from_mp3(audio: ID3) -> Optional[QPixmap]:
    """Extract cover art from MP3 ID3 tags.
    
    Args:
        audio: ID3 object
        
    Returns:
        QPixmap of cover art or None if not found
    """
    try:
        # Get all APIC frames (cover art)
        apic_frames = audio.getall("APIC:")
        if apic_frames:
            # Take the first APIC frame found
            apic = apic_frames[0]
            img_data = apic.data
            return create_pixmap_from_data(img_data)
    except Exception as e:
        print(f"Error extracting cover art from MP3: {e}")
    
    return None


def extract_cover_art_from_mp4(audio: MP4) -> Optional[QPixmap]:
    """Extract cover art from MP4 tags.
    
    Args:
        audio: MP4 object
        
    Returns:
        QPixmap of cover art or None if not found
    """
    try:
        if "covr" in audio:
            cover_list = audio["covr"]
            if cover_list: # 'covr' is a list of MP4Cover objects
                img_data = cover_list[0] # data is bytes
                return create_pixmap_from_data(img_data)
    except Exception as e:
        print(f"Error extracting cover art from MP4: {e}")
    
    return None


def create_pixmap_from_data(img_data: bytes) -> Optional[QPixmap]:
    """Create a QPixmap from image data.
    
    Args:
        img_data: Raw image data bytes
        
    Returns:
        QPixmap or None if conversion fails
    """
    try:
        pixmap = QPixmap()
        if pixmap.loadFromData(img_data):
            return pixmap
        
        # Fallback: Try loading via QImage first, then convert to QPixmap
        # This might handle some formats or corrupt data better.
        image = QImage()
        if image.loadFromData(img_data):
            return QPixmap.fromImage(image)
    except Exception as e:
        print(f"Error creating pixmap from image data: {e}")
    
    return None


def apply_metadata_defaults(metadata: Dict[str, Any]) -> Dict[str, Any]:
    """Apply default logic to metadata.
    
    Args:
        metadata: Metadata dictionary
        
    Returns:
        Updated metadata dictionary with defaults applied
    """
    title_value = metadata.get("title")
    if not metadata.get("album") and title_value: # If album is empty, use title
        metadata["album"] = title_value
    
    series_value = metadata.get("series")
    if not metadata.get("series_sort") and series_value: # If series_sort is empty, use series
        metadata["series_sort"] = series_value
    
    return metadata


def clear_metadata_cache() -> None:
    """Clear the metadata cache."""
    global _metadata_cache
    _metadata_cache.clear()


def get_duration(file_path: str) -> float:
    """Get the duration of an audio file in seconds.
    
    Args:
        file_path: Path to the audio file
        
    Returns:
        Duration in seconds or 0 if not available
    """
    try:
        audio = mutagen.File(file_path)
        if audio and hasattr(audio, "info") and hasattr(audio.info, "length"):
            return audio.info.length
    except Exception as e:
        print(f"Error getting duration: {e}")
    
    return 0.0


def calculate_estimated_size(duration: float, bitrate: int) -> int:
    """Calculate estimated output file size based on duration and bitrate.
    
    Args:
        duration: Duration in seconds
        bitrate: Bitrate in kbps
        
    Returns:
        Estimated size in bytes
    """
    return int(duration * bitrate * 1000 / 8) # size in bytes


def extract_tags(filepath: str) -> dict:
    """Extract common metadata tags from an audio file.
    
    Uses mutagen to extract Title, Artist, Album, Genre, Track Number, and Year.
    
    Args:
        filepath: Path to the audio file
        
    Returns:
        Dictionary with tag names as keys. Missing tags have None as value.
    """
    tags = {
        "title": None,
        "artist": None,
        "album": None,
        "genre": None,
        "track_number": None,
        "year": None
    }
    
    try:
        audio = mutagen.File(filepath)
        if audio is None:
            return tags
            
        # Handle different file formats
        file_ext = Path(filepath).suffix.lower()
        
        if file_ext == ".mp3" and hasattr(audio, "tags"):
            # MP3/ID3 tags
            if "TIT2" in audio.tags:
                tags["title"] = str(audio.tags["TIT2"])
            if "TPE1" in audio.tags:
                tags["artist"] = str(audio.tags["TPE1"])
            if "TALB" in audio.tags:
                tags["album"] = str(audio.tags["TALB"])
            if "TCON" in audio.tags:
                tags["genre"] = str(audio.tags["TCON"])
            if "TRCK" in audio.tags:
                tags["track_number"] = str(audio.tags["TRCK"])
            if "TDRC" in audio.tags:
                tags["year"] = str(audio.tags["TDRC"])
                
        elif file_ext in (".m4a", ".m4b", ".aac") and hasattr(audio, "tags"):
            # MP4 tags
            if "©nam" in audio.tags:
                tags["title"] = str(audio.tags["©nam"][0])
            if "©ART" in audio.tags:
                tags["artist"] = str(audio.tags["©ART"][0])
            if "©alb" in audio.tags:
                tags["album"] = str(audio.tags["©alb"][0])
            if "©gen" in audio.tags:
                tags["genre"] = str(audio.tags["©gen"][0])
            if "trkn" in audio.tags:
                # Track number is stored as tuple (current, total)
                track_info = audio.tags["trkn"][0]
                if isinstance(track_info, tuple) and len(track_info) > 0:
                    tags["track_number"] = str(track_info[0])
            if "©day" in audio.tags:
                tags["year"] = str(audio.tags["©day"][0])
                
    except Exception as e:
        print(f"Error extracting tags from {filepath}: {e}")
        
    return tags


def extract_cover(filepath: str) -> Optional[bytes]:
    """Extract embedded cover art from an audio file.
    
    Uses mutagen to extract cover art (e.g., APIC frames for MP3).
    
    Args:
        filepath: Path to the audio file
        
    Returns:
        Image data as bytes if found, otherwise None
    """
    try:
        audio = mutagen.File(filepath)
        if audio is None:
            return None
            
        file_ext = Path(filepath).suffix.lower()
        
        if file_ext == ".mp3":
            # MP3/ID3 - look for APIC frames
            if hasattr(audio, "tags") and audio.tags:
                for key in audio.tags:
                    if key.startswith("APIC:"):
                        apic = audio.tags[key]
                        if hasattr(apic, "data"):
                            return apic.data
                            
        elif file_ext in (".m4a", ".m4b", ".aac"):
            # MP4 - look for covr tag
            if hasattr(audio, "tags") and audio.tags and "covr" in audio.tags:
                cover_list = audio.tags["covr"]
                if cover_list and len(cover_list) > 0:
                    # MP4Cover objects store raw bytes
                    return bytes(cover_list[0])
                    
    except Exception as e:
        print(f"Error extracting cover from {filepath}: {e}")
        
    return None
