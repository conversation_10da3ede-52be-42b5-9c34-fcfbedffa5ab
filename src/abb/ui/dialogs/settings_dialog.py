import logging
from typing import Any, Dict

from PySide6.QtWidgets import (
    QCheckBox,
    QDialog,
    QDialogButtonBox,
    QFileDialog,
    QFormLayout,
    QHBoxLayout,
    QLineEdit,
    QPushButton,
    QTabWidget,
    QVBoxLayout,
    QWidget,
)

logger = logging.getLogger("AudiobookBoss.SettingsDialog")

class SettingsDialog(QDialog):
    """Dialog for configuring application settings."""
    
    def __init__(self, settings: Dict[str, Any], parent=None):
        """Initialize the settings dialog with the current settings.
        
        Args:
            settings: Dictionary containing the current application settings
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Settings")
        self.setMinimumWidth(500)
        
        self.initial_settings = settings.copy() # For comparison on accept/reject if needed
        self.updated_settings = settings.copy() # To store changes made in UI
        
        self._setup_ui()
        self._populate_settings()
        
        logger.debug("Settings dialog initialized")
    
    def _setup_ui(self):
        """Set up the dialog UI with tabs and controls."""
        main_layout = QVBoxLayout(self)
        
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        self._setup_theme_tab()
        self._setup_paths_tab()
        
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def _setup_theme_tab(self):
        """Set up the Theme tab with appearance settings."""
        theme_tab = QWidget()
        layout = QVBoxLayout(theme_tab)
        
        self.dark_mode_checkbox = QCheckBox("Use Dark Mode")
        self.dark_mode_checkbox.setObjectName("dark_mode_checkbox")
        self.dark_mode_checkbox.stateChanged.connect(self._on_dark_mode_changed)
        layout.addWidget(self.dark_mode_checkbox)
        
        layout.addStretch() # Push controls to the top
        
        self.tab_widget.addTab(theme_tab, "Theme")
    
    def _setup_paths_tab(self):
        """Set up the Paths tab with file path settings."""
        paths_tab = QWidget()
        layout = QFormLayout(paths_tab)
        
        ffmpeg_layout = QHBoxLayout()
        self.ffmpeg_path_edit = QLineEdit()
        self.ffmpeg_path_edit.setObjectName("ffmpeg_path_edit")
        self.ffmpeg_path_edit.textChanged.connect(self._on_ffmpeg_path_changed)
        ffmpeg_layout.addWidget(self.ffmpeg_path_edit)
        
        self.ffmpeg_browse_button = QPushButton("Browse...")
        self.ffmpeg_browse_button.setObjectName("ffmpeg_browse_button")
        self.ffmpeg_browse_button.clicked.connect(self._browse_ffmpeg_path)
        ffmpeg_layout.addWidget(self.ffmpeg_browse_button)
        
        layout.addRow("FFmpeg Path:", ffmpeg_layout)
        
        ffprobe_layout = QHBoxLayout()
        self.ffprobe_path_edit = QLineEdit()
        self.ffprobe_path_edit.setObjectName("ffprobe_path_edit")
        self.ffprobe_path_edit.textChanged.connect(self._on_ffprobe_path_changed)
        ffprobe_layout.addWidget(self.ffprobe_path_edit)
        
        self.ffprobe_browse_button = QPushButton("Browse...")
        self.ffprobe_browse_button.setObjectName("ffprobe_browse_button")
        self.ffprobe_browse_button.clicked.connect(self._browse_ffprobe_path)
        ffprobe_layout.addWidget(self.ffprobe_browse_button)
        
        layout.addRow("FFprobe Path:", ffprobe_layout)
        
        self.tab_widget.addTab(paths_tab, "Paths")
    
    def _populate_settings(self):
        """Populate the UI with the current settings."""
        # Example for "dark_mode" which might map to a "theme" setting
        # This assumes "theme":"dark" means dark mode is checked.
        self.dark_mode_checkbox.setChecked(self.initial_settings.get("dark_mode", False))
        
        self.ffmpeg_path_edit.setText(self.initial_settings.get("ffmpeg_path", ""))
        # Assuming "ffprobe_path" is a key that will be in initial_settings if managed
        self.ffprobe_path_edit.setText(self.initial_settings.get("ffprobe_path", ""))
    
    def _on_dark_mode_changed(self, state):
        """Handle dark mode checkbox state changes."""
        # Update the "theme" setting based on dark mode state
        self.updated_settings["dark_mode"] = bool(state)
        self.updated_settings["theme"] = "dark" if bool(state) else "light"
        logger.debug(f"Theme setting changed to: {self.updated_settings['theme']} (dark_mode: {self.updated_settings['dark_mode']})")
    
    def _on_ffmpeg_path_changed(self, path):
        """Handle FFmpeg path changes."""
        self.updated_settings["ffmpeg_path"] = path
        logger.debug(f"FFmpeg path changed to: {path}")
    
    def _on_ffprobe_path_changed(self, path):
        """Handle FFprobe path changes."""
        self.updated_settings["ffprobe_path"] = path
        logger.debug(f"FFprobe path changed to: {path}")
    
    def _browse_ffmpeg_path(self):
        """Open file dialog to browse for FFmpeg executable."""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "Select FFmpeg Executable",
            self.ffmpeg_path_edit.text(),
            "All Files (*)"
        )
        if path:
            self.ffmpeg_path_edit.setText(path)
    
    def _browse_ffprobe_path(self):
        """Open file dialog to browse for FFprobe executable."""
        path, _ = QFileDialog.getOpenFileName(
            self,
            "Select FFprobe Executable",
            self.ffprobe_path_edit.text(),
            "All Files (*)"
        )
        if path:
            self.ffprobe_path_edit.setText(path)
    
    def get_updated_settings(self) -> Dict[str, Any]:
        """Get the updated settings dictionary.
        
        Returns:
            Dictionary containing the updated settings
        """
        return self.updated_settings