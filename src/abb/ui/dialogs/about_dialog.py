import logging

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QDialog, QHBoxLayout, QLabel, QPushButton, QVBoxLayout

logger = logging.getLogger("AudiobookBoss.AboutDialog")

class AboutDialog(QDialog):
    """Dialog displaying information about the application."""
    
    def __init__(self, parent=None):
        """Initialize the about dialog.
        
        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("About Audiobook Boss")
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        
        self._setup_ui()
        
        logger.debug("About dialog initialized")
    
    def _setup_ui(self):
        """Set up the dialog UI with information and close button."""
        main_layout = QVBoxLayout(self)
        main_layout.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel("Audiobook Boss")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        self.version_label = QLabel("Version 1.0.0")
        self.version_label.setObjectName("version_label")
        self.version_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.version_label)
        
        main_layout.addSpacing(20)
        
        self.license_label = QLabel("License: MIT")
        self.license_label.setObjectName("license_label")
        self.license_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.license_label)
        
        credits_text = (
            "Credits:\n"
            "Developed by the Audiobook Boss Team\n"
            "Using PySide6 (Qt for Python)\n"
            "FFmpeg for audio processing"
        )
        self.credits_label = QLabel(credits_text)
        self.credits_label.setObjectName("credits_label")
        self.credits_label.setAlignment(Qt.AlignCenter)
        self.credits_label.setWordWrap(True)
        main_layout.addWidget(self.credits_label)
        
        main_layout.addSpacing(20)
        
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignCenter)
        
        self.close_button = QPushButton("Close")
        self.close_button.setObjectName("close_button")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)