import logging
from pathlib import Path

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QH<PERSON><PERSON><PERSON>ay<PERSON>, QLabel, QListWidget, QPushButton, QVBoxLayout, QWidget

from .dropzone import DropZone

logger = logging.getLogger("AudiobookBoss.LeftPanelWidget")

class LeftPanelWidget(QWidget):
    """Widget for the left panel of the main window.
    Contains a drop zone for files and a list of files.
    """

    # Signals
    files_dropped_signal = Signal(list)
    files_reordered_signal = Signal(list) # New signal for reordered files
    selection_changed_signal = Signal(int)
    request_remove_signal = Signal(int)
    request_move_up_signal = Signal(int)
    request_move_down_signal = Signal(int)
    request_add_files_signal = Signal()
    request_clear_list_signal = Signal()

    def __init__(self, parent=None):
        """Initialize the LeftPanelWidget."""
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create drop zone
        self.drop_zone = DropZone()
        layout.addWidget(self.drop_zone)

        # Create file list widget
        self.file_list_widget = DraggableListWidget(self) # Use custom DraggableListWidget
        self.file_list_widget.setObjectName("file_list_widget")
        layout.addWidget(self.file_list_widget)

        # Create file management buttons
        self._setup_file_management_buttons(layout)

        # Create file properties section
        self._setup_file_properties_ui(layout)

        self.setLayout(layout)

    def _setup_file_management_buttons(self, parent_layout):
        """Set up the file management buttons."""
        # Create a layout for file operations buttons (add/clear)
        file_ops_layout = QHBoxLayout()
        
        # Create add files button
        self.add_files_button = QPushButton("Add Files")
        self.add_files_button.setObjectName("add_files_button")
        file_ops_layout.addWidget(self.add_files_button)
        
        # Create clear list button
        self.clear_list_button = QPushButton("Clear List")
        self.clear_list_button.setObjectName("clear_list_button")
        self.clear_list_button.setEnabled(False)  # Disabled by default until list has items
        file_ops_layout.addWidget(self.clear_list_button)
        
        # Add file operations layout to parent layout
        parent_layout.addLayout(file_ops_layout)
        
        # Create a layout for selection-based buttons
        buttons_layout = QHBoxLayout()

        # Create move up button
        self.move_up_button = QPushButton("Move Up")
        self.move_up_button.setObjectName("move_up_button")
        self.move_up_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.move_up_button)

        # Create move down button
        self.move_down_button = QPushButton("Move Down")
        self.move_down_button.setObjectName("move_down_button")
        self.move_down_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.move_down_button)

        # Create remove button
        self.remove_button = QPushButton("Remove")
        self.remove_button.setObjectName("remove_button")
        self.remove_button.setEnabled(False)  # Disabled by default until a file is selected
        buttons_layout.addWidget(self.remove_button)

        # Add buttons layout to parent layout
        parent_layout.addLayout(buttons_layout)

    def _setup_file_properties_ui(self, parent_layout):
        """Set up the file properties UI components."""
        # Create a group for file properties
        properties_layout = QVBoxLayout()

        # Create labels for each property
        # Duration
        duration_layout = QHBoxLayout()
        duration_label = QLabel("Duration:")
        self.duration_value_label = QLabel("")
        self.duration_value_label.setObjectName("duration_value_label")
        duration_layout.addWidget(duration_label)
        duration_layout.addWidget(self.duration_value_label)
        properties_layout.addLayout(duration_layout)

        # Bitrate
        bitrate_layout = QHBoxLayout()
        bitrate_label = QLabel("Bitrate:")
        self.bitrate_value_label = QLabel("")
        self.bitrate_value_label.setObjectName("bitrate_value_label")
        bitrate_layout.addWidget(bitrate_label)
        bitrate_layout.addWidget(self.bitrate_value_label)
        properties_layout.addLayout(bitrate_layout)

        # Sample Rate
        sample_rate_layout = QHBoxLayout()
        sample_rate_label = QLabel("Sample Rate:")
        self.sample_rate_value_label = QLabel("")
        self.sample_rate_value_label.setObjectName("sample_rate_value_label")
        sample_rate_layout.addWidget(sample_rate_label)
        sample_rate_layout.addWidget(self.sample_rate_value_label)
        properties_layout.addLayout(sample_rate_layout)

        # Channels
        channels_layout = QHBoxLayout()
        channels_label = QLabel("Channels:")
        self.channels_value_label = QLabel("")
        self.channels_value_label.setObjectName("channels_value_label")
        channels_layout.addWidget(channels_label)
        channels_layout.addWidget(self.channels_value_label)
        properties_layout.addLayout(channels_layout)

        # Add properties layout to parent layout
        parent_layout.addLayout(properties_layout)

    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect DropZone's filesDropped signal to our handler
        self.drop_zone.filesDropped.connect(self._on_files_dropped)

        # Connect QListWidget's itemSelectionChanged signal to our handler
        self.file_list_widget.itemSelectionChanged.connect(self._on_selection_changed)

        # Connect button clicked signals to our handlers
        self.add_files_button.clicked.connect(self._on_add_files_button_clicked)
        self.clear_list_button.clicked.connect(self._on_clear_list_button_clicked)
        self.remove_button.clicked.connect(self._on_remove_button_clicked)
        self.move_up_button.clicked.connect(self._on_move_up_button_clicked)
        self.move_down_button.clicked.connect(self._on_move_down_button_clicked)

    def _on_files_dropped(self, file_paths):
        """Handle files dropped on the drop zone."""
        logger.info(f"Files dropped: {len(file_paths)} files")
        # Re-emit the signal
        self.files_dropped_signal.emit(file_paths)

    def update_file_list_display(self, file_paths):
        """Update the file list display with the provided file paths.

        Args:
            file_paths (list): List of file paths to display
        """
        # Clear the list widget
        self.file_list_widget.clear()

        # Add each file path to the list widget
        for path in file_paths:
            # Extract just the filename from the path for display
            filename = Path(path).name
            self.file_list_widget.addItem(filename)
            
        # Enable/disable clear list button based on whether there are items
        self.clear_list_button.setEnabled(len(file_paths) > 0)

        logger.info(f"Updated file list display with {len(file_paths)} files")

    def _on_selection_changed(self):
        """Handle selection changed in the file list widget."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        
        # Check if there are any items in the list
        has_items = self.file_list_widget.count() > 0
        
        # Enable/disable the clear list button based on whether there are items
        self.clear_list_button.setEnabled(has_items)

        # Only emit the signal if a valid row is selected
        if current_row >= 0:
            logger.info(f"Selection changed to row {current_row}")
            self.selection_changed_signal.emit(current_row)

            # Enable the buttons when an item is selected
            self.remove_button.setEnabled(True)

            # Enable/disable move up/down buttons based on position
            total_items = self.file_list_widget.count()
            self.move_up_button.setEnabled(current_row > 0)
            self.move_down_button.setEnabled(current_row < total_items - 1)
        else:
            # Disable selection-dependent buttons when no item is selected
            self.remove_button.setEnabled(False)
            self.move_up_button.setEnabled(False)
            self.move_down_button.setEnabled(False)

    def _on_remove_button_clicked(self):
        """Handle remove button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()

        # Only emit the signal if a valid row is selected
        if current_row >= 0:
            logger.info(f"Remove button clicked for row {current_row}")
            self.request_remove_signal.emit(current_row)

    def _on_move_up_button_clicked(self):
        """Handle move up button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()

        # Only emit the signal if a valid row is selected and it's not the first item
        if current_row > 0:
            logger.info(f"Move up button clicked for row {current_row}")
            self.request_move_up_signal.emit(current_row)

    def _on_move_down_button_clicked(self):
        """Handle move down button clicked."""
        # Get the current row index
        current_row = self.file_list_widget.currentRow()
        total_items = self.file_list_widget.count()

        # Only emit the signal if a valid row is selected and it's not the last item
        if current_row >= 0 and current_row < total_items - 1:
            logger.info(f"Move down button clicked for row {current_row}")
            self.request_move_down_signal.emit(current_row)
            
    def _on_add_files_button_clicked(self):
        """Handle add files button clicked."""
        logger.info("Add files button clicked")
        self.request_add_files_signal.emit()
        
    def _on_clear_list_button_clicked(self):
        """Handle clear list button clicked."""
        # Only emit the signal if there are files in the list
        if self.file_list_widget.count() > 0:
            logger.info("Clear list button clicked")
            self.request_clear_list_signal.emit()

    def update_selected_file_properties_display(self, file_properties):
        """Update the file properties display with the provided properties.

        Args:
            file_properties (dict): Dictionary of file properties to display
        """
        # Update each label with the corresponding property value
        if "duration" in file_properties:
            self.duration_value_label.setText(file_properties["duration"])

        if "bitrate" in file_properties:
            self.bitrate_value_label.setText(file_properties["bitrate"])

        if "sample_rate" in file_properties:
            self.sample_rate_value_label.setText(file_properties["sample_rate"])

        if "channels" in file_properties:
            self.channels_value_label.setText(file_properties["channels"])

        logger.info("Updated file properties display")

    def _handle_files_reordered(self):
        """Handles the reordering of files in the list widget and emits the signal.
        This method is called by DraggableListWidget after a drop event.
        """
        new_order_paths = []
        for i in range(self.file_list_widget.count()):
            new_order_paths.append(self.file_list_widget.item(i).text())
        self.files_reordered_signal.emit(new_order_paths)
        logger.info(f"Files reordered. New order: {new_order_paths}")

class DraggableListWidget(QListWidget):
    """Custom QListWidget to handle drag and drop reordering and emit a signal.
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.InternalMove)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.SingleSelection)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)

    def dropEvent(self, event):
        """Handle the drop event for reordering items.
        """
        super().dropEvent(event)
        # After the superclass handles the drop, notify the parent LeftPanelWidget
        # to emit the reordered signal.
        if isinstance(self.parent(), LeftPanelWidget):
            self.parent()._handle_files_reordered()

