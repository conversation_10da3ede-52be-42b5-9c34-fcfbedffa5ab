import logging

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QForm<PERSON>ayout, QHBoxLayout, QLineEdit, QWidget

from .coverart import CoverArtWidget

logger = logging.getLogger("AudiobookBoss.MetadataFormWidget")

class MetadataFormWidget(QWidget):
    """Widget for editing metadata of audio files.
    Contains form fields for various metadata properties and a cover art widget.
    """

    # Signals
    metadata_field_changed = Signal(str, str)  # field_key, new_value
    cover_art_changed_signal = Signal(str)  # new_image_path

    def __init__(self, parent=None):
        """Initialize the MetadataFormWidget."""
        super().__init__(parent)
        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Create cover art widget
        self.cover_art = CoverArtWidget()
        main_layout.addWidget(self.cover_art)

        # Create form layout for metadata fields
        form_layout = QFormLayout()

        # Create metadata fields
        self.title_edit = QLineEdit()
        self.title_edit.setObjectName("title_edit")
        form_layout.addRow("Title:", self.title_edit)

        self.artist_edit = QLineEdit()
        self.artist_edit.setObjectName("artist_edit")
        form_layout.addRow("Artist:", self.artist_edit)

        self.author_edit = QLineEdit()
        self.author_edit.setObjectName("author_edit")
        form_layout.addRow("Author:", self.author_edit)

        self.album_edit = QLineEdit()
        self.album_edit.setObjectName("album_edit")
        form_layout.addRow("Album:", self.album_edit)

        self.narrator_edit = QLineEdit()
        self.narrator_edit.setObjectName("narrator_edit")
        form_layout.addRow("Narrator:", self.narrator_edit)

        self.series_edit = QLineEdit()
        self.series_edit.setObjectName("series_edit")
        form_layout.addRow("Series:", self.series_edit)

        self.series_index_edit = QLineEdit()
        self.series_index_edit.setObjectName("series_index_edit")
        form_layout.addRow("Series Index:", self.series_index_edit)

        self.track_number_edit = QLineEdit()
        self.track_number_edit.setObjectName("track_number_edit")
        form_layout.addRow("Track Number:", self.track_number_edit)

        self.year_edit = QLineEdit()
        self.year_edit.setObjectName("year_edit")
        form_layout.addRow("Year:", self.year_edit)

        self.genre_edit = QLineEdit()
        self.genre_edit.setObjectName("genre_edit")
        form_layout.addRow("Genre:", self.genre_edit)

        self.description_edit = QLineEdit()
        self.description_edit.setObjectName("description_edit")
        form_layout.addRow("Description:", self.description_edit)

        # Add form layout to main layout
        main_layout.addLayout(form_layout)

        self.setLayout(main_layout)

    def populate_fields(self, metadata):
        """Populate the form fields with the provided metadata.

        Args:
            metadata (dict): Dictionary containing metadata values
        """
        # Set text fields
        if "title" in metadata:
            self.title_edit.setText(metadata["title"])

        if "artist" in metadata:
            self.artist_edit.setText(metadata["artist"])

        if "author" in metadata:
            self.author_edit.setText(metadata["author"])

        if "album" in metadata:
            self.album_edit.setText(metadata["album"])

        if "narrator" in metadata:
            self.narrator_edit.setText(metadata["narrator"])

        if "series" in metadata:
            self.series_edit.setText(metadata["series"])

        if "series_index" in metadata:
            self.series_index_edit.setText(metadata["series_index"])

        if "track" in metadata:
            self.track_number_edit.setText(metadata["track"])

        if "year" in metadata:
            self.year_edit.setText(metadata["year"])

        if "genre" in metadata:
            self.genre_edit.setText(metadata["genre"])

        if "description" in metadata:
            self.description_edit.setText(metadata["description"])

        # Set cover art if provided
        if "cover_art" in metadata and metadata["cover_art"]:
            self.cover_art.set_cover_art_from_pixmap(metadata["cover_art"])

        logger.info("Populated metadata form fields")

    def get_form_data(self):
        """Get the values from the form fields as a dictionary.

        Returns:
            dict: Dictionary containing the form field values
        """
        # Create a dictionary with the form field values
        form_data = {
            "title": self.title_edit.text(),
            "artist": self.artist_edit.text(),
            "author": self.author_edit.text(),
            "album": self.album_edit.text(),
            "narrator": self.narrator_edit.text(),
            "series": self.series_edit.text(),
            "series_index": self.series_index_edit.text(),
            "track": self.track_number_edit.text(),
            "year": self.year_edit.text(),
            "genre": self.genre_edit.text(),
            "description": self.description_edit.text(),
            "cover_art": self.cover_art.get_cover_art()
        }

        logger.info("Retrieved metadata form field values")
        return form_data

    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect form field textChanged signals to handler
        self.title_edit.editingFinished.connect(lambda: self._on_field_changed("title", self.title_edit.text()))
        self.artist_edit.editingFinished.connect(lambda: self._on_field_changed("artist", self.artist_edit.text()))
        self.author_edit.editingFinished.connect(lambda: self._on_field_changed("author", self.author_edit.text()))
        self.album_edit.editingFinished.connect(lambda: self._on_field_changed("album", self.album_edit.text()))
        self.narrator_edit.editingFinished.connect(lambda: self._on_field_changed("narrator", self.narrator_edit.text()))
        self.series_edit.editingFinished.connect(lambda: self._on_field_changed("series", self.series_edit.text()))
        self.series_index_edit.editingFinished.connect(lambda: self._on_field_changed("series_index", self.series_index_edit.text()))
        self.track_number_edit.editingFinished.connect(lambda: self._on_field_changed("track", self.track_number_edit.text()))
        self.year_edit.editingFinished.connect(lambda: self._on_field_changed("year", self.year_edit.text()))
        self.genre_edit.editingFinished.connect(lambda: self._on_field_changed("genre", self.genre_edit.text()))
        self.description_edit.editingFinished.connect(lambda: self._on_field_changed("description", self.description_edit.text()))
        
        # Connect cover art changed signals from CoverArtWidget
        self.cover_art.coverArtChanged.connect(self._on_cover_art_changed)
        self.cover_art.cover_art_path_changed.connect(self._on_cover_art_path_changed)

    def _on_field_changed(self, field_key, new_value):
        """Handle field value changed.

        Args:
            field_key (str): Key of the field that changed
            new_value (str): New value of the field
        """
        logger.info(f"Metadata field '{field_key}' changed to '{new_value}'")
        self.metadata_field_changed.emit(field_key, new_value)
        
    def _on_cover_art_changed(self, pixmap):
        """Handle cover art changed.

        Args:
            pixmap (QPixmap): New cover art pixmap
        """
        logger.info("Cover art pixmap changed")
        
    def _on_cover_art_path_changed(self, file_path):
        """Handle cover art path changed.

        Args:
            file_path (str): Path to the new cover art file
        """
        logger.info(f"Cover art path changed to: {file_path}")
        # Emit the cover_art_changed_signal with the file path
        self.cover_art_changed_signal.emit(file_path)
    
    def update_metadata_display(self, metadata_dict: dict):
        """Update the metadata form fields with values from the provided dictionary.
        
        This slot is connected to MainController.metadata_updated_signal and
        populates the form fields using the metadata dictionary.
        
        Args:
            metadata_dict: Dictionary containing metadata fields
        """
        # Update each field, using get() to handle missing tags gracefully
        self.title_edit.setText(metadata_dict.get('title', ''))
        self.artist_edit.setText(metadata_dict.get('artist', ''))
        self.album_edit.setText(metadata_dict.get('album', ''))
        self.genre_edit.setText(metadata_dict.get('genre', ''))
        self.track_number_edit.setText(metadata_dict.get('track', ''))
        self.year_edit.setText(metadata_dict.get('date', ''))
        
        # Also update author field if artist is provided (for compatibility)
        if 'artist' in metadata_dict:
            self.author_edit.setText(metadata_dict.get('artist', ''))
        
        logger.info("Updated metadata display from signal")
