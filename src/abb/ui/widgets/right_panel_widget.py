import logging

from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QProgressBar,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

from .metadata_form_widget import MetadataFormWidget
from .output_settings_widget import OutputSettingsWidget

logger = logging.getLogger("AudiobookBoss.RightPanelWidget")

class RightPanelWidget(QWidget):
    """Widget for the right panel of the main window.
    Contains metadata form, output settings, and processing controls.
    """

    # Signals
    metadata_field_changed = Signal(str, str)  # field_key, new_value
    setting_changed = Signal(str, object)  # setting_key, new_value
    start_processing_requested = Signal()
    cancel_processing_requested = Signal()
    cover_art_changed_signal = Signal(str)  # image_path

    def __init__(self, main_controller, parent=None):
        """Initialize the RightPanelWidget."""
        super().__init__(parent)
        self.main_controller = main_controller
        self._setup_ui()
        self._connect_signals()

    def _setup_ui(self):
        """Set up the UI components."""
        # Create main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        # Add panel title
        title_label = QLabel("Metadata & Output")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title_label)

        # Create metadata section
        self._setup_metadata_section(layout)

        # Create output settings section
        self._setup_output_section(layout)

        # Create processing controls
        self._setup_processing_controls(layout)

        self.setLayout(layout)

    def _setup_metadata_section(self, parent_layout):
        """Set up the metadata section using MetadataFormWidget."""
        # Create metadata group box
        metadata_group = QGroupBox("Metadata")
        metadata_layout = QVBoxLayout(metadata_group)

        # Create MetadataFormWidget
        self.metadata_form_widget = MetadataFormWidget()

        # Add to layout
        metadata_layout.addWidget(self.metadata_form_widget)

        # Add metadata group to parent layout
        parent_layout.addWidget(metadata_group)

    def _setup_output_section(self, parent_layout):
        """Set up the output settings section using OutputSettingsWidget."""
        # Create output group box
        output_group = QGroupBox("Output Settings")
        output_layout = QVBoxLayout(output_group)

        # Create OutputSettingsWidget
        self.output_settings_widget = OutputSettingsWidget()

        # Add to layout
        output_layout.addWidget(self.output_settings_widget)

        # Add output group to parent layout
        parent_layout.addWidget(output_group)

    def _setup_processing_controls(self, parent_layout):
        """Set up the processing controls (progress bar, process/cancel buttons)."""
        # Add estimated size section
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("Estimated Output Size:"))
        self.estimated_size_label = QLabel("—")
        self.estimated_size_label.setStyleSheet(
            "font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;"
        )
        size_layout.addWidget(self.estimated_size_label)
        size_layout.addStretch(1)
        parent_layout.addLayout(size_layout)

        # Add progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress_bar")
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)  # Initially hidden
        parent_layout.addWidget(self.progress_bar)

        # Add Process button
        process_layout = QHBoxLayout()
        self.process_button = QPushButton("Process Audiobook")
        self.process_button.setObjectName("process_button")
        self.process_button.setMinimumHeight(40)  # Make button more prominent

        self.cancel_button = QPushButton("Cancel Processing")
        self.cancel_button.setObjectName("cancel_button")
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setEnabled(False)  # Initially disabled

        process_layout.addStretch(1)
        process_layout.addWidget(self.process_button)
        process_layout.addWidget(self.cancel_button)
        process_layout.addStretch(1)
        parent_layout.addLayout(process_layout)

    def _connect_signals(self):
        """Connect signals to slots."""
        # Connect MetadataFormWidget signals
        self.metadata_form_widget.metadata_field_changed.connect(self._forward_metadata_field_changed)
        self.metadata_form_widget.cover_art_changed_signal.connect(self._forward_cover_art_changed)

        # Connect RightPanelWidget signals to MainController
        self.metadata_field_changed.connect(self.main_controller.update_metadata_field)
        self.cover_art_changed_signal.connect(self.main_controller.update_cover_art)

        # Connect OutputSettingsWidget signals
        self.output_settings_widget.setting_changed_signal.connect(self._forward_setting_changed)
        # Note: OutputSettingsWidget handles browse directory internally

        # Connect button signals
        self.process_button.clicked.connect(self._on_process_button_clicked)
        self.cancel_button.clicked.connect(self._on_cancel_button_clicked)

    def _forward_metadata_field_changed(self, field_key, new_value):
        """Forward metadata_field_changed signal from MetadataFormWidget."""
        logger.info(f"Forwarding metadata field changed: {field_key} = {new_value}")
        self.metadata_field_changed.emit(field_key, new_value)

    def _forward_setting_changed(self, setting_key, new_value):
        """Forward setting_changed signal from OutputSettingsWidget."""
        logger.info(f"Forwarding setting changed: {setting_key} = {new_value}")
        self.setting_changed.emit(setting_key, new_value)

    # _forward_browse_requested method removed as OutputSettingsWidget handles browse internally

    def _forward_cover_art_changed(self, image_path: str):
        """Forward cover_art_changed_signal from MetadataFormWidget."""
        logger.info(f"Forwarding cover art changed: {image_path}")
        self.cover_art_changed_signal.emit(image_path)

    def _on_process_button_clicked(self):
        """Handle process button clicked."""
        logger.info("Process button clicked")
        self.start_processing_requested.emit()

    def _on_cancel_button_clicked(self):
        """Handle cancel button clicked."""
        logger.info("Cancel button clicked")
        self.cancel_processing_requested.emit()

    def update_progress(self, value):
        """Update the progress bar value and make it visible.
        
        Args:
            value (int): Progress value (0-100)
        """
        self.progress_bar.setValue(value)
        self.progress_bar.setVisible(True)
        logger.info(f"Progress updated to {value}%")

    def reset_progress(self):
        """Reset the progress bar to 0 and hide it."""
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        logger.info("Progress reset")

    def set_processing_state(self, is_processing):
        """Set the processing state, updating button enabled states.
        
        Args:
            is_processing (bool): True if processing is active, False otherwise
        """
        self.process_button.setEnabled(not is_processing)
        self.cancel_button.setEnabled(is_processing)
        logger.info(f"Processing state set to {is_processing}")

    def set_estimated_size(self, size_text):
        """Set the estimated output size label text.
        
        Args:
            size_text (str): Size text to display
        """
        self.estimated_size_label.setText(size_text)
        logger.info(f"Estimated size set to {size_text}")

    def populate_metadata(self, metadata):
        """Populate the metadata form with the provided metadata.
        
        Args:
            metadata (dict): Dictionary containing metadata values
        """
        self.metadata_form_widget.populate_fields(metadata)
        logger.info("Metadata populated")

    def get_metadata(self):
        """Get the metadata from the form.
        
        Returns:
            dict: Dictionary containing the metadata
        """
        return self.metadata_form_widget.get_form_data()

    def populate_settings(self, settings):
        """Populate the output settings with the provided settings.
        
        Args:
            settings (dict): Dictionary containing settings values
        """
        self.output_settings_widget.populate_settings(settings)
        logger.info("Settings populated")

    def get_settings(self):
        """Get the settings from the output settings widget.
        
        Returns:
            dict: Dictionary containing the settings
        """
        return self.output_settings_widget.get_settings_data()

    def set_sample_rate(self, sample_rate):
        """Set the sample rate in the output settings widget.
        
        Args:
            sample_rate (str): Sample rate to display
        """
        self.output_settings_widget.set_sample_rate(sample_rate)
    
    def set_use_subdirectory(self, checked):
        """Set the subdirectory checkbox state.
        
        Args:
            checked (bool): Whether to use subdirectory
        """
        if hasattr(self.output_settings_widget, 'use_subdir_checkbox'):
            self.output_settings_widget.use_subdir_checkbox.setChecked(checked)
        else:
            # Handle the case where the checkbox might have a different name
            self.output_settings_widget.subdir_checkbox.setChecked(checked)
    
    def set_output_directory(self, directory):
        """Set the output directory in the output settings widget.
        
        Args:
            directory (str): Path to the output directory
        """
        self.output_settings_widget.output_dir_edit.setText(directory)
    
    def set_metadata_form_enabled(self, enabled):
        """Enable or disable the metadata form widget.
        
        Args:
            enabled (bool): Whether to enable the widget
        """
        self.metadata_form_widget.setEnabled(enabled)
    
    def set_output_settings_enabled(self, enabled):
        """Enable or disable the output settings widget.
        
        Args:
            enabled (bool): Whether to enable the widget
        """
        self.output_settings_widget.setEnabled(enabled)