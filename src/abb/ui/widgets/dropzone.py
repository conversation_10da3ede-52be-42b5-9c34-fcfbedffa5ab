"""DropZone widget for Audiobook Boss.
Provides a drag-and-drop area for audio files with click-to-select functionality.
"""

from pathlib import Path
from typing import List, Set

from PySide6.QtCore import QMimeData, QSettings, Qt, Signal
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QMouseEvent
from PySide6.QtWidgets import QFileDialog, QFrame, QLabel


class DropZone(QLabel):
    """Custom drag-and-drop area for audio files.
    Accepts .mp3, .m4a, .m4b, and .aac files.
    """

    # Signal emitted when files are dropped or selected
    filesDropped = Signal(list)

    # Supported file extensions
    SUPPORTED_EXTENSIONS = {".mp3", ".m4a", ".m4b", ".aac"}

    def __init__(self, text: str = "Drag & Drop files here or Click to Select") -> None:
        """Initialize the DropZone widget."""
        super().__init__(text)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFrameStyle(QFrame.Shape.StyledPanel | QFrame.Shadow.Raised)
        self.setStyleSheet(
            "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px; background-color: #fafafa;"
        )
        self.setAcceptDrops(True)
        self.setObjectName("dropZoneLabel")

        # Add a secondary line for supported formats
        self.setText(f"{text}\n<small>Supports: mp3, m4a, m4b, aac</small>")

        # Settings for remembering last directory
        self.settings = QSettings("AudiobookBoss", "ABB")

    def dragEnterEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag enter events, accepting only if URLs are present."""
        if event.mimeData().hasUrls():
            # Check if at least one file has a supported extension
            if self._has_supported_files(event.mimeData()):
                self.setStyleSheet(
                    "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
                    "font-size: 13px; padding: 24px; background-color: #f0f0f0;"
                )
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def dragLeaveEvent(self, event) -> None:
        """Reset styling when drag leaves the widget."""
        self.setStyleSheet(
            "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px; background-color: #fafafa;"
        )
        super().dragLeaveEvent(event)

    def dropEvent(self, event: QDropEvent) -> None:
        """Handle drop events, processing valid audio files."""
        if event.mimeData().hasUrls():
            file_paths = self._process_urls(event.mimeData())
            if file_paths:
                self.filesDropped.emit(file_paths)

            # Reset styling
            self.setStyleSheet(
                "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
                "font-size: 13px; padding: 24px; background-color: #fafafa;"
            )
            event.acceptProposedAction()
        else:
            event.ignore()

    def mouseReleaseEvent(self, event: QMouseEvent) -> None:
        """Handle mouse click to open file dialog."""
        if event.button() == Qt.MouseButton.LeftButton:
            # Get the last used directory or default to home
            last_dir = self.settings.value("last_input_dir", str(Path.home()), type=str)

            # Open file dialog
            paths, _ = QFileDialog.getOpenFileNames(
                self,
                "Select Audio Files",
                last_dir,
                "Audio Files (*.mp3 *.m4a *.m4b *.aac)",
            )

            if paths:
                # Save the directory for next time
                new_dir = str(Path(paths[0]).parent)
                self.settings.setValue("last_input_dir", new_dir)

                # Process and emit the file paths
                file_paths = self._normalize_paths(paths)
                self.filesDropped.emit(file_paths)

    def _has_supported_files(self, mime_data: QMimeData) -> bool:
        """Check if the mime data contains at least one supported file."""
        for url in mime_data.urls():
            file_path = url.toLocalFile()
            if file_path:
                suffix = Path(file_path).suffix.lower()
                if suffix in self.SUPPORTED_EXTENSIONS:
                    return True
        return False

    def _process_urls(self, mime_data: QMimeData) -> List[str]:
        """Process URLs from mime data, filtering for supported audio files."""
        file_paths = []

        for url in mime_data.urls():
            file_path = url.toLocalFile()
            if file_path:
                path = Path(file_path)
                if path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                    file_paths.append(str(path))

        return self._normalize_paths(file_paths)

    def _normalize_paths(self, paths: List[str]) -> List[str]:
        """Normalize paths and remove duplicates while preserving order."""
        # Initialize empty list for ordered unique paths and a set for tracking seen paths
        ordered_unique_paths: List[str] = []
        seen_paths: Set[str] = set()

        # Iterate through paths in original order
        for path in paths:
            # Resolve to absolute path
            abs_path_str = str(Path(path).resolve())

            # Add to ordered list only if not seen before
            if abs_path_str not in seen_paths:
                ordered_unique_paths.append(abs_path_str)
                seen_paths.add(abs_path_str)

        return ordered_unique_paths
