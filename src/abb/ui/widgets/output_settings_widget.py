from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFileDialog,
    QFormLayout,
    QHBoxLayout,
    QLineEdit,
    QPushButton,
    QVBoxLayout,
    QWidget,
)


class OutputSettingsWidget(QWidget):
    setting_changed_signal = Signal(str, object)

    def __init__(self, app_state=None, initial_settings=None, parent=None):
        super().__init__(parent)
        # Support both app_state and initial_settings for backward compatibility
        if app_state is not None and isinstance(app_state, dict):
            self.initial_settings = app_state.get("settings", {})
        else:
            self.initial_settings = initial_settings if initial_settings is not None else {}
        
        self._init_ui()
        self._load_initial_values()

    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        # Bitrate
        self.bitrate_combo = QComboBox()
        self.bitrate_combo.addItems([str(x) for x in [32, 48, 56, 64, 96, 128]])
        form_layout.addRow("Bitrate:", self.bitrate_combo)
        self.bitrate_combo.currentTextChanged.connect(
            lambda text: self._on_setting_changed("output_bitrate", int(text))
        )

        # Sample Rate
        self.samplerate_combo = QComboBox()
        self.samplerate_combo.addItems(["Auto (Pass-through)", "22050", "32000", "44100", "48000"])
        form_layout.addRow("Sample Rate:", self.samplerate_combo)
        self.samplerate_combo.currentTextChanged.connect(self._on_sample_rate_changed)

        # Channels
        self.channels_combo = QComboBox()
        self.channels_combo.addItems(["Mono", "Stereo"])
        form_layout.addRow("Channels:", self.channels_combo)
        self.channels_combo.currentTextChanged.connect(self._on_channels_changed)

        # Output Directory
        output_dir_layout = QHBoxLayout()
        self.output_dir_edit = QLineEdit()
        self.output_dir_button = QPushButton("Browse...")
        output_dir_layout.addWidget(self.output_dir_edit)
        output_dir_layout.addWidget(self.output_dir_button)
        form_layout.addRow("Output Directory:", output_dir_layout)
        self.output_dir_edit.textChanged.connect(
            lambda text: self._on_setting_changed("output_directory", text)
        )
        self.output_dir_button.clicked.connect(self._on_browse_output_dir)

        # Filename Pattern
        self.filename_pattern_edit = QLineEdit()
        form_layout.addRow("Filename Pattern:", self.filename_pattern_edit)
        self.filename_pattern_edit.textChanged.connect(
            lambda text: self._on_setting_changed("output_filename_pattern", text)
        )

        # Create Subdirectory
        self.subdir_checkbox = QCheckBox("Create subdirectory for each audiobook")
        form_layout.addRow("", self.subdir_checkbox)
        self.subdir_checkbox.stateChanged.connect(
            lambda state: self._on_setting_changed("output_create_subdirectory", bool(state))
        )

        main_layout.addLayout(form_layout)
        main_layout.addStretch(1) # Push content to the top

    def _load_initial_values(self):
        # Disconnect signals temporarily to prevent emitting signals during initial load
        self.bitrate_combo.blockSignals(True)
        self.samplerate_combo.blockSignals(True)
        self.channels_combo.blockSignals(True)
        self.output_dir_edit.blockSignals(True)
        self.filename_pattern_edit.blockSignals(True)
        self.subdir_checkbox.blockSignals(True)

        self.bitrate_combo.setCurrentText(str(self.initial_settings.get("output_bitrate", 64)))
        
        sample_rate_value = self.initial_settings.get("output_sample_rate", "auto")
        # Set the sample rate value
        if sample_rate_value == "auto" or sample_rate_value is None:
            self.samplerate_combo.setCurrentText("Auto (Pass-through)")
        else:
            # Convert to string and set
            rate_str = str(sample_rate_value)
            if rate_str in ["22050", "32000", "44100", "48000"]:
                self.samplerate_combo.setCurrentText(rate_str)
            else:
                # If it's a non-standard rate, add it to the combo
                self.samplerate_combo.addItem(rate_str)
                self.samplerate_combo.setCurrentText(rate_str)
        
        channels_value = self.initial_settings.get("output_channels", 1)
        self.channels_combo.setCurrentText("Stereo" if channels_value == 2 else "Mono")
        
        self.output_dir_edit.setText(self.initial_settings.get("output_directory", ""))
        self.filename_pattern_edit.setText(str(self.initial_settings.get("output_filename_pattern", "0")))
        self.subdir_checkbox.setChecked(self.initial_settings.get("output_create_subdirectory", False))

        # Reconnect signals
        self.bitrate_combo.blockSignals(False)
        self.samplerate_combo.blockSignals(False)
        self.channels_combo.blockSignals(False)
        self.output_dir_edit.blockSignals(False)
        self.filename_pattern_edit.blockSignals(False)
        self.subdir_checkbox.blockSignals(False)

    def _on_setting_changed(self, setting_name, new_value):
        if setting_name == "output_filename_pattern":
            try:
                new_value = int(new_value)
            except ValueError:
                # If conversion to int fails, emit the raw string and let SettingsManager handle validation
                pass
        self.setting_changed_signal.emit(setting_name, new_value)

    def _on_channels_changed(self, text):
        int_value = 2 if text == "Stereo" else 1
        self._on_setting_changed("output_channels", int_value)
    
    def _on_sample_rate_changed(self, text):
        """Handle sample rate combo box changes."""
        if text == "Auto (Pass-through)":
            # Use None or "auto" to indicate pass-through mode
            self._on_setting_changed("output_sample_rate", "auto")
        else:
            # Convert to integer for specific sample rates
            self._on_setting_changed("output_sample_rate", int(text))
    
    def set_sample_rate(self, sample_rate):
        """Set the sample rate value.
        
        Args:
            sample_rate: The sample rate in Hz (int) or "auto" for pass-through
        """
        # Block signals to prevent double emission
        self.samplerate_combo.blockSignals(True)
        
        if sample_rate == "auto" or sample_rate is None:
            self.samplerate_combo.setCurrentText("Auto (Pass-through)")
        else:
            rate_str = str(sample_rate)
            # Check if rate is in combo box
            if rate_str not in [self.samplerate_combo.itemText(i) for i in range(self.samplerate_combo.count())]:
                # Add non-standard sample rate
                self.samplerate_combo.addItem(rate_str)
            self.samplerate_combo.setCurrentText(rate_str)
        
        self.samplerate_combo.blockSignals(False)
        # Emit the setting changed signal
        self._on_setting_changed("output_sample_rate", sample_rate)

    def _on_browse_output_dir(self):
        selected_dir = QFileDialog.getExistingDirectory(self, "Select Output Directory")
        if selected_dir:
            self.output_dir_edit.setText(selected_dir) # This will trigger textChanged and emit signal
    
    def populate_settings(self, settings):
        """Populate the output settings with the provided settings.
        
        Args:
            settings (dict): Dictionary containing settings values
        """
        # Update initial settings and reload values
        self.initial_settings = settings
        self._load_initial_values()