"""CoverArtWidget for Audiobook Boss.
Provides a drag-and-drop area for cover art images with click-to-select functionality.
"""

from pathlib import Path
from typing import Optional

from PySide6.QtCore import QSettings, Qt, Signal
from PySide6.QtGui import (
    QDragEnterEvent,
    QDropEvent,
    QMouseEvent,
    QPixmap,
)
from PySide6.QtWidgets import (
    QFileDialog,
    QLabel,
    QPushButton,
    QVBoxLayout,
    QWidget,
)

MAX_PREVIEW_WIDTH = 180
MAX_PREVIEW_HEIGHT = 180
DEFAULT_PREVIEW_WIDTH = 100
DEFAULT_PREVIEW_HEIGHT = 100

class CoverArtWidget(QWidget):
    """Widget for displaying and managing cover art.
    Accepts drag-drop of image files and provides a button to load images.
    """

    # Signals emitted when cover art is changed
    coverArtChanged = Signal(QPixmap)
    # Additional signal that includes the image path for better integration
    cover_art_path_changed = Signal(str)

    # Supported file extensions
    SUPPORTED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".tiff", ".tif"}

    def __init__(self, parent=None) -> None:
        """Initialize the CoverArtWidget."""
        super().__init__(parent)

        # Create layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(8)
        self.setAcceptDrops(True) # Enable drag and drop for the widget itself

        # Create image display label
        self.image_label = QLabel("Drag Image Here")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)
        self.image_label.setAutoFillBackground(True)  # Ensure background is painted
        self.image_label.setStyleSheet(
            """
            QLabel {
                border: 1px solid #505050;  /* Darker, solid border */
                border-radius: 6px; 
                color: #6b7280; /* Text color for 'Drag Image Here' */
                font-size: 13px; 
                background-color: #404040; /* Dark gray background */
            }
            """
        )
        self.image_label.setAcceptDrops(True)

        # Create load button
        self.load_button = QPushButton("Load Cover Art")

        # Add widgets to layout
        self.layout.addWidget(self.image_label)
        self.layout.addWidget(self.load_button)

        # Connect signals
        self.load_button.clicked.connect(self.load_cover_art)

        # Settings for remembering last directory
        self.settings = QSettings("AudiobookBoss", "ABB")

        # Current pixmap
        self.current_pixmap = None


    def load_cover_art(self) -> None:
        """Open file dialog to select cover art image."""
        # Get the last used directory or default to home
        last_dir = self.settings.value("last_cover_dir", str(Path.home()), type=str)

        # Open file dialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Cover Art Image",
            last_dir,
            "Image Files (*.jpg *.jpeg *.png *.tiff *.tif)",
        )

        if file_path:
            # Save the directory for next time
            new_dir = str(Path(file_path).parent)
            self.settings.setValue("last_cover_dir", new_dir)

            # Set the cover art
            self.set_cover_art(file_path)

    def set_cover_art(self, file_path: str) -> None:
        """Set the cover art from a file path."""
        # Add null check to prevent TypeError
        if file_path is None:
            print("Error loading cover art: file_path is None")
            return
            
        try:
            print(f"Attempting to load cover art from: {file_path}")

            # Check file extension
            file_ext = Path(file_path).suffix.lower()
            print(f"File extension: {file_ext}")
            if file_ext not in [".jpg", ".jpeg", ".png", ".tiff", ".tif"]:
                self.image_label.setText("Unsupported format\nOnly JPG, PNG, TIFF")
                return

            # Check file size (8 MB limit)
            file_size_mb = Path(file_path).stat().st_size / (1024 * 1024)
            print(f"File size: {file_size_mb:.2f} MB")
            if file_size_mb > 8:
                self.image_label.setText("File too large\nMax 8MB")
                return

            # Load the image
            print("Loading image into QPixmap...")
            original_pixmap = QPixmap(file_path)
            if original_pixmap.isNull():
                self.image_label.setText("Failed to load image")
                return
            print(
                f"Image loaded successfully. Dimensions: {original_pixmap.width()}x{original_pixmap.height()}"
            )

            # Check original dimensions before any transformation
            width = original_pixmap.width()
            height = original_pixmap.height()

            # Log aspect ratio information for original image
            if width != height:
                print(f"Warning: Image is not 1:1 aspect ratio ({width}x{height})")

            # Check maximum size for original image
            if width > 4000 or height > 4000:
                self.image_label.setText("Too large\nMax 4000x4000")
                return

            print("Initial validation checks passed.")

            # Store the original validated pixmap
            self.current_pixmap = original_pixmap

            # Scale the original pixmap for UI preview to fit max dimensions
            preview_display_pixmap = original_pixmap.scaled(
                MAX_PREVIEW_WIDTH,
                MAX_PREVIEW_HEIGHT,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            
            # Set the image_label to the exact size of the scaled pixmap
            self.image_label.setFixedSize(preview_display_pixmap.size())
            self.image_label.setPixmap(preview_display_pixmap)
            self.image_label.setText("")  # Clear any previous text

            # Emit signals with the original validated pixmap and the file path
            print("Emitting coverArtChanged signals with original pixmap and file path...")
            self.coverArtChanged.emit(original_pixmap)
            self.cover_art_path_changed.emit(file_path)
            print("Cover art loading and UI preview updated.")

        except Exception as e:
            print(f"Error loading cover art: {e}")
            import traceback

            traceback.print_exc()
            self.image_label.setText("Error loading image")

    def get_cover_art(self) -> Optional[QPixmap]:
        """Return the current cover art pixmap."""
        return self.current_pixmap

    def clear_cover_art(self) -> None:
        """Clear the current cover art."""
        self.image_label.setPixmap(QPixmap())  # Clear the image
        self.image_label.setText("Drag Image Here") # Restore text
        # Reset label to default size when cleared
        self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)
        self.current_pixmap = None

    def set_cover_art_from_pixmap(self, pixmap: QPixmap) -> None:
        """Set the cover art from a QPixmap."""
        if pixmap and not pixmap.isNull():
            original_pixmap = pixmap  # Use this name for clarity

            # Check original dimensions before any transformation
            width = original_pixmap.width()
            height = original_pixmap.height()

            # Log aspect ratio information for original image
            if width != height:
                print(f"Warning: Image is not 1:1 aspect ratio ({width}x{height})")

            # Check maximum size for original image
            if width > 4000 or height > 4000:
                self.current_pixmap = None
                self.image_label.setPixmap(QPixmap())  # Clear image_label display
                self.image_label.setText("Too large\nMax 4000x4000")  # Set error message
                self.image_label.setFixedSize(DEFAULT_PREVIEW_WIDTH, DEFAULT_PREVIEW_HEIGHT)  # Reset to default size
                self.coverArtChanged.emit(QPixmap())  # Emit empty on failure
                return

            print("Initial validation checks passed for pixmap.")

            # Store the original validated pixmap
            self.current_pixmap = original_pixmap

            # Scale the original pixmap for UI preview to fit max dimensions
            preview_display_pixmap = original_pixmap.scaled(
                MAX_PREVIEW_WIDTH,
                MAX_PREVIEW_HEIGHT,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )

            # Set the image_label to the exact size of the scaled pixmap
            self.image_label.setFixedSize(preview_display_pixmap.size())
            self.image_label.setPixmap(preview_display_pixmap)
            self.image_label.setText("")  # Clear text

            # Emit signal with the original validated pixmap
            self.coverArtChanged.emit(original_pixmap)
        else:
            # Handle null or invalid input pixmap
            self.clear_cover_art()  # Clears current_pixmap and UI
            self.coverArtChanged.emit(QPixmap())
    
    def dragEnterEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag enter events for image files."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = Path(url.toLocalFile())
            if file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def dragMoveEvent(self, event: QDragEnterEvent) -> None:
        """Handle drag move events."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = Path(url.toLocalFile())
            if file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()

    def dropEvent(self, event: QDropEvent) -> None:
        """Handle drop events for image files."""
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            file_path = url.toLocalFile()
            self.set_cover_art(file_path)
            event.acceptProposedAction()
        else:
            event.ignore()

    def mousePressEvent(self, event: QMouseEvent) -> None:
        """Handle mouse press events to open file dialog."""
        if event.button() == Qt.MouseButton.LeftButton:
            self.load_cover_art()
        super().mousePressEvent(event) # Call base class implementation
    
    def update_cover_art_display(self, image_data: Optional[bytes]):
        """Update the cover art display with image data.
        
        This slot is connected to MainController.cover_art_updated_signal and
        displays the cover art from the provided image data bytes.
        
        Args:
            image_data: Cover art image data as bytes, QPixmap, or None
        """
        if image_data is not None:
            # Check if it's already a QPixmap
            if isinstance(image_data, QPixmap):
                # Directly use the pixmap
                self.set_cover_art_from_pixmap(image_data)
            else:
                # Assume it's bytes data
                # 1. Memory Bounds Checking
                MAX_IMAGE_DATA_SIZE = 50 * 1024 * 1024  # 50 MB
                if len(image_data) > MAX_IMAGE_DATA_SIZE:
                    self.clear_cover_art()
                    self.image_label.setText("Image too large (max 50MB)")
                    return

                # Create a QPixmap and load data
                pixmap = QPixmap()
                if pixmap.loadFromData(image_data):
                    # Use the existing method to handle display and validation
                    self.set_cover_art_from_pixmap(pixmap)
                else:
                    # 2. Consistent Error State Management: Failed to load image data
                    self.clear_cover_art() # Ensure clear_cover_art is called
                    self.image_label.setText("Invalid image data")
        else:
            # No image data, clear the display
            self.clear_cover_art()
