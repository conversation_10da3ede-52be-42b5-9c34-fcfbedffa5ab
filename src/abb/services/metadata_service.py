"""MetadataService for Audiobook Boss.

Handles metadata extraction, application of defaults, and updates.
"""
from typing import Any, Dict, Optional

from PySide6.QtCore import QObject, Signal

from ..metadata_utils import apply_metadata_defaults, extract_cover, extract_metadata, extract_tags
from .unified_metadata_handler import UnifiedMetadataHandler


class MetadataService(QObject):
    """Service for managing audio metadata.
    
    Handles operations like extracting metadata from files, applying defaults,
    and updating metadata fields. Emits signals when metadata changes.
    """
    
    # Phase 1B: Consolidated signals as per spec - exactly 3 signals
    metadata_loaded = Signal(dict)  # For file loading operations
    metadata_updated = Signal(dict)  # For metadata changes and updates  
    metadata_error = Signal(str)    # For error handling
    
    def __init__(self):
        """Initialize the MetadataService with empty metadata."""
        super().__init__()
        self._metadata = {}
        self.current_metadata: Optional[dict] = None
        self.current_cover_art_data: Optional[bytes] = None
        self.current_cover_art_path: Optional[str] = None
        self._unified_handler = UnifiedMetadataHandler()
    
    def get_metadata(self) -> Dict[str, Any]:
        """Get a copy of the current metadata.
        
        Returns:
            Dictionary containing metadata fields
        """
        return self._metadata.copy()
    
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary containing metadata fields
        """
        if UnifiedMetadataHandler.is_enabled():
            metadata = self._unified_handler.load_from_file(file_path)
        else:
            metadata = extract_metadata(file_path) # From metadata_utils
        
        self._metadata = metadata
        self.metadata_loaded.emit(self._metadata.copy())
        return metadata
    
    def apply_defaults(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Apply default values to metadata.
        
        Args:
            metadata: Metadata dictionary to apply defaults to
            
        Returns:
            Updated metadata dictionary with defaults applied
        """
        if UnifiedMetadataHandler.is_enabled():
            # Update handler's state with provided metadata and apply defaults
            self._unified_handler._current_metadata = metadata.copy()
            self._unified_handler.apply_defaults()
            updated_metadata = self._unified_handler.get_current_metadata()
        else:
            updated_metadata = apply_metadata_defaults(metadata) # From metadata_utils
        
        self._metadata = updated_metadata
        self.metadata_updated.emit(self._metadata.copy())
        return updated_metadata
    
    def update_metadata_field(self, field: str, value: Any) -> None:
        """Update a metadata field.
        
        Args:
            field: Name of the field to update
            value: New value for the field
        """
        if self._metadata.get(field) == value: # Handles field not existing too
            return # No change, or field doesn't exist and value is None (or default)
        
        self._metadata[field] = value
        self.metadata_updated.emit(self._metadata.copy())
    
    def clear_metadata(self) -> None:
        """Clear all metadata."""
        if not self._metadata: # Already empty
            return
        
        self._metadata = {}
        self.metadata_updated.emit(self._metadata.copy())
    
    def extract_and_load_metadata(self, filepath: str) -> None:
        """Extract metadata and cover art from a file and emit signals.
        
        This method extracts tags and cover art from the given file,
        stores them internally, and emits the appropriate signals.
        
        Args:
            filepath: Path to the audio file
        """
        if UnifiedMetadataHandler.is_enabled():
            # Extract tags using the unified handler
            self.current_metadata = self._unified_handler.extract_tags_only(filepath)
            
            # Extract cover art using the unified handler
            self.current_cover_art_data = self._unified_handler.extract_cover_art(filepath)
        else:
            # Extract tags using the original function
            self.current_metadata = extract_tags(filepath)
            
            # Extract cover art using the original function
            self.current_cover_art_data = extract_cover(filepath)
        
        # Emit consolidated signal with both metadata and cover art data
        metadata_with_cover = self.current_metadata.copy() if self.current_metadata else {}
        if self.current_cover_art_data is not None:
            metadata_with_cover['cover_art_data'] = self.current_cover_art_data
        self.metadata_loaded.emit(metadata_with_cover)

    def update_current_metadata(self, field_name: str, new_value: str):
        """Update the service's internal current_metadata dictionary and emit a signal.

        Args:
            field_name: The name of the metadata field to update.
            new_value: The new value for the specified field.
        """
        if self.current_metadata is None:
            self.current_metadata = {}
        self.current_metadata[field_name] = new_value
        self.metadata_updated.emit(self.current_metadata)

    def set_cover_art(self, image_path: str):
        """Update the internal current_cover_art_path and emit a signal.

        Args:
            image_path: The path to the new cover art image.
        """
        self.current_cover_art_path = image_path
        # Emit metadata update with cover art path
        if self.current_metadata is None:
            self.current_metadata = {}
        updated_metadata = self.current_metadata.copy()
        updated_metadata['cover_art_path'] = self.current_cover_art_path
        self.metadata_updated.emit(updated_metadata)
    
    def get_cover_art_path(self) -> Optional[str]:
        """Get the current cover art path.
        
        Returns:
            Optional[str]: Path to cover art image or None
        """
        return self.current_cover_art_path