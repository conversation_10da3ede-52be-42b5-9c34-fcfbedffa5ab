"""ProcessingService for Audiobook Boss.

Manages audio processing operations using QProcess.
"""

import os
from typing import Any, Dict, List, Optional

from PySide6.QtCore import QObject, QThread, Signal

from src.abb.ffmpeg_utils import _get_executable_path
from src.abb.metadata_utils import get_duration
from src.abb.processing_validator import ProcessingValidator
from src.abb.processing_worker import ProcessingWorker
from src.abb.services.path_service import PathService


class ProcessingService(QObject):
    """Service for managing audio processing operations.
    
    Uses QProcess to run FFmpeg commands for processing audiobooks.
    Provides signals for progress updates, completion, and errors.
    """
    
    progress = Signal(int)
    finished = Signal(str)
    error = Signal(str)
    status = Signal(str)
    
    def __init__(self):
        """Initialize the ProcessingService."""
        super().__init__()
        
        ffmpeg_path = self._get_ffmpeg_path()
        
        self._worker = ProcessingWorker(ffmpeg_path)
        self._worker_thread = QThread()
        self._validator = ProcessingValidator()
        self._path_service = PathService()
        
        self._worker.progress_updated.connect(self._on_worker_progress)
        self._worker.status.connect(self._on_worker_status)
        self._worker.processing_finished.connect(self._on_worker_finished)
        self._worker.processing_error.connect(self._on_worker_error)
        
        self._worker_thread.finished.connect(self._worker.deleteLater)
        self._worker_thread.finished.connect(self._worker_thread.deleteLater)
        
        self._output_file = "" # Used to hold the output file path for the 'finished' signal if needed.
    
    def process_full(self,
                    input_files: List[str],
                    output_path: str,
                    output_filename: str,
                    metadata: Dict[str, Any],
                    settings: Dict[str, Any]) -> None:
        """Process a full audiobook.
        
        Args:
            input_files: List of input audio file paths
            output_path: Directory to save the output file
            output_filename: Name of the output file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
        """
        os.makedirs(output_path, exist_ok=True)
        output_file_full_path = os.path.join(output_path, output_filename)
        self._output_file = output_file_full_path # Storing for potential use, though worker passes path back
        
        total_duration = sum(get_duration(file_path) for file_path in input_files)
        
        if not self._worker_thread.isRunning():
            self._worker.moveToThread(self._worker_thread)
            self._worker_thread.start()
        
        self.status.emit("Starting audio processing...")
        self._worker.start_full_process(
            input_files,
            output_path,
            output_filename,
            metadata,
            settings,
            total_duration
        )
    
    def start_processing(self,
                        file_list: List[str],
                        output_settings: Dict[str, Any],
                        metadata: Dict[str, Any],
                        cover_art_path: Optional[str] = None) -> None:
        """Start audio processing with validation.
        
        This is the main entry point for Feature 1.7 that includes:
        - Pre-processing validation
        - Output path generation
        - Metadata preparation
        - Delegating to process_full
        
        Args:
            file_list: List of input audio file paths
            output_settings: Dictionary containing:
                - output_directory: Base output directory
                - output_filename_pattern: 0 or 1 for filename pattern
                - use_subdirectory_pattern: Whether to create subdirs
                - output_bitrate: Bitrate in kbps
                - output_channels: 1 for mono, 2 for stereo
                - output_sample_rate: Sample rate or None for auto
            metadata: Dictionary of metadata to embed
            cover_art_path: Optional path to cover art image
        """
        from pathlib import Path
        
        # Convert to Path objects for validation
        input_paths = [Path(f) for f in file_list]
        
        # Calculate estimated size
        total_duration = sum(get_duration(str(f)) for f in input_paths)
        bitrate_kbps = output_settings.get('output_bitrate', 64)
        estimated_size_mb = (total_duration * bitrate_kbps * 1000 / 8) / (1024 * 1024)
        
        # Generate output path
        output_dir = self._path_service.calculate_output_path(
            base_directory=output_settings['output_directory'],
            metadata=metadata,
            use_subdirectory=output_settings.get('use_subdirectory_pattern', True)
        )
        
        # Validate before processing
        valid, error_msg = self._validator.validate_all(
            input_files=input_paths,
            output_directory=Path(output_dir),
            estimated_size_mb=estimated_size_mb
        )
        
        if not valid:
            self.error.emit(f"Validation failed: {error_msg}")
            self.status.emit(f"Cannot start processing: {error_msg}")
            return
        
        # Generate filename
        pattern = output_settings.get('output_filename_pattern', 0)
        output_filename = self._path_service.generate_output_filename(
            metadata=metadata,
            pattern=pattern
        )
        
        # Prepare metadata with cover art
        processing_metadata = metadata.copy()
        if cover_art_path:
            processing_metadata['cover_art_temp_path'] = cover_art_path
        
        # Prepare audio settings
        audio_settings = {
            'bitrate': output_settings.get('output_bitrate', 64),
            'channels': output_settings.get('output_channels', 1),
        }
        # Only include sample rate if specified
        if output_settings.get('output_sample_rate'):
            audio_settings['sample_rate'] = output_settings['output_sample_rate']
        
        # Delegate to existing process_full
        self.process_full(
            input_files=file_list,
            output_path=output_dir,
            output_filename=output_filename,
            metadata=processing_metadata,
            settings=audio_settings
        )
    
    def process_preview(self,
                       input_file_path: str,
                       metadata: Dict[str, Any],
                       settings: Dict[str, Any],
                       temp_cover_path: Optional[str] = None,
                       duration_seconds: int = 30) -> None:
        """Generate a preview of an audiobook.
        
        Args:
            input_file_path: Path to the input audio file
            metadata: Dictionary of metadata to embed
            settings: Dictionary of audio settings (bitrate, channels, etc.)
            temp_cover_path: Optional path to a cover art image
            duration_seconds: Duration of the preview in seconds (default: 30)
        """
        if not self._worker_thread.isRunning():
            self._worker.moveToThread(self._worker_thread)
            self._worker_thread.start()
        
        self.status.emit("Generating preview...")
        self._worker.start_preview_process(
            input_file_path,
            metadata,
            settings,
            temp_cover_path,
            duration_seconds
        )
    
    def cancel(self) -> None:
        """Cancel the current processing operation."""
        self.status.emit("Cancelling processing...")
        self._worker.cancel()
        
        if self._worker_thread.isRunning():
            self._worker_thread.quit()
            self._worker_thread.wait(5000) 
        
        self.status.emit("Processing cancelled") # This might be premature if worker is still cleaning up
    
    def cleanup_worker_resources(self) -> None:
        """Clean up worker thread resources.
        
        Ensures the worker thread is properly shut down before the service is destroyed.
        This prevents the "QThread: Destroyed while thread is still running" error.
        """
        if hasattr(self, '_worker_thread') and self._worker_thread is not None:
            if self._worker_thread.isRunning():
                self._worker_thread.quit()
                if not self._worker_thread.wait(5000): # Wait up to 5 seconds
                    self._worker_thread.terminate() # Force terminate if not finished
                    self._worker_thread.wait(1000) # Wait after terminate
            
            self._worker = None # Allow GC
            self._worker_thread = None # Allow GC
    
    def _get_ffmpeg_path(self) -> str:
        """Get the path to the FFmpeg executable.
        
        Returns:
            Path to the FFmpeg executable
        """
        ffmpeg_path = _get_executable_path("ffmpeg")
        return str(ffmpeg_path) if ffmpeg_path else "ffmpeg" # Default to "ffmpeg" relies on PATH
    
    # Slots for worker signals
    def _on_worker_progress(self, percentage: int) -> None:
        """Relay progress updates from the worker."""
        self.progress.emit(percentage)
    
    def _on_worker_status(self, message: str) -> None:
        """Relay status updates from the worker."""
        self.status.emit(message)
    
    def _on_worker_finished(self, output_path: str) -> None:
        """Relay successful completion from the worker."""
        self.status.emit("Processing completed successfully")
        self.finished.emit(output_path)
    
    def _on_worker_error(self, error_message: str) -> None:
        """Relay errors from the worker."""
        self.status.emit(f"Processing failed: {error_message}")
        self.error.emit(error_message)