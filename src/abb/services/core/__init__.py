"""Pure Python core service implementations.

This module contains Qt-independent service implementations that use callback-based
communication instead of Qt signals. These services can be used directly in non-Qt
environments or wrapped with Qt adapters for GUI applications.

Services implemented:
- FileServiceCore: File management operations
- PathServiceCore: Path and filename operations  
- SettingsManagerCore: Settings persistence and management
- MetadataHandlerCore: Metadata extraction and manipulation
- ProcessingServiceCore: Audio processing operations
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .file_service_core import FileServiceCore
    from .metadata_handler_core import MetadataHandlerCore
    from .path_service_core import PathServiceCore
    from .processing_service_core import ProcessingServiceCore
    from .settings_manager_core import SettingsManagerCore

__all__ = [
    "FileServiceCore",
    "PathServiceCore", 
    "SettingsManagerCore",
    "MetadataHandlerCore",
    "ProcessingServiceCore",
]