"""ProcessingServiceCore - Pure Python processing service without Qt dependencies.

Implements the IProcessingService protocol using callback-based communication,
Python threading, and subprocess instead of Qt signals, QThread, and QProcess.
"""

import os
import re
import subprocess
import threading
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

from ...ffmpeg_utils import (
    _get_executable_path,
    build_ffmpeg_command,
    build_ffmpeg_preview_command,
)
from ...metadata_utils import get_duration
from ...processing_validator import ProcessingValidator
from ..interfaces import IPathService


class ProcessingServiceCore:
    """Pure Python processing service without Qt dependencies.

    Implements IProcessingService protocol using callback-based communication
    instead of Qt signals, and Python threading/subprocess instead of Qt equivalents.
    """

    def __init__(self, path_service: IPathService):
        """Initialize the ProcessingServiceCore.

        Args:
            path_service: Path service dependency for output path calculations
        """
        self._path_service = path_service
        self._validator = ProcessingValidator()

        # Threading state
        self._lock = threading.Lock()
        self._is_processing = False
        self._cancel_event = threading.Event()
        self._process: Optional[subprocess.Popen] = None
        self._processing_thread: Optional[threading.Thread] = None
        self._progress_thread: Optional[threading.Thread] = None

        # Processing state
        self._temp_files: List[str] = []
        self._output_file: Optional[str] = None
        self._total_duration_seconds = 0.0

        # Callbacks for current operation
        self._progress_callback: Optional[Callable[[int], None]] = None
        self._status_callback: Optional[Callable[[str], None]] = None
        self._complete_callback: Optional[Callable[[str], None]] = None
        self._error_callback: Optional[Callable[[str], None]] = None

        # Get FFmpeg path once
        self._ffmpeg_path = self._get_ffmpeg_path()

    def process(
        self,
        input_files: List[str],
        output_path: str,
        output_filename: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        progress_callback: Callable[[int], None],
        status_callback: Callable[[str], None],
        complete_callback: Callable[[str], None],
        error_callback: Callable[[str], None],
    ) -> None:
        """Process audio files into audiobook format.

        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
            progress_callback: Called with progress percentage (0-100)
            status_callback: Called with status messages
            complete_callback: Called with output file path when complete
            error_callback: Called with error message if processing fails
        """
        with self._lock:
            if self._is_processing:
                error_callback("Processing already in progress")
                return

            self._is_processing = True
            self._cancel_event.clear()

        # Store callbacks
        self._progress_callback = progress_callback
        self._status_callback = status_callback
        self._complete_callback = complete_callback
        self._error_callback = error_callback

        # Start processing in background thread
        self._processing_thread = threading.Thread(
            target=self._process_impl,
            args=(input_files, output_path, output_filename, metadata, settings),
            daemon=True,
        )
        self._processing_thread.start()

    def process_preview(
        self,
        input_file: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        duration_seconds: int,
        complete_callback: Callable[[str], None],
        error_callback: Callable[[str], None],
    ) -> None:
        """Generate a preview of the processed audio.

        Args:
            input_file: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            duration_seconds: Length of preview in seconds
            complete_callback: Called with preview file path
            error_callback: Called with error message if operation fails
        """
        with self._lock:
            if self._is_processing:
                error_callback("Processing already in progress")
                return

            self._is_processing = True
            self._cancel_event.clear()

        # Store callbacks (no progress for preview)
        self._progress_callback = None
        self._status_callback = lambda msg: None  # No-op for preview
        self._complete_callback = complete_callback
        self._error_callback = error_callback

        # Start preview processing in background thread
        self._processing_thread = threading.Thread(
            target=self._process_preview_impl,
            args=(input_file, metadata, settings, duration_seconds),
            daemon=True,
        )
        self._processing_thread.start()

    def cancel(
        self,
        success_callback: Callable[[], None],
        error_callback: Optional[Callable[[str], None]] = None,
    ) -> None:
        """Cancel current processing operation.

        Args:
            success_callback: Called when cancellation is complete
            error_callback: Called with error message if cancellation fails
        """
        with self._lock:
            if not self._is_processing:
                success_callback()
                return

        # Set cancel event
        self._cancel_event.set()

        # Start cancellation in background thread to avoid blocking
        cancel_thread = threading.Thread(
            target=self._cancel_impl, args=(success_callback, error_callback), daemon=True
        )
        cancel_thread.start()

    def is_processing(self) -> bool:
        """Check if processing is currently active.

        Returns:
            True if processing is active, False otherwise.
        """
        with self._lock:
            return self._is_processing

    def _process_impl(
        self,
        input_files: List[str],
        output_path: str,
        output_filename: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
    ) -> None:
        """Implementation of full processing in background thread."""
        try:
            self._temp_files = []

            # Validate FFmpeg
            if not self._ffmpeg_path or not os.path.isfile(self._ffmpeg_path):
                self._emit_error("FFmpeg executable not found or not executable")
                return

            # Calculate total duration
            self._total_duration_seconds = sum(get_duration(file_path) for file_path in input_files)

            # Validate inputs
            input_paths = [Path(f) for f in input_files]
            bitrate_kbps = settings.get("bitrate", 64)
            estimated_size_mb = (self._total_duration_seconds * bitrate_kbps * 1000 / 8) / (
                1024 * 1024
            )

            valid, error_msg = self._validator.validate_all(
                input_files=input_paths,
                output_directory=Path(output_path),
                estimated_size_mb=estimated_size_mb,
            )

            if not valid:
                self._emit_error(f"Validation failed: {error_msg}")
                return

            # Handle cover art
            cover_art_temp_path = metadata.get("cover_art_temp_path")
            if cover_art_temp_path and os.path.exists(cover_art_temp_path):
                self._temp_files.append(cover_art_temp_path)

            # Create output directory
            os.makedirs(output_path, exist_ok=True)
            self._output_file = os.path.join(output_path, output_filename)

            # Build FFmpeg command
            ffprobe_exe_path = _get_executable_path("ffprobe")
            cmd = build_ffmpeg_command(
                input_files=input_files,
                output_file_full_path=self._output_file,
                metadata=metadata,
                settings=settings,
                ffmpeg_exe_path=self._ffmpeg_path,
                ffprobe_exe_path=ffprobe_exe_path,
            )

            if not cmd:
                self._emit_error("Failed to build FFmpeg command. Check input files and settings.")
                return

            # Run FFmpeg process
            self._emit_status("Starting audio processing...")
            self._run_ffmpeg_process(cmd)

        except Exception as e:
            self._emit_error(f"Processing error: {str(e)}")
        finally:
            self._cleanup_processing()

    def _process_preview_impl(
        self,
        input_file: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        duration_seconds: int,
    ) -> None:
        """Implementation of preview processing in background thread."""
        try:
            self._temp_files = []
            self._total_duration_seconds = duration_seconds

            # Build preview command
            cmd = build_ffmpeg_preview_command(
                input_file_path=input_file,
                metadata=metadata,
                settings=settings,
                temp_cover_path=metadata.get("cover_art_temp_path"),
                duration_seconds=duration_seconds,
            )

            if not cmd:
                self._emit_error("Failed to build FFmpeg preview command")
                return

            # Output file is last argument
            self._output_file = cmd[-1]
            self._temp_files.append(self._output_file)  # Preview output is temporary

            # Run FFmpeg process
            self._emit_status("Generating preview...")
            self._run_ffmpeg_process(cmd)

        except Exception as e:
            self._emit_error(f"Preview processing error: {str(e)}")
        finally:
            self._cleanup_processing()

    def _run_ffmpeg_process(self, cmd: List[str]) -> None:
        """Run FFmpeg process and monitor progress."""
        try:
            # Start process
            self._process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1,
            )

            # Start progress monitoring thread
            if self._progress_callback:
                self._progress_thread = threading.Thread(target=self._monitor_progress, daemon=True)
                self._progress_thread.start()

            # Wait for process completion
            stdout, stderr = self._process.communicate()

            # Check for cancellation
            if self._cancel_event.is_set():
                self._cleanup_after_cancel()
                return

            # Check exit code
            if self._process.returncode == 0:
                # Success
                if self._progress_callback:
                    self._progress_callback(100)
                self._emit_status("Processing completed successfully")
                self._cleanup_temp_files_only()
                if self._complete_callback:
                    self._complete_callback(self._output_file or "")
            else:
                # Error
                error_msg = f"FFmpeg failed (exit code {self._process.returncode})"
                if stderr.strip():
                    error_msg += f": {stderr.strip()}"
                self._emit_error(error_msg)

        except Exception as e:
            self._emit_error(f"Error running FFmpeg process: {str(e)}")

    def _monitor_progress(self) -> None:
        """Monitor FFmpeg stderr output for progress updates."""
        if not self._process or not self._progress_callback:
            return

        try:
            while self._process.poll() is None and not self._cancel_event.is_set():
                if self._process.stderr:
                    line = self._process.stderr.readline()
                    if line:
                        line = line.strip()

                        # Parse progress
                        progress = self._parse_ffmpeg_progress(line)
                        if progress is not None:
                            self._progress_callback(progress)

                        # Emit status for all lines
                        if self._status_callback:
                            self._status_callback(line)

                # Brief sleep to avoid busy waiting
                time.sleep(0.1)

        except Exception as e:
            if self._status_callback:
                self._status_callback(f"Progress monitoring error: {str(e)}")

    def _parse_ffmpeg_progress(self, stderr_line: str) -> Optional[int]:
        """Parse FFmpeg stderr output to extract progress information.

        Args:
            stderr_line: FFmpeg stderr output line

        Returns:
            Progress percentage (0-100) or None if no progress info found
        """
        match = re.search(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})", stderr_line)
        if match:
            hours, minutes, seconds = int(match.group(1)), int(match.group(2)), int(match.group(3))
            current_time_seconds = (hours * 3600) + (minutes * 60) + seconds

            if self._total_duration_seconds > 0:
                percentage = (current_time_seconds / self._total_duration_seconds) * 100
                # Clamp to 0-100
                return max(0, min(100, int(percentage)))
        return None

    def _cancel_impl(
        self, success_callback: Callable[[], None], error_callback: Optional[Callable[[str], None]]
    ) -> None:
        """Implementation of cancellation in background thread."""
        try:
            # Terminate process if running
            if self._process:
                self._process.terminate()

                # Wait for graceful termination
                try:
                    self._process.wait(timeout=2.0)
                except subprocess.TimeoutExpired:
                    # Force kill if not terminated
                    self._process.kill()
                    self._process.wait(timeout=1.0)

            # Wait for threads to finish
            if self._processing_thread and self._processing_thread.is_alive():
                self._processing_thread.join(timeout=3.0)

            if self._progress_thread and self._progress_thread.is_alive():
                self._progress_thread.join(timeout=1.0)

            # Cleanup files and processing state
            self._cleanup_after_cancel()
            self._cleanup_processing()

            success_callback()

        except Exception as e:
            # Ensure cleanup happens even if there's an exception
            try:
                self._cleanup_after_cancel()
                self._cleanup_processing()
            except Exception:
                pass  # Ignore cleanup errors

            if error_callback:
                error_callback(f"Cancellation error: {str(e)}")
            else:
                # Fallback to success if no error callback
                success_callback()

    def _cleanup_processing(self) -> None:
        """Clean up after processing completion."""
        with self._lock:
            self._is_processing = False

        self._process = None
        self._processing_thread = None
        self._progress_thread = None

        # Clear callbacks
        self._progress_callback = None
        self._status_callback = None
        self._complete_callback = None
        self._error_callback = None

    def _cleanup_temp_files_only(self) -> None:
        """Clean up temporary files but preserve output file."""
        temp_files_to_remove = []
        for temp_file in self._temp_files:
            # Don't remove the output file for successful processing
            if temp_file != self._output_file:
                temp_files_to_remove.append(temp_file)

        for temp_file in temp_files_to_remove:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass  # Ignore errors

        # Only remove non-output files from the list
        self._temp_files = [f for f in self._temp_files if f == self._output_file]

    def _cleanup_after_cancel(self) -> None:
        """Clean up after cancellation or error."""
        # Clean up all temp files including output
        for temp_file in self._temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError:
                    pass

        # Clean up output file if it exists
        if self._output_file and os.path.exists(self._output_file):
            try:
                os.remove(self._output_file)
            except OSError:
                pass

        self._temp_files = []
        self._output_file = None

    def _emit_status(self, message: str) -> None:
        """Emit status message via callback."""
        if self._status_callback:
            self._status_callback(message)

    def _emit_error(self, message: str) -> None:
        """Emit error message via callback and cleanup."""
        self._cleanup_after_cancel()
        if self._error_callback:
            self._error_callback(message)

    def _get_ffmpeg_path(self) -> str:
        """Get the path to the FFmpeg executable.

        Returns:
            Path to the FFmpeg executable
        """
        ffmpeg_path = _get_executable_path("ffmpeg")
        return str(ffmpeg_path) if ffmpeg_path else "ffmpeg"
