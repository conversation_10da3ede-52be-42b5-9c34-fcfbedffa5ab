"""Pure Python settings manager without Qt dependencies."""

import json
import logging
import os
import threading
from typing import Any, Callable, Dict, Optional

logger = logging.getLogger(__name__)


class SettingsManagerCore:
    """Pure Python settings manager implementing ISettingsManager protocol.
    
    This class provides the same functionality as SettingsManager but without Qt dependencies.
    It uses callback-based communication instead of Qt signals and is thread-safe.
    """
    
    def __init__(self, settings_file_path: str, default_settings: Optional[Dict[str, Any]] = None):
        """Initialize the settings manager.
        
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        self.settings_file_path = settings_file_path
        self._settings: Dict[str, Any] = {}
        self._lock = threading.Lock()
        
        if default_settings:
            self._settings.update(default_settings)
        self._load_settings()

    def _load_settings(self) -> None:
        """Load settings from JSON file, merging with existing defaults."""
        with self._lock:
            if os.path.exists(self.settings_file_path):
                try:
                    with open(self.settings_file_path, 'r') as f:
                        loaded_settings = json.load(f)
                        self._settings.update(loaded_settings)
                except FileNotFoundError:
                    logger.warning(
                        "Settings file not found: %s. Using default settings.",
                        self.settings_file_path
                    )
                except json.JSONDecodeError:
                    logger.warning(
                        f"Corrupt settings file: {self.settings_file_path}. Using default settings."
                    )
                except IOError as e:
                    logger.error(f"Error reading settings file {self.settings_file_path}: {e}")

    def _save_settings(self) -> None:
        """Save current settings to JSON file."""
        os.makedirs(os.path.dirname(self.settings_file_path), exist_ok=True)
        try:
            with open(self.settings_file_path, 'w') as f:
                json.dump(self._settings, f, indent=4)
        except IOError as e:
            logger.error(f"Error writing settings to file {self.settings_file_path}: {e}")

    def _validate_setting(self, name: str, value: Any) -> bool:
        """Validate a setting value based on its name.
        
        Args:
            name: Setting name to validate.
            value: Value to validate.
            
        Returns:
            True if valid, False otherwise.
        """
        if name == "output_bitrate":
            valid_bitrates = [32, 48, 56, 64, 96, 128]
            if not isinstance(value, int) or value not in valid_bitrates:
                logger.warning(f"Invalid output_bitrate: {value}. Must be one of {valid_bitrates}.")
                return False
        elif name == "output_directory":
            if not isinstance(value, str) or not value:
                logger.warning(f"Invalid output_directory: {value}. Must be a non-empty string.")
                return False
        elif name == "output_filename_pattern":
            if not isinstance(value, int) or value not in [0, 1, 2]:
                logger.warning(f"Invalid output_filename_pattern: {value}. Must be 0, 1, or 2.")
                return False
        return True

    def get_setting(self, name: str, default: Any = None) -> Any:
        """Get setting value by name.
        
        Args:
            name: Setting name
            default: Default value if setting not found
            
        Returns:
            Setting value or default
        """
        with self._lock:
            return self._settings.get(name, default)

    def set_setting(self, name: str, value: Any,
                    success_callback: Callable[[str, Any], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set setting value.
        
        Args:
            name: Setting name
            value: New setting value
            success_callback: Called with setting name and value when saved
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                if self._validate_setting(name, value):
                    self._settings[name] = value
                    self._save_settings()
                    # Execute callback outside the lock to prevent deadlocks
                    
            # Call success callback outside lock
            if self._validate_setting(name, value):
                success_callback(name, value)
            else:
                error_message = (f"Validation failed for setting '{name}' "
                                f"with value '{value}'. Setting not updated.")
                logger.error(error_message)
                if error_callback:
                    error_callback(error_message)
                    
        except Exception as e:
            error_message = f"Error setting '{name}' to '{value}': {str(e)}"
            logger.error(error_message)
            if error_callback:
                error_callback(error_message)

    def get_all_settings(self) -> Dict[str, Any]:
        """Get all current settings.
        
        Returns:
            Dictionary containing all settings.
        """
        with self._lock:
            return self._settings.copy()