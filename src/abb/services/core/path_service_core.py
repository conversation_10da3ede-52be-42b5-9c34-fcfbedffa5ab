"""Pure Python PathService implementation without Qt dependencies.

This module provides PathServiceCore, a pure Python implementation of the IPathService protocol.
It handles output path calculation and filename generation for audiobook processing without
any Qt dependencies, making it easier to test and reuse in different contexts.
"""

from pathlib import Path
from typing import Any, Dict


class PathServiceCore:
    """Pure Python implementation of path and filename generation service.
    
    This service provides functionality for:
    - Calculating output directory paths with optional subdirectory patterns
    - Generating output filenames using different naming patterns
    - Path and filename sanitization for cross-platform compatibility
    
    The service is stateless and thread-safe, implementing the IPathService protocol.
    """
    
    def calculate_output_path(self, base_dir: str, metadata: Dict[str, Any], 
                            use_subdirectory_pattern: bool) -> str:
        """Calculate the output directory path based on settings and metadata.
        
        Creates subdirectories based on artist and series metadata when enabled.
        The subdirectory structure is: base_dir/artist/series (if both present).
        
        Args:
            base_dir: Base output directory path.
            metadata: Dictionary containing audio metadata (artist, series).
            use_subdirectory_pattern: Whether to create subdirectories for author/series.
            
        Returns:
            Full output directory path as string.
            
        Example:
            >>> service = PathServiceCore()
            >>> metadata = {"artist": "John Doe", "series": "Mystery Series"}
            >>> service.calculate_output_path("/output", metadata, True)
            "/output/John Doe/Mystery Series"
        """
        output_path = Path(base_dir)
        
        if use_subdirectory_pattern:
            author = self._safe_str(metadata.get("artist", "")).strip()
            if author:
                output_path = output_path / self._sanitize_path(author)
            
            series = self._safe_str(metadata.get("series", "")).strip()
            if series:
                output_path = output_path / self._sanitize_path(series)
        
        return str(output_path)
    
    def generate_output_filename(self, metadata: Dict[str, Any], pattern_id: int) -> str:
        """Generate output filename based on pattern and metadata.
        
        Supports three filename patterns:
        - Pattern 0: "Title (Year)" - title with optional year in parentheses
        - Pattern 1: "Author - Title" - author and title separated by dash
        - Pattern 2: "Series - Title" - series and title separated by dash
        
        Args:
            metadata: Dictionary containing audio metadata (title, artist, series, year).
            pattern_id: Pattern to use (0=Title (Year), 1=Author - Title, 2=Series - Title).
            
        Returns:
            Sanitized filename string with .m4b extension.
            
        Example:
            >>> service = PathServiceCore()
            >>> metadata = {"title": "The Book", "artist": "Author", "year": "2023"}
            >>> service.generate_output_filename(metadata, 0)
            "The Book (2023).m4b"
        """
        filename = "audiobook.m4b"  # Default fallback
        
        if pattern_id == 0:  # Title (Year)
            title = self._safe_str(metadata.get("title", ""))
            year = self._safe_str(metadata.get("year", ""))
            if title.strip():
                filename = f"{title} ({year}).m4b" if year.strip() else f"{title}.m4b"
                
        elif pattern_id == 1:  # Author - Title
            author = self._safe_str(metadata.get("artist", ""))
            title = self._safe_str(metadata.get("title", ""))
            if author.strip() and title.strip():
                filename = f"{author} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
                
        elif pattern_id == 2:  # Series - Title
            series = self._safe_str(metadata.get("series", ""))
            title = self._safe_str(metadata.get("title", ""))
            if series.strip() and title.strip():
                filename = f"{series} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        
        # Validate generated filename to prevent empty or invalid names
        if not filename.strip() or filename.strip() in [".m4b", "().m4b", "( ).m4b"]:
            filename = "audiobook.m4b"
        
        return self._sanitize_filename(filename)
    
    def _sanitize_path(self, path_str: str) -> str:
        r"""Remove invalid characters from path components.
        
        Replaces characters that are invalid in file/directory names on most
        operating systems with underscores to ensure cross-platform compatibility.
        
        Args:
            path_str: Path string to sanitize.
            
        Returns:
            Sanitized path string with invalid characters replaced by underscores.
            
        Note:
            The following characters are considered invalid: < > : " / \ | ? *
        """
        invalid_chars = ["<", ">", ":", '"', "/", "\\", "|", "?", "*"]
        for char in invalid_chars:
            path_str = path_str.replace(char, "_")
        return path_str
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.
        
        Uses the same sanitization rules as path components to ensure
        the filename is valid across different operating systems.
        
        Args:
            filename: Filename to sanitize.
            
        Returns:
            Sanitized filename with invalid characters replaced.
        """
        return self._sanitize_path(filename)
    
    def _safe_str(self, val: Any) -> str:
        """Safely convert value to string.
        
        Handles None values gracefully by converting them to empty strings,
        while converting all other values to their string representation.
        
        Args:
            val: Value to convert (can be None, int, str, etc.).
            
        Returns:
            String representation of value or empty string if None.
        """
        return str(val) if val is not None else ""