"""FileServiceCore - Pure Python file service without Qt dependencies.

This module provides a FileService implementation that uses callback-based communication
instead of Qt signals, enabling pure Python usage and easier testing.
"""

import os
import threading
from pathlib import Path
from typing import Callable, List, Optional, Set

from ..interfaces import <PERSON>rror<PERSON>ate<PERSON><PERSON>, ServiceError


class FileServiceCore:
    """Pure Python file service implementation.
    
    Provides file management operations (add, remove, reorder) without Qt dependencies.
    Uses callback-based communication instead of Qt signals for maximum flexibility.
    Thread-safe operations using threading.Lock.
    """
    
    def __init__(self):
        """Initialize the FileServiceCore with an empty file list."""
        self._files: List[str] = []
        self._valid_extensions: Set[str] = {".mp3", ".m4a", ".m4b", ".aac"}
        self._lock = threading.Lock()
    
    def get_files(self) -> List[str]:
        """Get the current list of files.
        
        Returns:
            List of file paths currently managed by the service.
        """
        with self._lock:
            return self._files.copy()
    
    def get_combined_size(self) -> int:
        """Get total size of all files in bytes.
        
        Returns:
            Total size in bytes of all managed files.
        """
        with self._lock:
            total = 0
            for file_path in self._files:
                try:
                    total += os.path.getsize(file_path)
                except (OSError, IOError):
                    # Skip files that can't be accessed (deleted, moved, etc.)
                    continue
            return total
    
    def add_files(self, paths: List[str], 
                  success_callback: Callable[[List[str]], None],
                  error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Add files to the managed list.
        
        Args:
            paths: List of file paths to add
            success_callback: Called with list of successfully added files
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                normalized_paths = []
                # Use a set of normalized existing paths for efficient lookup
                existing_paths_lookup = {self._normalize_path(p) for p in self._files}
                
                for path in paths:
                    try:
                        normalized = self._normalize_path(path)
                        
                        # Skip duplicates
                        if normalized in existing_paths_lookup:
                            continue
                        
                        # Validate file exists and is accessible
                        if not os.path.exists(normalized):
                            if error_callback:
                                error_callback(f"File not found: {path}")
                            continue
                        
                        # Validate file extension
                        if not self._is_valid_audio_file(normalized):
                            if error_callback:
                                error_callback(f"Invalid audio file format: {path}")
                            continue
                        
                        normalized_paths.append(normalized)
                        existing_paths_lookup.add(normalized)
                        
                    except (OSError, IOError) as e:
                        if error_callback:
                            error_callback(f"Error accessing file {path}: {str(e)}")
                        continue
                    except Exception as e:
                        if error_callback:
                            error_callback(f"Unexpected error processing {path}: {str(e)}")
                        continue
                
                # Add successfully processed files
                if normalized_paths:
                    self._files.extend(normalized_paths)
                
                # Always call success callback with the files that were added
                success_callback(normalized_paths)
                
        except Exception as e:
            error_msg = f"Failed to add files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.FILE_NOT_FOUND) from None
    
    def remove_file(self, index: int,
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Remove file by index.
        
        Args:
            index: Index of file to remove
            success_callback: Called when file is successfully removed
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                if index < 0 or index >= len(self._files):
                    error_msg = (f"Invalid index {index}. "
                                f"Must be between 0 and {len(self._files) - 1}")
                    if error_callback:
                        error_callback(error_msg)
                    else:
                        raise ServiceError(error_msg, ErrorCategory.VALIDATION_ERROR)
                    return
                
                self._files.pop(index)
                success_callback()
                
        except Exception as e:
            error_msg = f"Failed to remove file at index {index}: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    
    def reorder_files(self, new_order: List[str],
                      success_callback: Callable[[], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Reorder files according to new path order.
        
        Args:
            new_order: List of file paths in desired order
            success_callback: Called when reordering is complete
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                # Validate that new_order contains the same files as current list
                current_normalized = {self._normalize_path(p) for p in self._files}
                new_normalized = {self._normalize_path(p) for p in new_order}
                
                if current_normalized != new_normalized:
                    error_msg = "New order must contain exactly the same files as current list"
                    if error_callback:
                        error_callback(error_msg)
                    else:
                        raise ServiceError(error_msg, ErrorCategory.VALIDATION_ERROR)
                    return
                
                # Normalize all paths in the new order
                normalized_new_order = [self._normalize_path(p) for p in new_order]
                self._files = normalized_new_order
                success_callback()
                
        except Exception as e:
            error_msg = f"Failed to reorder files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        
        Args:
            path: File path to normalize
            
        Returns:
            Normalized path
            
        Raises:
            ServiceError: If path cannot be normalized
        """
        try:
            return str(Path(path).resolve())
        except (OSError, ValueError) as e:
            raise ServiceError(f"Cannot normalize path '{path}': {str(e)}", 
                             ErrorCategory.VALIDATION_ERROR) from e
    
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        
        Args:
            path: File path to check
            
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        try:
            ext = Path(path).suffix.lower()
            return ext in self._valid_extensions
        except Exception:
            return False
    
    def clear_files(self, 
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all files from the managed list.
        
        Args:
            success_callback: Called when files are cleared
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._files.clear()
                success_callback()
        except Exception as e:
            error_msg = f"Failed to clear files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None
    
    def get_file_count(self) -> int:
        """Get the number of files currently managed.
        
        Returns:
            Number of files in the managed list.
        """
        with self._lock:
            return len(self._files)
    
    def format_combined_size(self) -> str:
        """Get the combined size formatted for display.
        
        Returns:
            Human-readable size string (e.g., "1.5 MB", "2.3 GB").
        """
        size = self.get_combined_size()
        
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"
    
    def validate_files(self, 
                       success_callback: Callable[[List[str]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Validate that all managed files still exist and are accessible.
        
        Removes any files that are no longer accessible.
        
        Args:
            success_callback: Called with list of files that were removed (if any)
            error_callback: Called with error message if validation fails
        """
        try:
            with self._lock:
                removed_files = []
                valid_files = []
                
                for file_path in self._files:
                    try:
                        if os.path.exists(file_path) and os.access(file_path, os.R_OK):
                            valid_files.append(file_path)
                        else:
                            removed_files.append(file_path)
                    except (OSError, IOError):
                        removed_files.append(file_path)
                
                if removed_files:
                    self._files = valid_files
                
                success_callback(removed_files)
                
        except Exception as e:
            error_msg = f"Failed to validate files: {str(e)}"
            if error_callback:
                error_callback(error_msg)
            else:
                raise ServiceError(error_msg, ErrorCategory.PROCESSING_ERROR) from None