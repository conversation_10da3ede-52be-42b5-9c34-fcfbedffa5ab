"""MetadataHandlerCore - Pure Python metadata handler without Qt dependencies.

This is the core implementation of the IMetadataHandler protocol that provides
callback-based communication instead of Qt signals. It wraps existing metadata
utilities from metadata_utils while maintaining complete Qt independence.

Key features:
- Implements IMetadataHandler protocol
- Thread-safe operations using threading.Lock
- Wraps metadata_utils functions for compatibility
- Maintains ABB to FFmpeg metadata mapping as single source of truth
- Supports cover art handling without Qt pixmaps
- Error handling with proper error categories
"""

import os
import threading
from pathlib import Path
from typing import Any, Callable, Dict, Optional

# Import core metadata functions (avoiding Qt dependencies)
from ...metadata_utils import (
    apply_metadata_defaults,
    clear_metadata_cache,
    extract_cover,
    extract_metadata,
)
from ..interfaces import ErrorCategory, ServiceError


class MetadataHandlerCore:
    """Pure Python metadata handler implementing IMetadataHandler protocol.
    
    This class provides a Qt-independent implementation of metadata operations
    using callback-based communication. It wraps existing metadata_utils functions
    while providing thread-safe state management and proper error handling.
    """
    
    # Single source of truth for ABB to FFmpeg metadata mapping
    # Inherited from UnifiedMetadataHandler for consistency
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album", 
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    
    def __init__(self):
        """Initialize the metadata handler with empty state."""
        self._lock = threading.Lock()
        self._current_metadata: Dict[str, Any] = {}
        self._current_file_path: Optional[str] = None
        self._cover_art_path: Optional[str] = None
        self._cover_art_data: Optional[bytes] = None
    
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get current metadata state.
        
        Thread-safe access to current metadata dictionary.
        
        Returns:
            Dictionary containing current metadata.
        """
        with self._lock:
            return self._current_metadata.copy()
    
    def load_from_file(self, path: str,
                       success_callback: Callable[[Dict[str, Any]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Load metadata from audio file.
        
        Args:
            path: Path to audio file
            success_callback: Called with extracted metadata
            error_callback: Called with error message if operation fails
        """
        def _load_operation():
            try:
                # Validate file exists
                if not os.path.exists(path):
                    error_msg = f"File not found: {path}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                
                # Extract metadata using existing utilities
                metadata = self._extract_metadata_safe(path)
                
                with self._lock:
                    self._current_metadata = metadata.copy()
                    self._current_file_path = path
                    
                    # Handle cover art extraction
                    self._extract_cover_art_safe(path)
                
                # Call success callback with metadata
                success_callback(metadata)
                
            except Exception as e:
                error_msg = f"Failed to load metadata from {path}: {str(e)}"
                if error_callback:
                    error_callback(error_msg)
        
        # Execute in thread for non-blocking operation
        thread = threading.Thread(target=_load_operation)
        thread.daemon = True
        thread.start()
    
    def update_field(self, field: str, value: Any,
                     success_callback: Callable[[Dict[str, Any]], None],
                     error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Update a metadata field.
        
        Args:
            field: Metadata field name
            value: New value for the field
            success_callback: Called with updated metadata
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._current_metadata[field] = value
                updated_metadata = self._current_metadata.copy()
            
            success_callback(updated_metadata)
            
        except Exception as e:
            error_msg = f"Failed to update field '{field}': {str(e)}"
            if error_callback:
                error_callback(error_msg)
    
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg.
        
        Uses the ABB_TO_FFMPEG_METADATA_MAP_GENERAL mapping to transform
        metadata fields into FFmpeg-compatible format.
        
        Returns:
            Dictionary with FFmpeg-compatible metadata mappings.
        """
        with self._lock:
            ffmpeg_metadata = {}
            
            # Use the unified mapping - single source of truth
            for abb_key, ffmpeg_key in self.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.items():
                value = self._current_metadata.get(abb_key)
                if value is not None and str(value).strip() != "":
                    ffmpeg_metadata[ffmpeg_key] = str(value)
            
            # Handle special mappings (from legacy command_builder.py logic)
            artist_val = self._current_metadata.get('artist')
            if artist_val:
                ffmpeg_metadata['artist'] = str(artist_val)
                # Set album_artist to artist if not explicitly set
                if 'album_artist' not in ffmpeg_metadata:
                    ffmpeg_metadata['album_artist'] = str(artist_val)
            
            # Handle comment/description priority
            comment_val = self._current_metadata.get('comment')
            description_val = self._current_metadata.get('description')
            if comment_val:
                ffmpeg_metadata['comment'] = str(comment_val)
            elif description_val:
                ffmpeg_metadata['comment'] = str(description_val)
            
            # Handle series_sort priority for album_sort
            series_sort_val = self._current_metadata.get('series_sort')
            sort_album_val = self._current_metadata.get('sort_album')
            if series_sort_val:
                ffmpeg_metadata['album_sort'] = str(series_sort_val)
            elif sort_album_val:
                ffmpeg_metadata['album_sort'] = str(sort_album_val)
            
            return ffmpeg_metadata
    
    def clear_metadata(self,
                       success_callback: Callable[[], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all metadata.
        
        Args:
            success_callback: Called when metadata is cleared
            error_callback: Called with error message if operation fails
        """
        try:
            with self._lock:
                self._current_metadata.clear()
                self._current_file_path = None
                self._cover_art_path = None
                self._cover_art_data = None
            
            # Clear the global metadata cache as well
            clear_metadata_cache()
            
            success_callback()
            
        except Exception as e:
            error_msg = f"Failed to clear metadata: {str(e)}"
            if error_callback:
                error_callback(error_msg)
    
    def set_cover_art(self, path: str,
                      success_callback: Callable[[Optional[str]], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set cover art from file path.
        
        Args:
            path: Path to cover art image file
            success_callback: Called with cover art path (None if cleared)
            error_callback: Called with error message if operation fails
        """
        def _set_cover_operation():
            try:
                if not path:  # Clear cover art
                    with self._lock:
                        self._cover_art_path = None
                        self._cover_art_data = None
                    success_callback(None)
                    return
                
                # Validate file exists and is an image
                if not os.path.exists(path):
                    error_msg = f"Cover art file not found: {path}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                
                # Check if it's a supported image format
                valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
                file_ext = Path(path).suffix.lower()
                if file_ext not in valid_extensions:
                    error_msg = f"Unsupported cover art format: {file_ext}"
                    if error_callback:
                        error_callback(error_msg)
                    return
                
                # Read image data for validation
                try:
                    with open(path, 'rb') as f:
                        cover_data = f.read()
                    
                    with self._lock:
                        self._cover_art_path = path
                        self._cover_art_data = cover_data
                    
                    success_callback(path)
                    
                except Exception as e:
                    error_msg = f"Failed to read cover art file: {str(e)}"
                    if error_callback:
                        error_callback(error_msg)
                
            except Exception as e:
                error_msg = f"Failed to set cover art: {str(e)}"
                if error_callback:
                    error_callback(error_msg)
        
        # Execute in thread for non-blocking operation
        thread = threading.Thread(target=_set_cover_operation)
        thread.daemon = True
        thread.start()
    
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art path.
        
        Returns:
            Path to current cover art file, or None if no cover art set.
        """
        with self._lock:
            return self._cover_art_path
    
    # Additional utility methods to support the interface
    
    def has_metadata(self) -> bool:
        """Check if metadata is currently loaded.
        
        Returns:
            True if metadata is loaded, False otherwise.
        """
        with self._lock:
            return bool(self._current_metadata)
    
    def get_field(self, field: str, default: Any = None) -> Any:
        """Get a specific metadata field value.
        
        Args:
            field: The field name to retrieve
            default: Default value if field not found
            
        Returns:
            The field value or default
        """
        with self._lock:
            return self._current_metadata.get(field, default)
    
    def reset_state(self) -> None:
        """Reset the handler's internal state.
        
        Thread-safe reset of all internal state.
        """
        with self._lock:
            self._current_metadata.clear()
            self._current_file_path = None
            self._cover_art_path = None
            self._cover_art_data = None
    
    def get_current_file_path(self) -> Optional[str]:
        """Get the file path of currently loaded metadata.
        
        Returns:
            Current file path or None if no file loaded
        """
        with self._lock:
            return self._current_file_path
    
    def get_cover_art_data(self) -> Optional[bytes]:
        """Get cover art data as bytes.
        
        Returns:
            Cover art data as bytes or None if no cover art set.
        """
        with self._lock:
            return self._cover_art_data
    
    # Private helper methods
    
    def _extract_metadata_safe(self, file_path: str) -> Dict[str, Any]:
        """Safely extract metadata from file using existing utilities.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Dictionary containing extracted metadata
            
        Raises:
            ServiceError: If metadata extraction fails
        """
        try:
            # Use existing extract_metadata function
            metadata = extract_metadata(file_path)
            
            # Apply defaults using existing function
            metadata = apply_metadata_defaults(metadata)
            
            # Remove Qt-dependent cover_art field if present
            if 'cover_art' in metadata:
                del metadata['cover_art']
            
            return metadata
            
        except Exception as e:
            raise ServiceError(
                f"Failed to extract metadata from {file_path}: {str(e)}",
                ErrorCategory.METADATA_ERROR
            ) from e
    
    def _extract_cover_art_safe(self, file_path: str) -> None:
        """Safely extract cover art from file.
        
        Args:
            file_path: Path to audio file
        """
        try:
            # Use existing extract_cover function to get raw bytes
            cover_data = extract_cover(file_path)
            if cover_data:
                self._cover_art_data = cover_data
                # Store original file path as cover art source
                self._cover_art_path = file_path
            else:
                self._cover_art_data = None
                self._cover_art_path = None
                
        except Exception as e:
            # Don't raise error for cover art extraction failure
            # Just log and continue without cover art
            print(f"Warning: Failed to extract cover art from {file_path}: {e}")
            self._cover_art_data = None
            self._cover_art_path = None
    
    @classmethod
    def get_metadata_mapping(cls) -> Dict[str, str]:
        """Get the ABB to FFmpeg metadata mapping.
        
        Provides access to the single source of truth mapping without
        requiring an instance of the handler.
        
        Returns:
            Dictionary mapping ABB field names to FFmpeg field names
        """
        return cls.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.copy()


# Factory function for creating instances
def create_metadata_handler_core() -> MetadataHandlerCore:
    """Create a new MetadataHandlerCore instance.
    
    Returns:
        New handler instance
    """
    return MetadataHandlerCore()


# Feature flag support
def is_core_metadata_enabled() -> bool:
    """Check if the core metadata handler should be used.
    
    Reads the ABB_PURE_SERVICES environment variable.
    
    Returns:
        True if ABB_PURE_SERVICES is set to 'true' (case-insensitive), False otherwise
    """
    return os.environ.get('ABB_PURE_SERVICES', 'False').lower() == 'true'