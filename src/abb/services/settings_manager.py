"""Settings manager for application configuration persistence."""

import json
import logging
import os

from PySide6.QtCore import QObject, Signal

logger = logging.getLogger(__name__)

class SettingsManager(QObject):
    """Manages application settings persistence and validation."""
    
    settings_changed = Signal(str, object)
    
    def __init__(self, settings_file_path, default_settings=None):
        """Initialize the settings manager.
        
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        super().__init__()
        self.settings_file_path = settings_file_path
        self._settings = {}
        if default_settings:
            self._settings.update(default_settings)
        self._load_settings()

    def _load_settings(self):
        """Load settings from JSON file, merging with existing defaults."""
        if os.path.exists(self.settings_file_path):
            try:
                with open(self.settings_file_path, 'r') as f:
                    loaded_settings = json.load(f)
                    self._settings.update(loaded_settings)
            except FileNotFoundError:
                logger.warning(
                    f"Settings file not found: {self.settings_file_path}. Using default settings."
                )
            except json.JSONDecodeError:
                logger.warning(
                    f"Corrupt settings file: {self.settings_file_path}. Using default settings."
                )
            except IOError as e:
                logger.error(f"Error reading settings file {self.settings_file_path}: {e}")

    def _save_settings(self):
        """Save current settings to JSON file."""
        os.makedirs(os.path.dirname(self.settings_file_path), exist_ok=True)
        try:
            with open(self.settings_file_path, 'w') as f:
                json.dump(self._settings, f, indent=4)
        except IOError as e:
            logger.error(f"Error writing settings to file {self.settings_file_path}: {e}")

    def get_setting(self, name, default_value=None):
        """Get a setting value by name.
        
        Args:
            name: Setting name to retrieve.
            default_value: Value to return if setting not found.
            
        Returns:
            Setting value or default_value if not found.
        """
        return self._settings.get(name, default_value)

    def set_setting(self, name, value):
        """Set a setting value with validation.
        
        Args:
            name: Setting name to update.
            value: New value for the setting.
        """
        if self._validate_setting(name, value):
            self._settings[name] = value
            self._save_settings()
            self.settings_changed.emit(name, value)
        else:
            logger.error(
                f"Validation failed for setting '{name}' with value '{value}'. Setting not updated."
            )

    def _validate_setting(self, name, value):
        """Validate a setting value based on its name.
        
        Args:
            name: Setting name to validate.
            value: Value to validate.
            
        Returns:
            True if valid, False otherwise.
        """
        if name == "output_bitrate":
            valid_bitrates = [32, 48, 56, 64, 96, 128]
            if not isinstance(value, int) or value not in valid_bitrates:
                logger.warning(f"Invalid output_bitrate: {value}. Must be one of {valid_bitrates}.")
                return False
        elif name == "output_directory":
            if not isinstance(value, str) or not value:
                logger.warning(f"Invalid output_directory: {value}. Must be a non-empty string.")
                return False
        elif name == "output_filename_pattern":
            if not isinstance(value, int) or value not in [0, 1, 2]:
                logger.warning(f"Invalid output_filename_pattern: {value}. Must be 0, 1, or 2.")
                return False
        return True