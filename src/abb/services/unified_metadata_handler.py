"""Unified Metadata Handler for Audiobook Boss.

A facade pattern that wraps existing metadata extraction functions while providing
a unified interface for metadata management. Enables gradual migration through
the ABB_NEW_META environment variable.

This handler maintains 100% compatibility with existing functions while providing
a cleaner interface for future development.
"""
import os
from typing import Any, Dict, Optional

# Import existing functions to wrap
from ..metadata_utils import (
    apply_metadata_defaults,
    clear_metadata_cache,
    extract_cover,
    extract_metadata,
    extract_tags,
)


class UnifiedMetadataHandler:
    """Unified facade for metadata operations.
    
    Wraps existing metadata functions while providing a cleaner interface
    and state management for metadata operations.
    """
    
    # Single source of truth for ABB to FFmpeg metadata mapping
    # This mapping is used throughout the application for consistency
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album",
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    
    def __init__(self):
        """Initialize the handler with empty state."""
        self._current_metadata: Dict[str, Any] = {}
        self._current_file_path: Optional[str] = None
        
    # Primary compatibility interfaces - wrap existing functions exactly
    
    def load_from_file(self, file_path: str) -> Dict[str, Any]:
        """Load metadata from a file (wraps extract_metadata).
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary containing metadata fields with defaults applied
        """
        metadata = extract_metadata(file_path)
        self._current_metadata = metadata.copy()
        self._current_file_path = file_path
        return metadata
    
    def extract_tags_only(self, file_path: str) -> dict:
        """Extract basic tags only (wraps extract_tags).
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary with basic tags, None for missing values
        """
        return extract_tags(file_path)
    
    def extract_cover_art(self, file_path: str) -> Optional[bytes]:
        """Extract cover art as bytes (wraps extract_cover).
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Cover art as bytes or None if not found
        """
        return extract_cover(file_path)
    
    # New unified interface for state management
    
    def update_field(self, field: str, value: Any) -> None:
        """Update a metadata field in the current state.
        
        Args:
            field: The metadata field name (e.g., 'title', 'artist')
            value: The new value for the field
        """
        self._current_metadata[field] = value
    
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get the current metadata state.
        
        Returns:
            Copy of current metadata dictionary
        """
        return self._current_metadata.copy()
    
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg command building.
        
        Uses the ABB_TO_FFMPEG_METADATA_MAP_GENERAL mapping
        to transform metadata fields into FFmpeg-compatible format.
        
        Returns:
            Dictionary with FFmpeg metadata keys and string values
        """
        ffmpeg_metadata = {}
        
        # Use the unified mapping - single source of truth
        for abb_key, ffmpeg_key in self.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.items():
            value = self._current_metadata.get(abb_key)
            if value is not None and str(value).strip() != "":
                ffmpeg_metadata[ffmpeg_key] = str(value)
        
        # Handle special mappings (from legacy command_builder.py logic)
        artist_val = self._current_metadata.get('artist')
        if artist_val:
            ffmpeg_metadata['artist'] = str(artist_val)
            # Set album_artist to artist if not explicitly set
            if 'album_artist' not in ffmpeg_metadata:
                ffmpeg_metadata['album_artist'] = str(artist_val)
        
        # Handle comment/description priority
        comment_val = self._current_metadata.get('comment')
        description_val = self._current_metadata.get('description')
        if comment_val:
            ffmpeg_metadata['comment'] = str(comment_val)
        elif description_val:
            ffmpeg_metadata['comment'] = str(description_val)
        
        # Handle series_sort priority for album_sort
        series_sort_val = self._current_metadata.get('series_sort')
        sort_album_val = self._current_metadata.get('sort_album')
        if series_sort_val:
            ffmpeg_metadata['album_sort'] = str(series_sort_val)
        elif sort_album_val:
            ffmpeg_metadata['album_sort'] = str(sort_album_val)
        
        return ffmpeg_metadata
    
    def apply_defaults(self) -> None:
        """Apply default metadata logic to current state."""
        self._current_metadata = apply_metadata_defaults(self._current_metadata)
    
    def clear_cache(self) -> None:
        """Clear the metadata cache."""
        clear_metadata_cache()
    
    def reset_state(self) -> None:
        """Reset the handler's internal state."""
        self._current_metadata = {}
        self._current_file_path = None
    
    # Utility methods
    
    def get_current_file_path(self) -> Optional[str]:
        """Get the file path of currently loaded metadata.
        
        Returns:
            Current file path or None if no file loaded
        """
        return self._current_file_path
    
    def has_metadata(self) -> bool:
        """Check if metadata is currently loaded.
        
        Returns:
            True if metadata is loaded, False otherwise
        """
        return bool(self._current_metadata)
    
    def get_field(self, field: str, default: Any = None) -> Any:
        """Get a specific metadata field value.
        
        Args:
            field: The field name to retrieve
            default: Default value if field not found
            
        Returns:
            The field value or default
        """
        return self._current_metadata.get(field, default)
    
    @staticmethod
    def is_enabled() -> bool:
        """Check if the unified metadata handler should be used.
        
        Reads the ABB_NEW_META environment variable.
        
        Returns:
            True if ABB_NEW_META is set to 'true' (case-insensitive), False otherwise
        """
        return os.environ.get('ABB_NEW_META', 'False').lower() == 'true'
    
    @classmethod
    def get_metadata_mapping(cls) -> Dict[str, str]:
        """Get the ABB to FFmpeg metadata mapping.
        
        Provides access to the single source of truth mapping without
        requiring an instance of the handler.
        
        Returns:
            Dictionary mapping ABB field names to FFmpeg field names
        """
        return cls.ABB_TO_FFMPEG_METADATA_MAP_GENERAL.copy()


# Convenience factory function for backward compatibility
def create_metadata_handler() -> UnifiedMetadataHandler:
    """Create a new UnifiedMetadataHandler instance.
    
    Returns:
        New handler instance
    """
    return UnifiedMetadataHandler()