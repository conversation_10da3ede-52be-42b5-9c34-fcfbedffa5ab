"""Service factory with feature flag support for ABB services.

This module provides a centralized factory for creating service instances
with support for the ABB_PURE_SERVICES feature flag that enables gradual
migration between Qt-based and pure Python service implementations.
"""

import logging
import os
from typing import Optional, Union

from .adapters.file_service_adapter import QtFileServiceAdapter
from .adapters.metadata_handler_adapter import QtMetadataHandlerAdapter
from .adapters.processing_service_adapter import QtProcessingServiceAdapter
from .adapters.settings_manager_adapter import QtSettingsManagerAdapter

# Import core services and adapters
from .core.file_service_core import FileServiceCore
from .core.metadata_handler_core import MetadataHandlerCore
from .core.path_service_core import PathServiceCore
from .core.processing_service_core import ProcessingServiceCore
from .core.settings_manager_core import SettingsManagerCore

# Import current Qt services
from .file_service import FileService
from .interfaces import (
    IPathService,
)
from .metadata_service import MetadataService
from .path_service import PathService
from .processing_service import ProcessingService
from .settings_manager import SettingsManager

logger = logging.getLogger(__name__)


def is_pure_services_enabled() -> bool:
    """Check if pure services are enabled via environment variable.
    
    Returns:
        True if ABB_PURE_SERVICES is set to 'true' (case insensitive)
    """
    return os.environ.get('ABB_PURE_SERVICES', 'false').lower() == 'true'


def create_file_service() -> Union[FileService, QtFileServiceAdapter]:
    """Create a file service instance based on feature flag.
    
    Returns:
        FileService instance (Qt-based) or QtFileServiceAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure FileService with Qt adapter")
        core_service = FileServiceCore()
        return QtFileServiceAdapter(core_service)
    else:
        logger.debug("Creating Qt-based FileService")
        return FileService()


def create_metadata_service() -> Union[MetadataService, QtMetadataHandlerAdapter]:
    """Create a metadata service instance based on feature flag.
    
    Returns:
        MetadataService instance (Qt-based) or QtMetadataHandlerAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure MetadataHandler with Qt adapter")
        core_service = MetadataHandlerCore()
        return QtMetadataHandlerAdapter(core_service)
    else:
        logger.debug("Creating Qt-based MetadataService")
        return MetadataService()


def create_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    """Create a processing service instance based on feature flag.
    
    Args:
        path_service: Optional path service dependency. If not provided,
                     will be created based on the same feature flag.
    
    Returns:
        ProcessingService instance (Qt-based) or QtProcessingServiceAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure ProcessingService with Qt adapter")
        
        # Create path service if not provided
        if path_service is None:
            path_service = create_path_service()
            
        core_service = ProcessingServiceCore(path_service)
        return QtProcessingServiceAdapter(core_service, path_service)
    else:
        logger.debug("Creating Qt-based ProcessingService")
        return ProcessingService()


def create_settings_manager(settings_file_path: str, 
                           default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    """Create a settings manager instance based on feature flag.
    
    Args:
        settings_file_path: Path to settings file
        default_settings: Optional default settings dictionary
    
    Returns:
        SettingsManager instance (Qt-based) or QtSettingsManagerAdapter wrapping pure service
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure SettingsManager with Qt adapter")
        core_service = SettingsManagerCore(settings_file_path, default_settings)
        return QtSettingsManagerAdapter(core_service)
    else:
        logger.debug("Creating Qt-based SettingsManager")
        return SettingsManager(settings_file_path, default_settings)


def create_path_service() -> Union[PathService, PathServiceCore]:
    """Create a path service instance based on feature flag.
    
    Note: PathService is already pure Python, but this factory maintains
    consistency and allows for future Qt-specific enhancements if needed.
    
    Returns:
        PathService instance (existing) or PathServiceCore (pure implementation)
    """
    if is_pure_services_enabled():
        logger.debug("Creating pure PathServiceCore")
        return PathServiceCore()
    else:
        logger.debug("Creating existing PathService")
        return PathService()


class ServiceFactory:
    """Factory class for creating service instances with caching support.
    
    This factory provides both factory functions and caching capabilities
    for service instances that should be singletons.
    """
    
    def __init__(self):
        """Initialize the service factory."""
        self._services = {}
        
    def get_file_service(self) -> Union[FileService, QtFileServiceAdapter]:
        """Get cached file service or create new one.
        
        Returns:
            Cached or new file service instance
        """
        if 'file_service' not in self._services:
            self._services['file_service'] = create_file_service()
        return self._services['file_service']
        
    def get_metadata_service(self) -> Union[MetadataService, QtMetadataHandlerAdapter]:
        """Get cached metadata service or create new one.
        
        Returns:
            Cached or new metadata service instance
        """
        if 'metadata_service' not in self._services:
            self._services['metadata_service'] = create_metadata_service()
        return self._services['metadata_service']
        
    def get_processing_service(self, path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
        """Get cached processing service or create new one.
        
        Args:
            path_service: Optional path service dependency
            
        Returns:
            Cached or new processing service instance
        """
        if 'processing_service' not in self._services:
            self._services['processing_service'] = create_processing_service(path_service)
        return self._services['processing_service']
        
    def get_settings_manager(self, settings_file_path: str, 
                           default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
        """Get cached settings manager or create new one.
        
        Args:
            settings_file_path: Path to settings file
            default_settings: Optional default settings
            
        Returns:
            Cached or new settings manager instance
        """
        cache_key = f'settings_manager_{settings_file_path}'
        if cache_key not in self._services:
            self._services[cache_key] = create_settings_manager(settings_file_path, default_settings)
        return self._services[cache_key]
        
    def get_path_service(self) -> Union[PathService, PathServiceCore]:
        """Get cached path service or create new one.
        
        Returns:
            Cached or new path service instance
        """
        if 'path_service' not in self._services:
            self._services['path_service'] = create_path_service()
        return self._services['path_service']
        
    def clear_cache(self) -> None:
        """Clear all cached service instances."""
        self._services.clear()
        logger.debug("Service factory cache cleared")
        
    def get_service_info(self) -> dict:
        """Get information about current service configuration.
        
        Returns:
            Dictionary with service configuration information
        """
        return {
            'pure_services_enabled': is_pure_services_enabled(),
            'cached_services': list(self._services.keys()),
            'environment_variable': os.environ.get('ABB_PURE_SERVICES', 'not set')
        }


# Global factory instance for convenience
default_factory = ServiceFactory()


# Convenience functions using the default factory
def get_file_service() -> Union[FileService, QtFileServiceAdapter]:
    """Get file service from default factory."""
    return default_factory.get_file_service()


def get_metadata_service() -> Union[MetadataService, QtMetadataHandlerAdapter]:
    """Get metadata service from default factory."""
    return default_factory.get_metadata_service()


def get_processing_service(path_service: Optional[IPathService] = None) -> Union[ProcessingService, QtProcessingServiceAdapter]:
    """Get processing service from default factory."""
    return default_factory.get_processing_service(path_service)


def get_settings_manager(settings_file_path: str, 
                        default_settings: Optional[dict] = None) -> Union[SettingsManager, QtSettingsManagerAdapter]:
    """Get settings manager from default factory."""
    return default_factory.get_settings_manager(settings_file_path, default_settings)


def get_path_service() -> Union[PathService, PathServiceCore]:
    """Get path service from default factory."""
    return default_factory.get_path_service()


def clear_service_cache() -> None:
    """Clear default factory cache."""
    default_factory.clear_cache()


def get_service_info() -> dict:
    """Get service configuration information."""
    return default_factory.get_service_info()