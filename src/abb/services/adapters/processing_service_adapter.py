"""Qt adapter for ProcessingServiceCore service.

This adapter provides Qt signal-based interfaces while delegating all processing
operations to the pure Python ProcessingServiceCore implementation.
"""

import logging
from typing import Any, Dict, List, Optional

from PySide6.QtCore import Signal

from ..core.path_service_core import PathService<PERSON><PERSON>
from ..core.processing_service_core import ProcessingServiceCore
from ..interfaces import IPathService
from .base_adapter import QtServiceAdapterBase

logger = logging.getLogger(__name__)


class QtProcessingServiceAdapter(QtServiceAdapterBase[ProcessingServiceCore]):
    """Qt adapter for ProcessingServiceCore providing signal-based communication.
    
    This adapter maintains complete backwards compatibility with the existing
    ProcessingService while delegating all business logic to ProcessingServiceCore.
    
    The adapter handles the complex threading, progress reporting, and cancellation
    patterns required for audio processing operations.
    
    Signals:
        progress: Emitted with percentage (0-100) during processing
        finished: Emitted with output file path on successful completion
        error: Emitted with error message on processing failure
        status: Emitted with status messages during processing
    """
    
    # Qt signals matching original ProcessingService
    progress = Signal(int)           # Progress percentage 0-100
    finished = Signal(str)           # Output file path on completion
    error = Signal(str)              # Error messages
    status = Signal(str)             # Status updates during processing
    
    def __init__(self, core_service: Optional[ProcessingServiceCore] = None,
                 path_service: Optional[IPathService] = None):
        """Initialize the processing service adapter.
        
        Args:
            core_service: Optional ProcessingServiceCore instance. If not provided,
                         a new instance will be created with the path_service.
            path_service: Optional IPathService implementation. If not provided,
                         a new PathServiceCore will be created.
        """
        # Create path service if not provided
        if path_service is None:
            path_service = PathServiceCore()
            
        # Create core service if not provided
        if core_service is None:
            core_service = ProcessingServiceCore(path_service)
            
        super().__init__(core_service)
        
        # Initialize state tracking
        self._is_processing = False
        self._current_operation = None
        
    def initialize_adapter(self) -> None:
        """Initialize the adapter by setting up core service callbacks."""
        logger.debug("QtProcessingServiceAdapter initialized")
        
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources."""
        # Cancel any ongoing processing
        if self._is_processing:
            self.cancel()
            
        logger.debug("QtProcessingServiceAdapter cleaned up")
        
    def process_full(self, input_files: List[str], output_path: str,
                     output_filename: str, metadata: Dict[str, Any],
                     settings: Dict[str, Any]) -> None:
        """Process audio files into audiobook format (full processing).
        
        This method delegates to the core service's process method with
        appropriate callback registration for Qt signal emission.
        
        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            if self._is_processing:
                self.emit_signal_safe(self.error, "Processing already in progress")
                return
                
            self._is_processing = True
            self._current_operation = "full"
            
            # Define callbacks
            def on_progress(percentage: int) -> None:
                self.emit_signal_safe(self.progress, percentage)
                
            def on_status(message: str) -> None:
                self.emit_signal_safe(self.status, message)
                
            def on_complete(output_file: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.finished, output_file)
                
            def on_error(error_msg: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.error, error_msg)
                
            # Start processing
            core_service.process(
                input_files=input_files,
                output_path=output_path,
                output_filename=output_filename,
                metadata=metadata,
                settings=settings,
                progress_callback=on_progress,
                status_callback=on_status,
                complete_callback=on_complete,
                error_callback=on_error
            )
            
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "process_full")
            
    def start_processing(self, file_list: List[str], output_settings: Dict[str, Any],
                        metadata: Dict[str, Any], cover_art_path: Optional[str] = None) -> None:
        """Start processing with validation and setup (main entry point).
        
        This method performs validation and setup before delegating to process_full.
        It maintains compatibility with the original ProcessingService interface.
        
        Args:
            file_list: List of input audio files
            output_settings: Dictionary containing output configuration
            metadata: Metadata to embed
            cover_art_path: Optional path to cover art image
        """
        try:
            # Extract settings
            output_dir = output_settings.get("output_directory", "")
            output_filename = output_settings.get("output_filename", "output.m4b")
            
            # Add cover art to metadata if provided
            if cover_art_path:
                metadata = metadata.copy()
                metadata["cover_art_path"] = cover_art_path
                
            # Process settings for core service
            settings = {
                "bitrate": output_settings.get("output_bitrate", 64),
                "sample_rate": output_settings.get("output_sample_rate", "auto"),
                "channels": output_settings.get("output_channels", 1),
                "use_subdirectory": output_settings.get("output_create_subdirectory", True),
                "filename_pattern": output_settings.get("output_filename_pattern", 0),
            }
            
            # Delegate to process_full
            self.process_full(
                input_files=file_list,
                output_path=output_dir,
                output_filename=output_filename,
                metadata=metadata,
                settings=settings
            )
            
        except Exception as e:
            self.handle_service_error(e, "start_processing")
            
    def process_preview(self, input_file_path: str, metadata: Dict[str, Any],
                       settings: Dict[str, Any], temp_cover_path: Optional[str] = None,
                       duration_seconds: int = 30) -> None:
        """Generate a preview of the processed audio.
        
        Args:
            input_file_path: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            temp_cover_path: Optional temporary cover art path
            duration_seconds: Length of preview in seconds
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            if self._is_processing:
                self.emit_signal_safe(self.error, "Processing already in progress")
                return
                
            self._is_processing = True
            self._current_operation = "preview"
            
            # Add cover art to metadata if provided
            if temp_cover_path:
                metadata = metadata.copy()
                metadata["cover_art_path"] = temp_cover_path
                
            # Define callbacks
            def on_complete(output_file: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.finished, output_file)
                
            def on_error(error_msg: str) -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.error, error_msg)
                
            # Start preview processing
            core_service.process_preview(
                input_file=input_file_path,
                metadata=metadata,
                settings=settings,
                duration_seconds=duration_seconds,
                complete_callback=on_complete,
                error_callback=on_error
            )
            
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "process_preview")
            
    def cancel(self) -> None:
        """Cancel current processing operation."""
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            def on_success() -> None:
                self._is_processing = False
                self._current_operation = None
                self.emit_signal_safe(self.status, "Processing cancelled")
                
            def on_error(error_msg: str) -> None:
                logger.error(f"Failed to cancel processing: {error_msg}")
                # Still mark as not processing even if cancel fails
                self._is_processing = False
                self._current_operation = None
                
            core_service.cancel(on_success, on_error)
            
        except Exception as e:
            self._is_processing = False
            self._current_operation = None
            self.handle_service_error(e, "cancel")
            
    def cleanup_worker_resources(self) -> None:
        """Clean up worker thread resources.
        
        This method ensures proper cleanup of background threads and processes.
        It's called during service shutdown or when processing is complete.
        """
        try:
            # Cancel any ongoing processing
            if self._is_processing:
                self.cancel()
                
            # Core service handles its own cleanup
            core_service = self.get_core_service()
            if core_service:
                # ProcessingServiceCore cleans up in its own methods
                logger.debug("Worker resources cleaned up via core service")
                
        except Exception as e:
            logger.error(f"Error cleaning up worker resources: {e}")
            
    def is_processing(self) -> bool:
        """Check if processing is currently active.
        
        Returns:
            True if processing is active, False otherwise
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                return core_service.is_processing()
            return False
        except Exception:
            return self._is_processing
            
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from core service operations.
        
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
        """
        error_msg = str(error)
        if context:
            error_msg = f"{context}: {error_msg}"
            
        logger.error(f"QtProcessingServiceAdapter error: {error_msg}")
        self.emit_signal_safe(self.error, error_msg)
        
        # Reset processing state on error
        self._is_processing = False
        self._current_operation = None
        
        # Call parent error handler
        super().handle_service_error(error, context)
        
    def _get_ffmpeg_path(self) -> str:
        """Get FFmpeg executable path.
        
        This method is for compatibility with code that might access it directly.
        The actual FFmpeg path is managed by the core service.
        
        Returns:
            Path to FFmpeg executable
        """
        # Import here to avoid circular dependency
        from ..ffmpeg_utils import _get_executable_path
        return _get_executable_path("ffmpeg")