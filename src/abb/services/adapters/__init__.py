"""Qt Adapter layer for ABB services.

This module contains Qt adapter classes that wrap pure Python core services,
providing Qt signal-based interfaces while delegating business logic to
pure Python implementations.

The adapter pattern enables:
- Backwards compatibility with existing Qt-based UI code
- Feature flag-based migration between Qt and pure Python services
- Easier testing of business logic without Qt dependencies
- Cleaner separation between UI framework and business logic

Adapters implemented:
- QtFileServiceAdapter: File management with Qt signals
- QtSettingsManagerAdapter: Settings with Qt signal notifications
- QtMetadataHandlerAdapter: Metadata operations with Qt signals
- QtProcessingServiceAdapter: Audio processing with Qt progress signals
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .base_adapter import QtServiceAdapterBase
    from .file_service_adapter import QtFileServiceAdapter
    from .metadata_handler_adapter import QtMetadataHandlerAdapter
    from .processing_service_adapter import QtProcessingServiceAdapter
    from .settings_manager_adapter import QtSettingsManagerAdapter

from .base_adapter import QtServiceAdapterBase
from .processing_service_adapter import QtProcessingServiceAdapter
from .settings_manager_adapter import QtSettingsManagerAdapter

__all__ = [
    "QtServiceAdapterBase",
    "QtFileServiceAdapter",
    "QtSettingsManagerAdapter", 
    "QtMetadataHandlerAdapter",
    "QtProcessingServiceAdapter",
]