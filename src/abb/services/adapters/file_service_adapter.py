"""QtFileServiceAdapter - Qt adapter for FileServiceCore.

This module provides a Qt adapter that wraps a FileServiceCore instance,
maintaining complete backwards compatibility with the existing FileService
while delegating business logic to the pure Python core service.
"""

from typing import List, Optional

from PySide6.QtCore import QObject, QTimer, Signal

from ..core.file_service_core import FileServiceCore


class QtFileServiceAdapter(QObject):
    """Qt adapter for FileServiceCore that maintains FileService compatibility.
    
    This adapter wraps a FileServiceCore instance and provides the same Qt signals
    and method signatures as the original FileService, enabling seamless migration
    between Qt-dependent and pure Python service implementations.
    
    Key features:
    - Complete backwards compatibility with existing FileService interface
    - Thread-safe signal emissions using Qt's queued connections
    - Automatic callback-to-signal translation
    - Error handling that maintains existing behavior patterns
    - No data copying overhead in method delegation
    """
    
    # Qt signals - identical to original FileService
    files_changed = Signal(list)                # Signal emitted when the file list changes
    combined_size_changed_signal = Signal(str)  # Signal for combined size updates
    
    def __init__(self, core_service: Optional[FileServiceCore] = None):
        """Initialize the adapter with a FileServiceCore instance.
        
        Args:
            core_service: FileServiceCore instance to wrap. If None, creates a new one.
        """
        super().__init__()
        self._core = core_service or FileServiceCore()
        
        # Initialize timers as None, will be created if needed
        self._files_changed_timer: Optional[QTimer] = None
        self._size_changed_timer: Optional[QTimer] = None
        
        # Cached data for signal emission
        self._cached_files: List[str] = []
        self._cached_size: str = ""
    
    def get_files(self) -> List[str]:
        """Get the current list of files.
        
        Returns:
            List of file paths currently managed by the service.
        """
        return self._core.get_files()
    
    def get_combined_size(self) -> int:
        """Get the combined size of all files in bytes.
        
        Returns:
            Total size in bytes of all managed files.
        """
        return self._core.get_combined_size()
    
    def format_combined_size(self) -> str:
        """Get the combined size formatted for display.
        
        Returns:
            Human-readable size string (e.g., "1.5 MB", "2.3 GB").
        """
        return self._core.format_combined_size()
    
    def get_file_count(self) -> int:
        """Get the number of files currently managed.
        
        Returns:
            Number of files in the managed list.
        """
        return self._core.get_file_count()
    
    def clear_files(self) -> None:
        """Clear all files from the managed list.
        
        Maintains backwards compatibility by not requiring callbacks.
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, log error but don't raise
            # The original FileService didn't have error handling for clear
            print(f"Warning: Failed to clear files: {error_msg}")
        
        self._core.clear_files(on_success, on_error)
    
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the list, normalizing paths and filtering by extension.
        
        Args:
            paths: List of file paths to add
            
        Returns:
            List of file paths that were actually added
        """
        added_files: List[str] = []
        
        def on_success(successfully_added: List[str]) -> None:
            nonlocal added_files
            added_files = successfully_added
            if successfully_added:
                self._queue_files_changed_signal()
                self._queue_size_changed_signal()
        
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, don't raise errors during add_files
            # The original FileService silently skipped problematic files
            pass
        
        # Call core service - callbacks are called synchronously
        self._core.add_files(paths, on_success, on_error)
        
        return added_files
    
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        
        Args:
            index: Index of the file to remove
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, silently handle invalid indices
            # The original FileService had bounds checking but no error propagation
            pass
        
        self._core.remove_file(index, on_success, on_error)
    
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files by setting a new order based on provided file paths.
        
        Args:
            new_order_paths: List of file paths representing the new order
        """
        def on_success() -> None:
            self._queue_files_changed_signal()
            self._queue_size_changed_signal()
        
        def on_error(error_msg: str) -> None:
            # For backwards compatibility, silently handle reorder errors
            # The original FileService assumed valid input
            pass
        
        self._core.reorder_files(new_order_paths, on_success, on_error)
    
    def _queue_files_changed_signal(self) -> None:
        """Queue emission of files_changed signal in a thread-safe manner."""
        self._cached_files = self._core.get_files()
        
        # Try to use QTimer if possible, otherwise emit directly
        try:
            if self._files_changed_timer is None:
                self._files_changed_timer = QTimer()
                self._files_changed_timer.setSingleShot(True)
                self._files_changed_timer.timeout.connect(self._emit_files_changed)
            
            if not self._files_changed_timer.isActive():
                self._files_changed_timer.start(0)
        except (RuntimeError, AttributeError):
            # Qt event loop not running or other issue, emit signal directly
            self._emit_files_changed()
    
    def _queue_size_changed_signal(self) -> None:
        """Queue emission of combined_size_changed_signal in a thread-safe manner."""
        self._cached_size = self._core.format_combined_size()
        
        # Try to use QTimer if possible, otherwise emit directly
        try:
            if self._size_changed_timer is None:
                self._size_changed_timer = QTimer()
                self._size_changed_timer.setSingleShot(True)
                self._size_changed_timer.timeout.connect(self._emit_size_changed)
                
            if not self._size_changed_timer.isActive():
                self._size_changed_timer.start(0)
        except (RuntimeError, AttributeError):
            # Qt event loop not running or other issue, emit signal directly
            self._emit_size_changed()
    
    def _emit_files_changed(self) -> None:
        """Emit the files_changed signal with cached data."""
        # Create a copy to match original FileService behavior
        self.files_changed.emit(self._cached_files.copy())
    
    def _emit_size_changed(self) -> None:
        """Emit the combined_size_changed_signal with cached formatted size."""
        self.combined_size_changed_signal.emit(self._cached_size)
    
    def validate_files(self) -> None:
        """Validate that all managed files still exist and are accessible.
        
        Removes any files that are no longer accessible and emits signals if changes occur.
        This is an additional method not in the original FileService but useful for adapters.
        """
        def on_success(removed_files: List[str]) -> None:
            if removed_files:
                # Files were removed, emit change signals
                self._queue_files_changed_signal()
                self._queue_size_changed_signal()
        
        def on_error(error_msg: str) -> None:
            # Log validation errors but don't propagate them
            print(f"Warning: File validation failed: {error_msg}")
        
        self._core.validate_files(on_success, on_error)
    
    # Private methods that mirror the original FileService for internal consistency
    def _emit_combined_size(self) -> None:
        """Legacy method name for backwards compatibility.
        
        This method maintains the same name as the original FileService
        to ensure any subclasses or extensions continue to work.
        """
        self._queue_size_changed_signal()
    
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        
        This delegates to the core service's normalization logic.
        Maintained for backwards compatibility with any code that calls this directly.
        
        Args:
            path: File path to normalize
            
        Returns:
            Normalized path
            
        Raises:
            ServiceError: If path cannot be normalized
        """
        return self._core._normalize_path(path)
    
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        
        This delegates to the core service's validation logic.
        Maintained for backwards compatibility.
        
        Args:
            path: File path to check
            
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        return self._core._is_valid_audio_file(path)