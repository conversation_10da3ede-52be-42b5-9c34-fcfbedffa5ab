"""Qt adapter for SettingsManagerCore that provides Qt signal-based interface."""

import logging
from typing import Any

from PySide6.QtCore import QObject, Signal

from ..core.settings_manager_core import SettingsManagerCore

logger = logging.getLogger(__name__)


class QtSettingsManagerAdapter(QObject):
    """Qt adapter for SettingsManagerCore that maintains backwards compatibility.
    
    This adapter wraps a SettingsManagerCore instance and provides the exact same
    interface as the original SettingsManager, including Qt signals. All business
    logic is delegated to the pure Python core service.
    
    Provides:
    - Identical Qt signals to original SettingsManager
    - Same method signatures for complete backwards compatibility  
    - Thread-safe operations via core service
    - Callback-to-signal translation
    """
    
    # Qt signal matching original SettingsManager exactly
    settings_changed = Signal(str, object)  # Setting key, new value
    
    def __init__(self, settings_file_path, default_settings=None):
        """Initialize the Qt settings manager adapter.
        
        Args:
            settings_file_path: Path to the JSON settings file.
            default_settings: Optional dictionary of default settings.
        """
        super().__init__()
        
        # Create core service instance
        self._core = SettingsManagerCore(settings_file_path, default_settings)
        
        # Store for backwards compatibility
        self.settings_file_path = settings_file_path
        
    def get_setting(self, name, default_value=None):
        """Get a setting value by name.
        
        Args:
            name: Setting name to retrieve.
            default_value: Value to return if setting not found.
            
        Returns:
            Setting value or default_value if not found.
        """
        return self._core.get_setting(name, default_value)
    
    def set_setting(self, name, value):
        """Set a setting value with validation.
        
        This method maintains the exact signature of the original SettingsManager
        for backwards compatibility, handling callback-to-signal translation internally.
        
        Args:
            name: Setting name to update.
            value: New value for the setting.
        """
        def success_callback(setting_name: str, setting_value: Any) -> None:
            """Handle successful setting update by emitting Qt signal."""
            self.settings_changed.emit(setting_name, setting_value)
        
        def error_callback(error_message: str) -> None:
            """Handle setting validation/save errors.
            
            Original SettingsManager logged errors but did not raise exceptions,
            so we maintain that behavior.
            """
            # Error already logged in core service, no additional action needed
            # This maintains the original behavior where set_setting never raised
            pass
        
        # Delegate to core service with callback translation
        self._core.set_setting(name, value, success_callback, error_callback)
    
    def get_all_settings(self):
        """Get all current settings.
        
        Returns:
            Dictionary containing all settings.
        """
        return self._core.get_all_settings()
    
    # Legacy methods for backwards compatibility
    # These are now handled by the core service, but we maintain the interface
    
    def _load_settings(self):
        """Load settings from JSON file.
        
        This method is maintained for backwards compatibility but is now 
        handled automatically by the core service during initialization.
        Calling this method is a no-op since the core service manages loading.
        """
        # Core service handles loading automatically
        # This method exists only for backwards compatibility
        pass
    
    def _save_settings(self):
        """Save current settings to JSON file.
        
        This method is maintained for backwards compatibility but is now
        handled automatically by the core service during set_setting operations.
        Calling this method is a no-op since the core service manages saving.
        """
        # Core service handles saving automatically in set_setting
        # This method exists only for backwards compatibility  
        pass
    
    def _validate_setting(self, name, value):
        """Validate a setting value based on its name.
        
        This method is maintained for backwards compatibility but validation
        is now handled by the core service. This method delegates to the core
        for consistency.
        
        Args:
            name: Setting name to validate.
            value: Value to validate.
            
        Returns:
            True if valid, False otherwise.
        """
        return self._core._validate_setting(name, value)