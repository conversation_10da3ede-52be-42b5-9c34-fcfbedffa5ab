"""Base adapter class for Qt service adapters.

This module provides QtServiceAdapterBase, a foundation class for all Qt service
adapters that bridge pure Python services with Qt signal/slot mechanisms.
"""

import logging
import threading
import weakref
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor
from typing import Callable, Dict, Generic, List, Optional, Set, TypeVar

from PySide6.QtCore import QMetaObject, QObject, Qt, QThread, QTimer, Signal

logger = logging.getLogger(__name__)

# Type variables for generic service types
ServiceType = TypeVar('ServiceType')
CallbackType = Callable[..., None]


# Create a metaclass that combines Qt's metaclass with ABC
class QtAdapterMeta(type(QObject), type(ABC)):
    """Metaclass that resolves the conflict between Qt and ABC metaclasses."""
    pass


class QtServiceAdapterBase(QObject, Generic[ServiceType], metaclass=QtAdapterMeta):
    """Base class for Qt service adapters.
    
    Provides common patterns and utilities for bridging pure Python services
    with Qt signal/slot mechanisms. Handles thread safety, error propagation,
    callback management, and resource cleanup.
    
    Features:
    - Thread-safe signal emission
    - Callback registration and management
    - Error handling and propagation
    - Resource cleanup and memory management
    - Weak reference tracking to prevent cycles
    - Logging integration for debugging
    """
    
    # Common error signal that all adapters can use
    error_occurred = Signal(str, str)  # error_type, error_message
    
    # Status signals for adapter lifecycle
    adapter_initialized = Signal()
    adapter_destroyed = Signal()
    
    def __init__(self, core_service: ServiceType, parent: Optional[QObject] = None):
        """Initialize the Qt service adapter.
        
        Args:
            core_service: The pure Python service to adapt
            parent: Optional Qt parent object
        """
        super().__init__(parent)
        
        # Store weak reference to core service to prevent cycles
        self._core_service_ref = weakref.ref(core_service)
        
        # Callback management
        self._callbacks: Dict[str, List[weakref.ref]] = {}
        self._callback_lock = threading.RLock()
        
        # Error handling
        self._error_handlers: Set[weakref.ref] = set()
        
        # Thread safety
        self._signal_queue_timer = QTimer()
        self._signal_queue_timer.timeout.connect(self._process_queued_signals)
        self._signal_queue_timer.setSingleShot(True)
        self._signal_queue: List[tuple] = []
        self._signal_queue_lock = threading.Lock()
        
        # Resource tracking
        self._is_destroyed = False
        self._cleanup_callbacks: List[Callable[[], None]] = []
        
        # Thread pool for async operations
        self._thread_pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="QtAdapter")
        
        # Setup logging context
        self._logger = logger.getChild(self.__class__.__name__)
        
        self._logger.debug(f"Initialized {self.__class__.__name__}")
        self.adapter_initialized.emit()
    
    @property
    def core_service(self) -> Optional[ServiceType]:
        """Get the core service, returns None if it has been garbage collected."""
        return self._core_service_ref()
    
    @property
    def is_destroyed(self) -> bool:
        """Check if the adapter has been destroyed."""
        return self._is_destroyed
    
    def register_callback(self, event_name: str, callback: CallbackType) -> None:
        """Register a callback for a specific event.
        
        Args:
            event_name: Name of the event to listen for
            callback: Callback function to register
        """
        if self._is_destroyed:
            self._logger.warning(f"Attempted to register callback on destroyed adapter: {event_name}")
            return
        
        with self._callback_lock:
            if event_name not in self._callbacks:
                self._callbacks[event_name] = []
            
            # Store weak reference to prevent memory leaks
            callback_ref = weakref.ref(callback)
            self._callbacks[event_name].append(callback_ref)
            
            self._logger.debug(f"Registered callback for event: {event_name}")
    
    def unregister_callback(self, event_name: str, callback: CallbackType) -> bool:
        """Unregister a callback for a specific event.
        
        Args:
            event_name: Name of the event
            callback: Callback function to unregister
            
        Returns:
            True if callback was found and removed, False otherwise
        """
        with self._callback_lock:
            if event_name not in self._callbacks:
                return False
            
            # Find and remove the callback
            for i, callback_ref in enumerate(self._callbacks[event_name]):
                if callback_ref() is callback:
                    del self._callbacks[event_name][i]
                    self._logger.debug(f"Unregistered callback for event: {event_name}")
                    return True
            
            return False
    
    def emit_to_callbacks(self, event_name: str, *args, **kwargs) -> None:
        """Emit an event to all registered callbacks.
        
        Args:
            event_name: Name of the event to emit
            *args: Positional arguments for callbacks
            **kwargs: Keyword arguments for callbacks
        """
        if self._is_destroyed:
            return
        
        with self._callback_lock:
            if event_name not in self._callbacks:
                return
            
            # Clean up dead references and call live ones
            live_callbacks = []
            for callback_ref in self._callbacks[event_name]:
                callback = callback_ref()
                if callback is not None:
                    live_callbacks.append(callback_ref)
                    try:
                        callback(*args, **kwargs)
                    except Exception as e:
                        self._handle_callback_error(event_name, callback, e)
            
            # Update the list to remove dead references
            self._callbacks[event_name] = live_callbacks
    
    def emit_signal_safe(self, signal: Signal, *args) -> None:
        """Thread-safe signal emission.
        
        If called from the main thread, emits immediately.
        If called from another thread, queues for emission on main thread.
        
        Args:
            signal: Qt signal to emit
            *args: Arguments for the signal
        """
        if self._is_destroyed:
            return
        
        if QThread.currentThread() == self.thread():
            # We're on the main thread, emit directly
            try:
                signal.emit(*args)
            except Exception as e:
                self._logger.error(f"Error emitting signal {signal}: {e}")
                self._emit_error("signal_emission", str(e))
        else:
            # We're on a different thread, queue for main thread
            with self._signal_queue_lock:
                self._signal_queue.append((signal, args))
            
            # Use QMetaObject to invoke timer start on main thread
            QMetaObject.invokeMethod(
                self._signal_queue_timer,
                "start",
                Qt.ConnectionType.QueuedConnection,
                10  # 10ms delay
            )
    
    def _process_queued_signals(self) -> None:
        """Process queued signals on the main thread."""
        with self._signal_queue_lock:
            signals_to_emit = self._signal_queue.copy()
            self._signal_queue.clear()
        
        for signal, args in signals_to_emit:
            try:
                signal.emit(*args)
            except Exception as e:
                self._logger.error(f"Error emitting queued signal {signal}: {e}")
                self._emit_error("queued_signal_emission", str(e))
    
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from the core service.
        
        Args:
            error: The exception that occurred
            context: Additional context about where the error occurred
        """
        error_msg = f"{context}: {str(error)}" if context else str(error)
        error_type = error.__class__.__name__
        
        self._logger.error(f"Service error [{error_type}]: {error_msg}")
        self._emit_error(error_type, error_msg)
        
        # Call registered error handlers
        for handler_ref in list(self._error_handlers):
            handler = handler_ref()
            if handler is not None:
                try:
                    handler(error, context)
                except Exception as e:
                    self._logger.error(f"Error in error handler: {e}")
            else:
                # Remove dead reference
                self._error_handlers.discard(handler_ref)
    
    def register_error_handler(self, handler: Callable[[Exception, str], None]) -> None:
        """Register an error handler for service errors.
        
        Args:
            handler: Function to call when errors occur
        """
        self._error_handlers.add(weakref.ref(handler))
    
    def add_cleanup_callback(self, callback: Callable[[], None]) -> None:
        """Add a callback to be called during cleanup.
        
        Args:
            callback: Function to call during cleanup
        """
        self._cleanup_callbacks.append(callback)
    
    def run_async(self, func: Callable, *args, **kwargs) -> None:
        """Run a function asynchronously in the thread pool.
        
        Args:
            func: Function to run
            *args: Positional arguments
            **kwargs: Keyword arguments
        """
        if self._is_destroyed:
            return
        
        def wrapped_func():
            try:
                return func(*args, **kwargs)
            except Exception as e:
                self._logger.error(f"Error in async function {func.__name__}: {e}")
                self.emit_signal_safe(self.error_occurred, "async_error", str(e))
        
        self._thread_pool.submit(wrapped_func)
    
    def _emit_error(self, error_type: str, error_message: str) -> None:
        """Emit error signal in a thread-safe manner.
        
        Args:
            error_type: Type/category of the error
            error_message: Detailed error message
        """
        self.emit_signal_safe(self.error_occurred, error_type, error_message)
    
    def _handle_callback_error(self, event_name: str, callback: CallbackType, error: Exception) -> None:
        """Handle errors that occur in callbacks.
        
        Args:
            event_name: Name of the event that triggered the callback
            callback: The callback that failed
            error: The exception that occurred
        """
        callback_name = getattr(callback, '__name__', str(callback))
        error_msg = f"Callback {callback_name} for event {event_name} failed: {error}"
        self._logger.error(error_msg)
        self._emit_error("callback_error", error_msg)
    
    @abstractmethod
    def initialize_adapter(self) -> None:
        """Initialize the adapter's specific functionality.
        
        This method should be implemented by subclasses to set up
        their specific signal connections and service integrations.
        """
        pass
    
    @abstractmethod
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources.
        
        This method should be implemented by subclasses to clean up
        their specific resources and connections.
        """
        pass
    
    def cleanup(self) -> None:
        """Clean up all adapter resources.
        
        This method handles the complete cleanup of the adapter,
        including calling subclass cleanup methods and clearing
        all internal state.
        """
        if self._is_destroyed:
            return
        
        self._logger.debug(f"Starting cleanup of {self.__class__.__name__}")
        self._is_destroyed = True
        
        try:
            # Call subclass cleanup
            self.cleanup_adapter()
        except Exception as e:
            self._logger.error(f"Error in adapter cleanup: {e}")
        
        # Run cleanup callbacks
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                self._logger.error(f"Error in cleanup callback: {e}")
        
        # Clean up internal state
        with self._callback_lock:
            self._callbacks.clear()
        
        self._error_handlers.clear()
        self._cleanup_callbacks.clear()
        
        # Stop and clean up timer
        if self._signal_queue_timer.isActive():
            self._signal_queue_timer.stop()
        
        # Clean up signal queue
        with self._signal_queue_lock:
            self._signal_queue.clear()
        
        # Shutdown thread pool
        self._thread_pool.shutdown(wait=False)
        
        self._logger.debug(f"Completed cleanup of {self.__class__.__name__}")
        self.adapter_destroyed.emit()
    
    def __del__(self):
        """Ensure cleanup is called when adapter is garbage collected."""
        if not self._is_destroyed:
            self.cleanup()


class QtServiceAdapterMixin:
    """Mixin class providing common adapter utilities.
    
    Can be used with adapters that don't inherit from QtServiceAdapterBase
    but still need some of its functionality.
    """
    
    @staticmethod
    def create_error_handler(adapter: QtServiceAdapterBase, 
                           error_signal: Signal) -> Callable[[Exception, str], None]:
        """Create a standard error handler that emits to a specific signal.
        
        Args:
            adapter: The adapter instance
            error_signal: Signal to emit errors to
            
        Returns:
            Error handler function
        """
        def handle_error(error: Exception, context: str = ""):
            error_type = error.__class__.__name__
            error_msg = f"{context}: {str(error)}" if context else str(error)
            adapter.emit_signal_safe(error_signal, error_type, error_msg)
        
        return handle_error
    
    @staticmethod
    def create_callback_wrapper(adapter: QtServiceAdapterBase,
                               signal: Signal) -> Callable[..., None]:
        """Create a callback wrapper that safely emits to a Qt signal.
        
        Args:
            adapter: The adapter instance
            signal: Signal to emit to
            
        Returns:
            Callback function that emits to the signal
        """
        def callback_wrapper(*args, **kwargs):
            # Convert kwargs to args if needed, as Qt signals don't handle kwargs well
            if kwargs:
                # Log warning about kwargs being dropped
                adapter._logger.warning(f"Dropping kwargs in signal emission: {kwargs}")
            
            adapter.emit_signal_safe(signal, *args)
        
        return callback_wrapper