"""Qt adapter for MetadataHandlerCore service.

This adapter provides Qt signal-based interfaces while delegating all metadata
operations to the pure Python MetadataHandlerCore implementation.
"""

import logging
from typing import Any, Dict, Optional

from PySide6.QtCore import Signal

from ..core.metadata_handler_core import MetadataHandlerCore
from .base_adapter import QtServiceAdapterBase

logger = logging.getLogger(__name__)


class QtMetadataHandlerAdapter(QtServiceAdapterBase[MetadataHandlerCore]):
    """Qt adapter for MetadataHandlerCore providing signal-based communication.
    
    This adapter maintains complete backwards compatibility with the existing
    MetadataService while delegating all business logic to MetadataHandlerCore.
    
    Signals:
        metadata_loaded: Emitted when metadata is loaded from a file
        metadata_updated: Emitted when metadata fields are updated
        metadata_error: Emitted when errors occur during operations
    """
    
    # Qt signals matching original MetadataService
    metadata_loaded = Signal(dict)    # File loading operations
    metadata_updated = Signal(dict)   # Metadata changes and updates
    metadata_error = Signal(str)      # Error handling
    
    def __init__(self, core_service: Optional[MetadataHandlerCore] = None):
        """Initialize the metadata handler adapter.
        
        Args:
            core_service: Optional MetadataHandlerCore instance. If not provided,
                         a new instance will be created.
        """
        # Create core service if not provided
        if core_service is None:
            core_service = MetadataHandlerCore()
            
        super().__init__(core_service)
        
        # Initialize state properties for compatibility
        self.current_metadata: Optional[dict] = None
        self.current_cover_art_data: Optional[bytes] = None
        self.current_cover_art_path: Optional[str] = None
        
    def initialize_adapter(self) -> None:
        """Initialize the adapter by setting up core service callbacks."""
        # The core service methods will be called directly with callbacks
        # No persistent callback registration needed for MetadataHandlerCore
        logger.debug("QtMetadataHandlerAdapter initialized")
        
    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources."""
        # Clear state
        self.current_metadata = None
        self.current_cover_art_data = None
        self.current_cover_art_path = None
        logger.debug("QtMetadataHandlerAdapter cleaned up")
        
    def get_metadata(self) -> Dict[str, Any]:
        """Get current metadata dictionary.
        
        Returns:
            Copy of current metadata dictionary
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                metadata = core_service.get_current_metadata()
                self.current_metadata = metadata.copy() if metadata else None
                return metadata
            return {}
        except Exception as e:
            self.handle_service_error(e, "get_metadata")
            return {}
            
    def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract metadata from an audio file.
        
        This method is synchronous for backwards compatibility but uses
        the core service's async capabilities internally.
        
        Args:
            file_path: Path to audio file
            
        Returns:
            Extracted metadata dictionary
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return {}
                
            result = {}
            error = None
            
            def on_success(metadata: Dict[str, Any]) -> None:
                nonlocal result
                result = metadata
                
            def on_error(error_msg: str) -> None:
                nonlocal error
                error = error_msg
                
            # Call core service method
            core_service.load_from_file(file_path, on_success, on_error)
            
            if error:
                self.emit_signal_safe(self.metadata_error, error)
                return {}
                
            return result
            
        except Exception as e:
            self.handle_service_error(e, "extract_metadata")
            return {}
            
    def apply_defaults(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Apply default values to metadata.
        
        Args:
            metadata: Metadata dictionary to apply defaults to
            
        Returns:
            Metadata with defaults applied
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return metadata
                
            # Update core service metadata and apply defaults
            for field, value in metadata.items():
                core_service.update_field(field, value, lambda _: None)
                
            # Apply defaults
            result = {}
            
            def on_success(updated_metadata: Dict[str, Any]) -> None:
                nonlocal result
                result = updated_metadata
                
            core_service.apply_defaults(on_success)
            return result
            
        except Exception as e:
            self.handle_service_error(e, "apply_defaults")
            return metadata
            
    def update_metadata_field(self, field: str, value: Any) -> None:
        """Update a single metadata field.
        
        Args:
            field: Field name to update
            value: New value for the field
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            def on_success(metadata: Dict[str, Any]) -> None:
                self.current_metadata = metadata.copy()
                self.emit_signal_safe(self.metadata_updated, metadata)
                
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
                
            core_service.update_field(field, value, on_success, on_error)
            
        except Exception as e:
            self.handle_service_error(e, "update_metadata_field")
            
    def clear_metadata(self) -> None:
        """Clear all metadata."""
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            def on_success() -> None:
                self.current_metadata = None
                self.current_cover_art_data = None
                self.current_cover_art_path = None
                self.emit_signal_safe(self.metadata_updated, {})
                
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
                
            core_service.clear_metadata(on_success, on_error)
            
        except Exception as e:
            self.handle_service_error(e, "clear_metadata")
            
    def extract_and_load_metadata(self, filepath: str) -> None:
        """Extract metadata and cover art from a file and load it.
        
        This method combines metadata extraction and cover art extraction,
        emitting appropriate signals for UI updates.
        
        Args:
            filepath: Path to audio file
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            def on_metadata_loaded(metadata: Dict[str, Any]) -> None:
                self.current_metadata = metadata.copy()
                # Extract cover art
                cover_data = core_service.get_cover_art_data()
                self.current_cover_art_data = cover_data
                self.current_cover_art_path = core_service.get_cover_art_path()
                
                # Emit signal with metadata
                self.emit_signal_safe(self.metadata_loaded, metadata)
                
            def on_error(error_msg: str) -> None:
                logger.error(f"Failed to extract metadata from {filepath}: {error_msg}")
                self.emit_signal_safe(self.metadata_error, error_msg)
                
            # Load metadata from file
            core_service.load_from_file(filepath, on_metadata_loaded, on_error)
            
        except Exception as e:
            self.handle_service_error(e, "extract_and_load_metadata")
            
    def update_current_metadata(self, field_name: str, new_value: str) -> None:
        """Update current metadata field and emit signal.
        
        Args:
            field_name: Metadata field to update
            new_value: New value for the field
        """
        # This is an alias for update_metadata_field for compatibility
        self.update_metadata_field(field_name, new_value)
        
    def set_cover_art(self, image_path: str) -> None:
        """Set cover art from image file path.
        
        Args:
            image_path: Path to cover art image file
        """
        try:
            core_service = self.get_core_service()
            if not core_service:
                return
                
            def on_success(path: Optional[str]) -> None:
                self.current_cover_art_path = path
                # Get updated metadata
                metadata = core_service.get_current_metadata()
                self.current_metadata = metadata.copy()
                self.emit_signal_safe(self.metadata_updated, metadata)
                
            def on_error(error_msg: str) -> None:
                self.emit_signal_safe(self.metadata_error, error_msg)
                
            core_service.set_cover_art(image_path, on_success, on_error)
            
        except Exception as e:
            self.handle_service_error(e, "set_cover_art")
            
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art file path.
        
        Returns:
            Path to cover art file or None if no cover art
        """
        try:
            core_service = self.get_core_service()
            if core_service:
                path = core_service.get_cover_art_path()
                self.current_cover_art_path = path
                return path
            return None
        except Exception as e:
            self.handle_service_error(e, "get_cover_art_path")
            return None
            
    def handle_service_error(self, error: Exception, context: str = "") -> None:
        """Handle errors from core service operations.
        
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
        """
        error_msg = str(error)
        if context:
            error_msg = f"{context}: {error_msg}"
            
        logger.error(f"QtMetadataHandlerAdapter error: {error_msg}")
        self.emit_signal_safe(self.metadata_error, error_msg)
        
        # Call parent error handler
        super().handle_service_error(error, context)