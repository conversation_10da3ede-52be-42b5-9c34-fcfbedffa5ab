"""FileService for Audiobook Boss.

Handles file operations like adding, removing, and reordering files.
"""
import os
from pathlib import Path
from typing import List

from PySide6.QtCore import QObject, Signal


class FileService(QObject):
    """Service for managing audio files.
    
    Handles operations like adding, removing, and reordering files.
    Validates file types and normalizes paths.
    Emits signals when the file list changes.
    """
    
    files_changed = Signal(list) # Signal emitted when the file list changes
    combined_size_changed_signal = Signal(str)  # New signal for combined size
    
    def __init__(self):
        """Initialize the FileService with an empty file list."""
        super().__init__()
        self._files = []
        self._valid_extensions = {".mp3", ".m4a", ".m4b", ".aac"}
    
    def get_files(self) -> List[str]:
        """Get the current list of files.
        
        Returns:
            List of file paths
        """
        return self._files.copy()
    
    def get_combined_size(self) -> int:
        """Get the combined size of all files in bytes.
        """
        total = 0
        for f in self._files:
            try:
                total += os.path.getsize(f)
            except Exception:
                pass
        return total
    
    def _emit_combined_size(self):
        size = self.get_combined_size()
        # Format size for display (human readable)
        if size < 1024:
            size_str = f"{size} B"
        elif size < 1024 * 1024:
            size_str = f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            size_str = f"{size / (1024 * 1024):.1f} MB"
        else:
            size_str = f"{size / (1024 * 1024 * 1024):.2f} GB"
        self.combined_size_changed_signal.emit(size_str)
    
    def add_files(self, paths: List[str]) -> List[str]:
        """Add files to the list, normalizing paths and filtering by extension.
        
        Args:
            paths: List of file paths to add
            
        Returns:
            List of file paths that were actually added
        """
        normalized_paths = []
        # Use a set of normalized existing paths for efficient lookup
        existing_paths_lookup = {self._normalize_path(p) for p in self._files}
        
        for path in paths:
            normalized = self._normalize_path(path)
            
            if normalized in existing_paths_lookup: # Check against the lookup set
                continue
                
            if not self._is_valid_audio_file(normalized):
                continue
                
            normalized_paths.append(normalized)
            existing_paths_lookup.add(normalized) # Add to lookup set as well
        
        if normalized_paths:
            self._files.extend(normalized_paths)
            self.files_changed.emit(self._files.copy())
            self._emit_combined_size()
        
        return normalized_paths
    
    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        
        Args:
            index: Index of the file to remove
        """
        if 0 <= index < len(self._files):
            self._files.pop(index)
            self.files_changed.emit(self._files.copy())
            self._emit_combined_size()
    
    def reorder_files(self, new_order_paths: list) -> None:
        """Reorder files by setting a new order based on provided file paths.
        
        Args:
            new_order_paths: List of file paths representing the new order
        """
        self._files = new_order_paths
        self.files_changed.emit(self._files.copy())
        self._emit_combined_size()
    
    def _normalize_path(self, path: str) -> str:
        """Normalize a file path to its absolute, canonical form.
        
        Args:
            path: File path to normalize
            
        Returns:
            Normalized path
        """
        return str(Path(path).resolve())
    
    def _is_valid_audio_file(self, path: str) -> bool:
        """Check if a file has a valid audio extension.
        
        Args:
            path: File path to check
            
        Returns:
            True if the file has a valid audio extension, False otherwise
        """
        ext = Path(path).suffix.lower()
        return ext in self._valid_extensions