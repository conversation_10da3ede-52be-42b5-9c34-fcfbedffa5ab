"""Pure Python Protocol interfaces for ABB services.

This module defines Protocol interfaces that enable service decoupling from Qt dependencies.
Services implementing these protocols can use callback-based communication instead of Qt signals,
allowing for pure Python implementations that are easier to test and reuse.

Feature flag: ABB_PURE_SERVICES controls whether these interfaces are used.
"""

from typing import Any, Callable, Dict, List, Optional, Protocol


class IFileService(Protocol):
    """Protocol for file management operations."""
    
    def get_files(self) -> List[str]:
        """Get current list of files.
        
        Returns:
            List of file paths currently managed by the service.
        """
        ...
    
    def get_combined_size(self) -> int:
        """Get total size of all files in bytes.
        
        Returns:
            Total size in bytes of all managed files.
        """
        ...
    
    def add_files(self, paths: List[str], 
                  success_callback: Callable[[List[str]], None],
                  error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Add files to the managed list.
        
        Args:
            paths: List of file paths to add
            success_callback: Called with list of successfully added files
            error_callback: Called with error message if operation fails
        """
        ...
    
    def remove_file(self, index: int,
                    success_callback: Callable[[], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Remove file by index.
        
        Args:
            index: Index of file to remove
            success_callback: Called when file is successfully removed
            error_callback: Called with error message if operation fails
        """
        ...
    
    def reorder_files(self, new_order: List[str],
                      success_callback: Callable[[], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Reorder files according to new path order.
        
        Args:
            new_order: List of file paths in desired order
            success_callback: Called when reordering is complete
            error_callback: Called with error message if operation fails
        """
        ...


class IMetadataHandler(Protocol):
    """Protocol for metadata operations."""
    
    def get_current_metadata(self) -> Dict[str, Any]:
        """Get current metadata state.
        
        Returns:
            Dictionary containing current metadata.
        """
        ...
    
    def load_from_file(self, path: str,
                       success_callback: Callable[[Dict[str, Any]], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Load metadata from audio file.
        
        Args:
            path: Path to audio file
            success_callback: Called with extracted metadata
            error_callback: Called with error message if operation fails
        """
        ...
    
    def update_field(self, field: str, value: Any,
                     success_callback: Callable[[Dict[str, Any]], None],
                     error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Update a metadata field.
        
        Args:
            field: Metadata field name
            value: New value for the field
            success_callback: Called with updated metadata
            error_callback: Called with error message if operation fails
        """
        ...
    
    def get_for_ffmpeg(self) -> Dict[str, str]:
        """Get metadata formatted for FFmpeg.
        
        Returns:
            Dictionary with FFmpeg-compatible metadata mappings.
        """
        ...
    
    def clear_metadata(self,
                       success_callback: Callable[[], None],
                       error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Clear all metadata.
        
        Args:
            success_callback: Called when metadata is cleared
            error_callback: Called with error message if operation fails
        """
        ...
    
    def set_cover_art(self, path: str,
                      success_callback: Callable[[Optional[str]], None],
                      error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set cover art from file path.
        
        Args:
            path: Path to cover art image file
            success_callback: Called with cover art path (None if cleared)
            error_callback: Called with error message if operation fails
        """
        ...
    
    def get_cover_art_path(self) -> Optional[str]:
        """Get current cover art path.
        
        Returns:
            Path to current cover art file, or None if no cover art set.
        """
        ...


class IProcessingService(Protocol):
    """Protocol for audio processing operations."""
    
    def process(self, input_files: List[str], 
                output_path: str,
                output_filename: str,
                metadata: Dict[str, Any],
                settings: Dict[str, Any],
                progress_callback: Callable[[int], None],
                status_callback: Callable[[str], None],
                complete_callback: Callable[[str], None],
                error_callback: Callable[[str], None]) -> None:
        """Process audio files into audiobook format.
        
        Args:
            input_files: List of input audio file paths
            output_path: Directory for output file
            output_filename: Name of output file
            metadata: Metadata to embed in output
            settings: Processing settings (bitrate, format, etc.)
            progress_callback: Called with progress percentage (0-100)
            status_callback: Called with status messages
            complete_callback: Called with output file path when complete
            error_callback: Called with error message if processing fails
        """
        ...
    
    def process_preview(self, input_file: str,
                        metadata: Dict[str, Any],
                        settings: Dict[str, Any],
                        duration_seconds: int,
                        complete_callback: Callable[[str], None],
                        error_callback: Callable[[str], None]) -> None:
        """Generate a preview of the processed audio.
        
        Args:
            input_file: Path to input audio file
            metadata: Metadata to embed
            settings: Processing settings
            duration_seconds: Length of preview in seconds
            complete_callback: Called with preview file path
            error_callback: Called with error message if operation fails
        """
        ...
    
    def cancel(self,
               success_callback: Callable[[], None],
               error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Cancel current processing operation.
        
        Args:
            success_callback: Called when cancellation is complete
            error_callback: Called with error message if cancellation fails
        """
        ...
    
    def is_processing(self) -> bool:
        """Check if processing is currently active.
        
        Returns:
            True if processing is active, False otherwise.
        """
        ...


class ISettingsManager(Protocol):
    """Protocol for settings management."""
    
    def get_setting(self, name: str, default: Any = None) -> Any:
        """Get setting value by name.
        
        Args:
            name: Setting name
            default: Default value if setting not found
            
        Returns:
            Setting value or default
        """
        ...
    
    def set_setting(self, name: str, value: Any,
                    success_callback: Callable[[str, Any], None],
                    error_callback: Optional[Callable[[str], None]] = None) -> None:
        """Set setting value.
        
        Args:
            name: Setting name
            value: New setting value
            success_callback: Called with setting name and value when saved
            error_callback: Called with error message if operation fails
        """
        ...
    
    def get_all_settings(self) -> Dict[str, Any]:
        """Get all current settings.
        
        Returns:
            Dictionary containing all settings.
        """
        ...


class IPathService(Protocol):
    """Protocol for path and filename operations."""
    
    def calculate_output_path(self, base_dir: str, 
                              metadata: Dict[str, Any],
                              use_subdirectory_pattern: bool) -> str:
        """Calculate output directory path.
        
        Args:
            base_dir: Base output directory
            metadata: Metadata for path generation
            use_subdirectory_pattern: Whether to create subdirectories
            
        Returns:
            Calculated output directory path
        """
        ...
    
    def generate_output_filename(self, metadata: Dict[str, Any], 
                                 pattern_id: int) -> str:
        """Generate output filename based on pattern.
        
        Args:
            metadata: Metadata for filename generation
            pattern_id: Filename pattern identifier (0, 1, or 2)
            
        Returns:
            Generated filename
        """
        ...


# Error categories for better error handling
class ErrorCategory:
    """Standard error categories for service operations."""
    
    FILE_NOT_FOUND = "file_not_found"
    PERMISSION_DENIED = "permission_denied"
    INVALID_FORMAT = "invalid_format"
    PROCESSING_ERROR = "processing_error"
    METADATA_ERROR = "metadata_error"
    SETTINGS_ERROR = "settings_error"
    VALIDATION_ERROR = "validation_error"
    DISK_FULL = "disk_full"
    FFMPEG_ERROR = "ffmpeg_error"
    NETWORK_ERROR = "network_error"


class ServiceError(Exception):
    """Base exception for service operations."""
    
    def __init__(self, message: str, category: str = ErrorCategory.PROCESSING_ERROR):
        super().__init__(message)
        self.message = message
        self.category = category
    
    def __str__(self) -> str:
        return f"[{self.category}] {self.message}"


# Factory function type for creating services
ServiceFactory = Callable[[], Any]

# Registry for service factories (used by adapters)
SERVICE_REGISTRY: Dict[str, ServiceFactory] = {}


def register_service_factory(interface_name: str, factory: ServiceFactory) -> None:
    """Register a factory function for a service interface.
    
    Args:
        interface_name: Name of the interface (e.g., "IFileService")
        factory: Factory function that creates service instances
    """
    SERVICE_REGISTRY[interface_name] = factory


def get_service_factory(interface_name: str) -> Optional[ServiceFactory]:
    """Get registered factory for a service interface.
    
    Args:
        interface_name: Name of the interface
        
    Returns:
        Factory function or None if not registered
    """
    return SERVICE_REGISTRY.get(interface_name)