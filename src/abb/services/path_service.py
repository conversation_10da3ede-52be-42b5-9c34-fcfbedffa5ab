"""Path service for output path calculation and filename generation."""

from pathlib import Path
from typing import Any, Dict


class PathService:
    """Service for calculating output paths and generating filenames for audiobook processing."""
    
    def calculate_output_path(self, base_dir: str, metadata: Dict[str, Any], 
                            use_subdirectory_pattern: bool) -> str:
        """Calculate the output directory path based on settings and metadata.
        
        Args:
            base_dir: Base output directory path.
            metadata: Dictionary containing audio metadata (artist, series).
            use_subdirectory_pattern: Whether to create subdirectories for author/series.
            
        Returns:
            Full output directory path as string.
        """
        output_path = Path(base_dir)
        
        if use_subdirectory_pattern:
            author = self._safe_str(metadata.get("artist", "")).strip()
            if author:
                output_path = output_path / self._sanitize_path(author)
            
            series = self._safe_str(metadata.get("series", "")).strip()
            if series:
                output_path = output_path / self._sanitize_path(series)
        
        return str(output_path)
    
    def generate_output_filename(self, metadata: Dict[str, Any], pattern_id: int) -> str:
        """Generate output filename based on pattern and metadata.
        
        Args:
            metadata: Dictionary containing audio metadata (title, artist, series, year).
            pattern_id: Pattern to use (0=Title (Year), 1=Author - Title, 2=Series - Title).
            
        Returns:
            Sanitized filename string with .m4b extension.
        """
        filename = "audiobook.m4b"  # Default
        
        if pattern_id == 0:  # Title (Year)
            title = self._safe_str(metadata.get("title", ""))
            year = self._safe_str(metadata.get("year", ""))
            if title.strip():
                filename = f"{title} ({year}).m4b" if year.strip() else f"{title}.m4b"
                
        elif pattern_id == 1:  # Author - Title
            author = self._safe_str(metadata.get("artist", ""))
            title = self._safe_str(metadata.get("title", ""))
            if author.strip() and title.strip():
                filename = f"{author} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
                
        elif pattern_id == 2:  # Series - Title
            series = self._safe_str(metadata.get("series", ""))
            title = self._safe_str(metadata.get("title", ""))
            if series.strip() and title.strip():
                filename = f"{series} - {title}.m4b"
            elif title.strip():
                filename = f"{title}.m4b"
        
        # Basic check for empty name
        if not filename.strip() or filename.strip() in [".m4b", "().m4b", "( ).m4b"]:
            filename = "audiobook.m4b"
        
        return self._sanitize_filename(filename)
    
    def _sanitize_path(self, path_str: str) -> str:
        """Remove invalid characters from path components.
        
        Args:
            path_str: Path string to sanitize.
            
        Returns:
            Sanitized path string with invalid characters replaced by underscores.
        """
        invalid_chars = ["<", ">", ":", '"', "/", "\\", "|", "?", "*"]
        for char in invalid_chars:
            path_str = path_str.replace(char, "_")
        return path_str
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility.
        
        Args:
            filename: Filename to sanitize.
            
        Returns:
            Sanitized filename with invalid characters replaced.
        """
        return self._sanitize_path(filename)
    
    def _safe_str(self, val: Any) -> str:
        """Safely convert value to string.
        
        Args:
            val: Value to convert (can be None).
            
        Returns:
            String representation of value or empty string if None.
        """
        return str(val) if val is not None else ""