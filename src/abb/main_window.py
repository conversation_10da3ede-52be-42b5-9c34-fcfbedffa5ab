"""MainWindow class for Audiobook Boss (ABB).
Inherits QMainWindow, sets default size to 1280x820 (see Style Guide §2).
"""

import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

from PySide6.QtCore import (
    QEvent,
    QSettings,
    QSize,
    Qt,
)
from PySide6.QtGui import QAction, QPalette
from PySide6.QtWidgets import (
    QFileDialog,
    QLabel,
    QMainWindow,
    QMessageBox,
    QSplitter,
    QStatusBar,
    QVBoxLayout,
    QWidget,
)

from .controllers.main_controller import MainController
from .ffmpeg_utils import (
    _get_executable_path,
)
from .ffmpeg_utils import (
    calculate_file_size as calculate_file_size_util,
)
from .metadata_utils import (
    calculate_estimated_size,
    get_duration,
)
from .services.settings_manager import SettingsManager
from .ui.widgets.left_panel_widget import LeftPanelWidget
from .ui.widgets.right_panel_widget import RightPanelWidget


def load_qss_file(file_path: Path) -> str:
    """Load and return the content of a QSS file."""
    if file_path.exists():
        with open(file_path, "r") as file:
            return file.read()
    return ""


class MainWindow(QMainWindow):
    """Main application window."""

    def __init__(self) -> None:
        super().__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(QSize(1280, 820))

        self.settings = QSettings("AudiobookBoss", "ABB")
        self.logger = logging.getLogger("AudiobookBoss.MainWindow")

        self.app_state = {
            "file_list": [],
            "metadata": {},
            "settings": self.load_settings(),
            "processing_state": {},
        }
    
        # Create a settings manager for persistent settings
        settings_path = os.path.join(os.path.expanduser("~"), ".audiobookboss", "settings.json")
        self._settings_manager = SettingsManager(settings_path, self.app_state["settings"])
    
        # Initialize controller with settings manager
        self._controller = MainController(settings_manager=self._settings_manager)

        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(12, 12, 12, 10)
        self.main_layout.setSpacing(8)

        self.splitter = QSplitter(Qt.Horizontal)

        self.left_panel = QWidget()
        self.left_panel.setObjectName("left_panel")
        self.right_panel = QWidget()
        self.right_panel.setObjectName("right_panel")

        self._setup_left_panel()
        self._setup_right_panel()

        self._setup_property_labels()
        self.setup_controller_connections()

        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        self.splitter.setSizes([33, 67]) # 33/67 ratio

        self.main_layout.addWidget(self.splitter)

        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        self._setup_menu_bar()
        self.apply_theme()
        self._check_system_dependencies()

    def _check_system_dependencies(self) -> None:
        """Check for essential system dependencies like ffmpeg and ffprobe."""
        ffmpeg_path = _get_executable_path("ffmpeg")
        ffprobe_path = _get_executable_path("ffprobe")

        ffmpeg_ok = ffmpeg_path is not None
        ffprobe_ok = ffprobe_path is not None

        if not ffmpeg_ok or not ffprobe_ok:
            self.show_ffmpeg_ffprobe_warning_dialog(ffmpeg_ok, ffprobe_ok)

    def show_ffmpeg_ffprobe_warning_dialog(
        self, ffmpeg_ok: bool, ffprobe_ok: bool
    ) -> None:
        """Display a warning dialog if ffmpeg or ffprobe are not found."""
        missing_tools = []
        if not ffmpeg_ok:
            missing_tools.append("FFmpeg")
        if not ffprobe_ok:
            missing_tools.append("FFprobe")

        tool_list_str = " and ".join(missing_tools)

        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle(f"{tool_list_str} Not Found")
        msg_box.setText(
            f"The essential command-line tool(s) {tool_list_str} could not be found. "
            f"Audiobook Boss needs these tools to process audio files.\n\n"
            f"Please ensure that {tool_list_str} is installed and accessible in your system's PATH, "
            f"or that it is included in the application's bundled resources.\n\n"
            f"You may need to configure the paths in the application settings if they are installed in a custom location."
        )
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec()

    def event(self, event: QEvent) -> bool:
        """Handle application events."""
        if event.type() == QEvent.ApplicationPaletteChange:
            self.apply_theme()
        return super().event(event)

    def apply_theme(self) -> None:
        """Apply appropriate theme based on system palette."""
        app_palette = self.palette()
        is_dark_mode = app_palette.color(QPalette.Window).lightness() < 128
        self.load_stylesheet(is_dark_mode)

    def load_stylesheet(self, is_dark_mode: bool) -> None:
        """Load and apply the appropriate stylesheet."""
        if getattr(sys, "frozen", False):
            base_path = Path(sys._MEIPASS) # Running as compiled app
        else:
            base_path = Path(__file__).parent # Running in development

        stylesheet_name = "style_dark.qss" if is_dark_mode else "style_light.qss"
        stylesheet_path = base_path / "Resources" / stylesheet_name
        stylesheet_content = load_qss_file(stylesheet_path)

        if stylesheet_content:
            self.setStyleSheet(stylesheet_content)
        else:
            # Minimal default styling if stylesheet doesn't exist
            self.setStyleSheet("QSplitter::handle { background-color: palette(mid); }")

    def load_settings(self) -> dict:
        """Load settings from QSettings."""
        settings_dict = {
            "last_input_dir": self.settings.value("last_input_dir", str(Path.home()), type=str),
            "last_output_dir": self.settings.value("last_output_dir", str(Path.home()), type=str),
            "output_bitrate": self.settings.value("output_bitrate", 64, type=int),
            "output_channels": self.settings.value("output_channels", 1, type=int), # 1 = mono
            "output_sample_rate": self.settings.value("output_sample_rate", 44100, type=int),  # Auto-detected from input
            "output_directory": self.settings.value("output_directory", str(Path.home()), type=str),
            "output_filename_pattern": self.settings.value("output_filename_pattern", 0, type=int),
            "output_create_subdirectory": self.settings.value("output_create_subdirectory", False, type=bool),
            "use_subdirectory_pattern": self.settings.value("use_subdirectory_pattern", True, type=bool),
            "filename_pattern": self.settings.value("filename_pattern", 0, type=int), # 0 = default pattern
        }
        return settings_dict

    def save_settings(self) -> None:
        """Save current settings to QSettings."""
        self.settings.setValue("last_input_dir", self.app_state["settings"]["last_input_dir"])
        self.settings.setValue("last_output_dir", self.app_state["settings"]["last_output_dir"])
        self.settings.setValue("output_bitrate", self.app_state["settings"]["output_bitrate"])
        self.settings.setValue("output_channels", self.app_state["settings"]["output_channels"])
        self.settings.setValue("output_sample_rate", self.app_state["settings"]["output_sample_rate"])
        self.settings.setValue("output_directory", self.app_state["settings"]["output_directory"])
        self.settings.setValue("output_filename_pattern", self.app_state["settings"]["output_filename_pattern"])
        self.settings.setValue("output_create_subdirectory", self.app_state["settings"]["output_create_subdirectory"])
        self.settings.setValue("use_subdirectory_pattern", self.app_state["settings"]["use_subdirectory_pattern"])
        self.settings.setValue("filename_pattern", self.app_state["settings"]["filename_pattern"])
        self.settings.sync()

    def update_setting(self, key: str, value) -> None:
        """Update a specific setting and save it."""
        self._controller.update_setting(key, value)

    @property
    def metadata_form_widget(self):
        """Convenience property to access the metadata form widget."""
        return self.right_panel_widget.metadata_form_widget

    def _on_setting_changed(self, key: str, value) -> None:
        """Handle setting changes from the UI widgets.

        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        # Update local app_state first
        self.app_state["settings"][key] = value
        
        # Then update through the controller which will save to SettingsManager
        self._controller.update_setting(key, value)

    def closeEvent(self, event) -> None:
        """Save settings and clean up when the application is closing."""
        self.save_settings()
        # Removed processing_thread and processing_worker cleanup; handled by controller now
        super().closeEvent(event)

    def _setup_left_panel(self) -> None:
        """Set up the left panel with input components using LeftPanelWidget."""
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)

        self.left_panel_widget = LeftPanelWidget()
        self.left_panel_widget.files_dropped_signal.connect(lambda files: self._controller.add_files(files))
        self.left_panel_widget.selection_changed_signal.connect(self._on_file_selection_changed)
        self.left_panel_widget.request_remove_signal.connect(self.remove_file)
        self.left_panel_widget.request_move_up_signal.connect(self._on_move_file_up_requested)
        self.left_panel_widget.request_move_down_signal.connect(self._on_move_file_down_requested)
        self.left_panel_widget.request_add_files_signal.connect(self._on_add_files_requested)
        self.left_panel_widget.request_clear_list_signal.connect(self.clear_file_list)
        self.left_panel_widget.files_reordered_signal.connect(self._handle_files_reordered_in_ui)
        left_layout.addWidget(self.left_panel_widget)

    def _on_file_selection_changed(self, index: int) -> None:
        """Handle file selection changed in the LeftPanelWidget.
        Delegates to the controller.

        Args:
            index: Index of the selected file
        """
        if index >= 0 and index < len(self.app_state["file_list"]):
            file_path = self.app_state["file_list"][index]
            self._controller.handle_file_selection_changed([file_path])
        else:
            self._controller.handle_file_selection_changed([])

    def _update_ui_for_selected_file(self, properties: Optional[Dict[str, Any]] = None, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Update UI elements based on the currently selected file's properties and metadata.
        This method is intended to be called by a signal from the controller.
        """
        is_first_file = False
        if self.app_state["file_list"] and self.left_panel_widget.file_list_widget.currentRow() == 0:
            is_first_file = True

        self._set_file_property_labels(properties, is_first_file)

        if properties:
            file_properties = {
                "duration": str(properties.get("duration", "N/A")),
                "bitrate": f"{properties.get('bitrate', 'N/A')} kbps",
                "sample_rate": f"{properties.get('sample_rate', 'N/A')} Hz",
                "channels": str(properties.get("channels", "N/A"))
            }
            self.left_panel_widget.update_selected_file_properties_display(file_properties)
        else:
            self.left_panel_widget.update_selected_file_properties_display({}) # Clear properties if no file or error

        if metadata:
            self.right_panel_widget.populate_metadata(metadata)
        else:
            self.right_panel_widget.populate_metadata({}) # Clear metadata if no file or error

    def _on_move_file_up_requested(self, index: int) -> None:
        """Handle move file up request from LeftPanelWidget.

        Args:
            index: Index of the file to move up
        """
        if index > 0 and index < len(self.app_state["file_list"]):
            file_list = self.app_state["file_list"].copy()
            file_list[index], file_list[index-1] = file_list[index-1], file_list[index]
            self._update_file_list_display(file_list)
            self.left_panel_widget.file_list_widget.setCurrentRow(index-1)

    def _on_move_file_down_requested(self, index: int) -> None:
        """Handle move file down request from LeftPanelWidget.

        Args:
            index: Index of the file to move down
        """
        if index >= 0 and index < len(self.app_state["file_list"]) - 1:
            file_list = self.app_state["file_list"].copy()
            file_list[index], file_list[index+1] = file_list[index+1], file_list[index]
            self._update_file_list_display(file_list)
            self.left_panel_widget.file_list_widget.setCurrentRow(index+1)

    def _handle_files_reordered_in_ui(self, new_order_paths: List[str]) -> None:
        """Handle files reordered signal from LeftPanelWidget.
        Calls the controller's reorder_files method with the new order.

        Args:
            new_order_paths: List of file paths in the new order
        """
        self._controller.reorder_files(new_order_paths)

    def _setup_menu_bar(self) -> None:
        """Set up the menu bar with Settings and About actions."""
        menu_bar = self.menuBar()
        file_menu = menu_bar.addMenu("&File")
        
        settings_action = QAction("&Settings...", self)
        settings_action.setStatusTip("Open application settings")
        settings_action.triggered.connect(self._show_settings_dialog)
        file_menu.addAction(settings_action)
        
        help_menu = menu_bar.addMenu("&Help")
        about_action = QAction("&About Audiobook Boss", self)
        about_action.setStatusTip("Show information about this application")
        about_action.triggered.connect(self._show_about_dialog)
        help_menu.addAction(about_action)

    def _setup_property_labels(self):
        """Create property labels that are potentially still needed for the right panel."""
        # These labels might be redundant if RightPanelWidget and its children handle all displays.
        # MainWindow._setup_output_audio_settings_group also creates self.sample_rate_output_label.
        self.sample_rate_output_label = QLabel("—") 
        self.combined_size_label = QLabel("—")
        self.file_size_label = QLabel("—")

        for label in [self.sample_rate_output_label, self.combined_size_label, self.file_size_label]:
            label.setStyleSheet("font-family: 'Menlo', 'Monaco', 'Consolas', 'DejaVu Sans Mono', monospace;")

    def remove_file(self, index: int) -> None:
        """Remove a file from the list by index.
        Delegates to the controller.

        Args:
            index: Index of the file to remove
        """
        self._controller.remove_file(index)

    def _on_add_files_requested(self) -> None:
        """Handle add files button click from LeftPanelWidget.
        Opens a file dialog and adds selected files to the file list.
        """
        open_dir = self.app_state["settings"]["last_input_dir"]
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("Audio Files (*.mp3 *.m4a *.m4b *.aac *.mp4 *.wav *.flac)")
        file_dialog.setDirectory(open_dir)

        if file_dialog.exec_():
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                self._controller.add_files(file_paths)
    
    def clear_file_list(self) -> None:
        """Clear all files from the list.
        Delegates to the controller to handle the operation.
        """
        self._controller.clear_file_list()

    def _update_file_list_display(self, files: List[str]) -> None:
        """Update the file list widget with the provided files.

        Args:
            files: List of file paths to display
        """
        self.app_state["file_list"] = files
        self.left_panel_widget.update_file_list_display(files)
        self.update_combined_size()
        self.update_estimated_size()

    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in bytes to a human-readable string (KB, MB, GB)."""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

    def update_combined_size(self) -> None:
        """Calculate and update the combined size of all files in the list."""
        total_size = 0
        for file_path in self.app_state["file_list"]:
            total_size += calculate_file_size_util(file_path)
        self.combined_size_label.setText(self.format_file_size(total_size))

    def _set_file_property_labels(
        self, properties: Optional[Dict[str, Any]], is_first_file: bool
    ) -> None:
        """Set the file property labels based on the fetched properties."""
        if properties:
            self.file_size_label.setText(self.format_file_size(properties.get("file_size", 0)))
            if is_first_file:
                self.sample_rate_output_label.setText(f"{properties.get('sample_rate', 'N/A')} Hz")
        else: 
            self.file_size_label.setText("N/A")
            if is_first_file:
                self.sample_rate_output_label.setText("N/A")

    def _setup_right_panel(self) -> None:
        """Set up the right panel with metadata and output components using RightPanelWidget."""
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)

        self.right_panel_widget = RightPanelWidget(self._controller)
        self.right_panel_widget.populate_settings(self.app_state["settings"])
        # Connect to RightPanelWidget's setting_changed signal (which forwards from OutputSettingsWidget)
        self.right_panel_widget.setting_changed.connect(self._on_setting_changed)
        self.right_panel_widget.start_processing_requested.connect(self.start_processing)
        self.right_panel_widget.cancel_processing_requested.connect(self.cancel_processing)
        right_layout.addWidget(self.right_panel_widget)

    def _on_cover_art_update_requested(self, image_path: str) -> None:
        """Handle cover art update request from the RightPanelWidget.

        Args:
            image_path: Path to the new cover art image file
        """
        self._controller.update_cover_art(image_path)
        self.update_estimated_size()

    def update_cover_art(self, pixmap) -> None:
        """Update cover art in metadata."""
        if "metadata" not in self.app_state:
            self.app_state["metadata"] = {}
        self.app_state["metadata"]["cover_art"] = pixmap # Storing QPixmap in app_state
        self.update_estimated_size()
        
    def _on_cover_art_changed(self, image_path) -> None:
        """Handle cover art changed signal from the MetadataFormWidget.
        
        Args:
            image_path (str): Path to the new cover art image
        """
        self._controller.update_metadata("cover_art", image_path)
        self.logger.info(f"Cover art updated to: {image_path}")

    def update_bitrate_setting(self) -> None:
        """Update bitrate setting in app state."""
        # Assumes self.bitrate_combo is valid (might be an issue if setup methods are dead)
        bitrate = self.bitrate_combo.currentData() 
        self.app_state["settings"]["default_bitrate"] = bitrate
        self.update_setting("default_bitrate", bitrate)
        self.update_estimated_size()

    def update_channels_setting(self) -> None:
        """Update channels setting in app state."""
        # Assumes self.channels_combo is valid
        channels = self.channels_combo.currentData()
        self.app_state["settings"]["default_channels"] = channels
        self.update_setting("default_channels", channels)

    def update_subdir_setting(self, checked: bool) -> None:
        """Update subdirectory pattern setting in app state."""
        self.app_state["settings"]["use_subdirectory_pattern"] = checked
        self.update_setting("use_subdirectory_pattern", checked)
        self.right_panel_widget.set_use_subdirectory(checked)

    def update_filename_pattern(self, button) -> None:
        """Update filename pattern setting in app state."""
        # Assumes self.filename_pattern_group is valid
        pattern_id = self.filename_pattern_group.id(button)
        self.app_state["settings"]["filename_pattern"] = pattern_id
        self.update_setting("filename_pattern", pattern_id)

    def browse_output_directory(self) -> None:
        """Open directory dialog to select output directory."""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory",
            self.app_state["settings"]["last_output_dir"],
        )

        if directory:
            self.app_state["settings"]["last_output_dir"] = directory
            self.update_setting("last_output_dir", directory)
            self.right_panel_widget.set_output_directory(directory)

    def update_estimated_size(self) -> None:
        """Update the estimated output size based on input files and settings."""
        if not self.app_state["file_list"]:
            self.right_panel_widget.set_estimated_size("—")
            return

        total_duration = 0
        for file_path in self.app_state["file_list"]:
            total_duration += get_duration(file_path)

        bitrate = self.app_state["settings"].get("output_bitrate", 64)
        estimated_size = calculate_estimated_size(total_duration, bitrate)
        self.right_panel_widget.set_estimated_size(self.format_file_size(estimated_size))

    def extract_and_populate_metadata(self, file_path: str) -> None:
        """Extract metadata from a file and populate the UI fields.
        
        Args:
            file_path: Path to the audio file to extract metadata from
        """
        self._controller.extract_metadata(file_path)

    def _update_metadata_display(self, metadata: Dict[str, Any]) -> None:
        """Update the UI fields with the provided metadata.

        Args:
            metadata: Dictionary containing metadata fields
        """
        self.app_state["metadata"] = metadata
        self.right_panel_widget.populate_metadata(metadata)

    # Methods set_default_bitrate_from_file, _determine_optimal_bitrate, 
    # _update_bitrate_ui, and _update_sample_rate_label were removed from MainWindow.
    # Their functionality is intended to be handled by MainController.configure_audio_defaults().
    # Any calls to self.set_default_bitrate_from_file(file_path) should be replaced with
    # self._controller.configure_audio_defaults(file_path).

    def start_processing(self) -> None:
        """Start processing the audiobook.
        Delegates to the controller to handle the processing.
        """
        # Set output file path for verification in _processing_done
        output_path = self._controller.get_output_path()
        output_filename = self._controller.get_output_filename()
        self._output_file_path = str(Path(output_path) / output_filename)
        
        # Start processing - controller will emit processing_started_signal which triggers UI updates
        self._controller.start_processing()

    def _set_main_ui_enabled(self, enabled: bool) -> None:
        """Toggle enabled state of primary UI input widgets."""
        self.left_panel_widget.setEnabled(enabled)
        # RightPanelWidget's process/cancel buttons are handled by its set_processing_state method
        self.right_panel_widget.set_metadata_form_enabled(enabled)
        self.right_panel_widget.set_output_settings_enabled(enabled)

    def setup_controller_connections(self) -> None:
        """Connect signals from the controller to the UI update methods."""
        self._controller.file_list_updated_signal.connect(self._update_file_list_display)
        self._controller.metadata_updated_signal.connect(self._update_metadata_display)
        self._controller.selected_file_properties_updated_signal.connect(self._on_selected_file_properties_updated)
        self._controller.selected_file_data_changed.connect(self._on_selected_file_metadata_updated)
        self._controller.combined_size_updated_signal.connect(self.combined_size_label.setText)
        # Also update the metadata form widget when metadata changes
        self._controller.metadata_updated_signal.connect(self._on_metadata_updated_from_controller)
        self._controller.cover_art_updated_signal.connect(self._on_cover_art_updated_from_controller)
        self._controller.status_message_updated_signal.connect(self._update_status)
        self._controller.processing_started_signal.connect(lambda: self._set_ui_for_processing_start(True))
        self._controller.processing_progress_signal.connect(self._update_progress)
        self._controller.processing_finished_signal.connect(self._processing_done)
        self._controller.error_occurred_signal.connect(self._processing_error)
        # Connect controller.setting_changed only for specific auto-detected settings
        self._controller.setting_changed.connect(self._on_controller_setting_changed)

    def _on_selected_file_properties_updated(self, properties: Dict[str, Any]) -> None:
        """Handle selected file properties updated signal from controller."""
        self._update_ui_for_selected_file(properties=properties)
    
    def _on_metadata_updated_from_controller(self, metadata: Dict[str, Any]) -> None:
        """Delegate metadata update to the right panel widget."""
        self.right_panel_widget.metadata_form_widget.update_metadata_display(metadata)
    
    def _on_cover_art_updated_from_controller(self, cover_art_data) -> None:
        """Delegate cover art update to the right panel widget."""
        self.right_panel_widget.metadata_form_widget.cover_art.update_cover_art_display(cover_art_data)
    
    def _on_controller_setting_changed(self, key: str, value) -> None:
        """Handle setting changes from the controller (auto-detected values).
        
        Args:
            key: Setting key that changed
            value: New value for the setting
        """
        # Update local app_state
        self.app_state["settings"][key] = value
        
        # Update UI for specific auto-detected settings
        if key == "output_sample_rate":
            # Update the output settings widget with the detected sample rate
            self.right_panel_widget.output_settings_widget.set_sample_rate(value)

    def _on_selected_file_metadata_updated(self, metadata: Dict[str, Any]) -> None:
        """Handle selected file metadata updated signal from controller."""
        self._update_ui_for_selected_file(metadata=metadata)
        
    def _set_ui_for_processing_start(self, is_starting: bool) -> None:
        """Update UI elements based on processing state."""
        if is_starting:
            self._set_main_ui_enabled(False)
            self.right_panel_widget.set_processing_state(True) # Handles its own buttons
            self.status_bar.showMessage("Processing...")
        else:
            self._set_main_ui_enabled(True)
            self.right_panel_widget.set_processing_state(False) # Handles its own buttons

    def cancel_processing(self) -> None:
        """Cancel the current processing operation."""
        self.status_bar.showMessage("Cancelling...")
        self._controller.cancel_processing()

    def _update_progress(self, value: int) -> None:
        """Update progress bar with the given value."""
        self.right_panel_widget.update_progress(value)
        self.status_bar.showMessage(f"Processing: {value}%")

    def _update_status(self, status: str) -> None:
        """Update status bar with the given status message."""
        self.status_bar.showMessage(status)

    def _processing_done(self, output_file_path: str = None) -> None:
        """Handle processing completion."""
        self._set_ui_for_processing_start(False)
        self.right_panel_widget.reset_progress()
        file_path_to_check = output_file_path if output_file_path else getattr(self, "_output_file_path", None)

        if file_path_to_check and os.path.exists(file_path_to_check):
            self.status_bar.showMessage(f"Processing completed successfully. File saved to: {file_path_to_check}", 5000)
        else:
            self.status_bar.showMessage("Processing completed but output file not found. Check permissions and disk space.", 5000)

    def _processing_error(self, error_message: str) -> None:
        """Handle processing error."""
        self._set_ui_for_processing_start(False)
        self.right_panel_widget.reset_progress()
        self.status_bar.showMessage(f"Error: {error_message}", 5000)

    def _show_settings_dialog(self) -> None:
        """Show the settings dialog and update settings if accepted."""
        self._controller.show_settings_dialog()

    def _show_about_dialog(self) -> None:
        """Show the about dialog."""
        self._controller.show_about_dialog()
