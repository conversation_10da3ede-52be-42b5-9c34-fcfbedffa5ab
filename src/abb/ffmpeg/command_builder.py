import os
from typing import Any, Dict, List, Optional


class FFmpegCommandBuilder:
    """Builds FFmpeg commands for audio processing"""
    
    # DEPRECATED: Use UnifiedMetadataHandler.get_metadata_mapping() instead
    # This constant is kept only for backward compatibility with tests
    # All runtime logic now uses UnifiedMetadataHandler as single source of truth
    ABB_TO_FFMPEG_METADATA_MAP_GENERAL = {
        "title": "title",
        "album": "album",
        "year": "date",
        "genre": "genre",
        "narrator": "composer",
        "series": "mood",
        "series_pos": "track",
        "copyright": "copyright",
        "sort_title": "title_sort",
        "sort_artist": "artist_sort",
        "sort_album_artist": "album_artist_sort",
        "sort_composer": "composer_sort",
        "disc_number": "disc",
        "compilation": "compilation",
        "grouping": "grouping",
        "lyrics": "lyrics",
        "publisher": "publisher",
        "comment": "comment",
        "description": "description"
    }
    
    def __init__(self, codec_check_func):
        """Initialize with a function to check codec availability"""
        self._codec_available = codec_check_func
        self._metadata_handler = None  # Lazy-loaded when ABB_NEW_META=True
    
    def build_ffmpeg_command(
        self,
        input_files: List[str],
        output_file_full_path: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        ffmpeg_exe_path: str,
        ffprobe_exe_path: Optional[str] = None,
    ) -> List[str]:
        """Build complete FFmpeg command for processing"""
        cmd = []
        cmd.append(ffmpeg_exe_path)
        cmd.extend(["-hide_banner", "-loglevel", "error", "-y"])
        
        # Input files
        for input_file in input_files:
            cmd.extend(["-i", input_file])
        
        # Cover art
        cover_art_path = metadata.get("cover_art_temp_path")
        cover_art_stream_idx: Optional[int] = None
        if cover_art_path:
            cmd.extend(["-i", cover_art_path])
            cover_art_stream_idx = len(input_files)
        
        # Handle multiple inputs
        if len(input_files) > 1:
            filter_parts = []
            for i in range(len(input_files)):
                filter_parts.append(f"[{i}:a]")
            filter_parts.append(f"concat=n={len(input_files)}:v=0:a=1[outa]")
            cmd.extend(["-filter_complex", "".join(filter_parts)])
            cmd.extend(["-map", "[outa]"])
        elif input_files:
            cmd.extend(["-map", "0:a:0"])
        
        # Map cover art
        if cover_art_stream_idx is not None:
            cmd.extend(["-map", f"{cover_art_stream_idx}:v"])
            cmd.extend(["-c:v", "copy"])
            cmd.extend(["-disposition:v", "attached_pic"])
        
        # Apply settings
        self._apply_audio_settings_to_command(cmd, settings)
        self._apply_metadata_to_command(cmd, metadata)
        
        # Handle chapters for single M4A/M4B input
        if len(input_files) == 1:
            first_input_lower = input_files[0].lower()
            if first_input_lower.endswith(".m4a") or first_input_lower.endswith(".m4b"):
                cmd.extend(["-map_chapters", "0"])
        
        # Output format
        cmd.extend(["-f", "mp4"])
        cmd.extend(["-movflags", "+faststart"])
        cmd.append(output_file_full_path)
        
        return cmd
    
    def build_ffmpeg_preview_command(
        self,
        input_file_path: str,
        metadata: Dict[str, Any],
        settings: Dict[str, Any],
        temp_cover_path: Optional[str] = None,
        duration_seconds: int = 30,
    ) -> List[str]:
        """Build FFmpeg command for preview generation"""
        import tempfile

        from ..ffmpeg_utils import _get_executable_path
        
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            raise FileNotFoundError("FFmpeg executable not found")
        
        cmd = []
        cmd.append(str(ffmpeg_exe_path))
        cmd.extend(["-hide_banner", "-loglevel", "error", "-y"])
        cmd.extend(["-i", input_file_path])
        
        cover_art_stream_idx: Optional[int] = None
        if temp_cover_path:
            cmd.extend(["-i", temp_cover_path])
            cover_art_stream_idx = 1
        
        cmd.extend(["-map", "0:a:0"])
        
        if cover_art_stream_idx is not None:
            cmd.extend(["-map", f"{cover_art_stream_idx}:v"])
            cmd.extend(["-c:v", "copy"])
            cmd.extend(["-disposition:v", "attached_pic"])
        
        self._apply_audio_settings_to_command(cmd, settings)
        self._apply_metadata_to_command(cmd, metadata)
        
        cmd.extend(["-t", str(duration_seconds)])
        cmd.extend(["-f", "mp4"])
        cmd.extend(["-movflags", "+faststart"])
        
        output_file = tempfile.mktemp(suffix=".m4b")
        cmd.append(output_file)
        
        return cmd
    
    def _apply_metadata_to_command(self, cmd: List[str], metadata: Dict[str, Any]) -> None:
        """Apply metadata tags to FFmpeg command"""
        # Check if we should use UnifiedMetadataHandler for mapping
        if self._should_use_unified_handler():
            ffmpeg_tags_to_apply = self._get_ffmpeg_tags_via_handler(metadata)
        else:
            ffmpeg_tags_to_apply = self._get_ffmpeg_tags_legacy(metadata)
        
        # Apply all tags
        for tag_key, tag_value in ffmpeg_tags_to_apply.items():
            if tag_value is not None and str(tag_value).strip() != "":
                cmd.extend(["-metadata", f"{tag_key}={tag_value}"])
    
    def _should_use_unified_handler(self) -> bool:
        """Check if UnifiedMetadataHandler should be used for mappings."""
        return os.environ.get('ABB_NEW_META', 'False').lower() == 'true'
    
    def _get_ffmpeg_tags_via_handler(self, metadata: Dict[str, Any]) -> Dict[str, str]:
        """Get FFmpeg tags using UnifiedMetadataHandler (ABB_NEW_META=True path)."""
        # Lazy-load the handler to avoid circular imports
        if self._metadata_handler is None:
            from ..services.unified_metadata_handler import UnifiedMetadataHandler
            self._metadata_handler = UnifiedMetadataHandler()
        
        # Set the metadata in the handler and get formatted output
        self._metadata_handler._current_metadata = metadata.copy()
        return self._metadata_handler.get_for_ffmpeg()
    
    def _get_ffmpeg_tags_legacy(self, metadata: Dict[str, Any]) -> Dict[str, str]:
        """Get FFmpeg tags using legacy logic (ABB_NEW_META=False path).
        
        Even in legacy mode, we use UnifiedMetadataHandler as the single source
        of truth for the mapping, but apply the logic manually for backward compatibility.
        """
        from ..services.unified_metadata_handler import UnifiedMetadataHandler
        
        ffmpeg_tags_to_apply: Dict[str, str] = {}
        
        # Handle artist/album_artist
        artist_val = metadata.get('artist')
        album_artist_val = metadata.get('album_artist')
        if artist_val:
            ffmpeg_tags_to_apply['artist'] = str(artist_val)
        if album_artist_val:
            ffmpeg_tags_to_apply['album_artist'] = str(album_artist_val)
        elif artist_val:
            ffmpeg_tags_to_apply['album_artist'] = str(artist_val)
        
        # Handle comment/description
        comment_val = metadata.get('comment')
        description_val = metadata.get('description')
        if comment_val:
            ffmpeg_tags_to_apply['comment'] = str(comment_val)
        elif description_val:
            ffmpeg_tags_to_apply['comment'] = str(description_val)
        
        # Handle series_sort/sort_album
        series_sort_val = metadata.get('series_sort')
        sort_album_val = metadata.get('sort_album')
        if series_sort_val:
            ffmpeg_tags_to_apply['album_sort'] = str(series_sort_val)
        elif sort_album_val:
            ffmpeg_tags_to_apply['album_sort'] = str(sort_album_val)
        
        # Map general metadata using UnifiedMetadataHandler as single source of truth
        metadata_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        for abb_key, ffmpeg_key in metadata_mapping.items():
            value = metadata.get(abb_key)
            if value is not None and str(value).strip() != "":
                if ffmpeg_key not in ffmpeg_tags_to_apply:
                    ffmpeg_tags_to_apply[ffmpeg_key] = str(value)
        
        return ffmpeg_tags_to_apply
    
    def _apply_audio_settings_to_command(self, cmd: List[str], settings: Dict[str, Any]) -> None:
        """Apply audio codec and settings to FFmpeg command"""
        audio_codec = "libfdk_aac" if self._codec_available("libfdk_aac") else "aac"
        cmd.extend(["-c:a", audio_codec])
        
        if settings.get("bitrate"):
            cmd.extend(["-b:a", f"{settings['bitrate']}k"])
        if settings.get("channels"):
            cmd.extend(["-ac", str(settings['channels'])])
        if settings.get("sample_rate"):
            cmd.extend(["-ar", str(settings['sample_rate'])])