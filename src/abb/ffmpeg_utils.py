"""FFmpeg utilities for Audiobook Boss.
Handles bundled FFmpeg binaries with libfdk support.
"""

import json
import os
import shutil
import subprocess
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .ffmpeg.command_builder import FFmpegCommandBuilder

_audio_properties_cache: Dict[str, Dict] = {}


def calculate_file_size(file_path: str) -> int:
    """Calculate the size of a file in bytes."""
    if file_path is None:
        return 0
    try:
        return Path(file_path).stat().st_size
    except (FileNotFoundError, OSError, TypeError):
        return 0


def get_audio_properties(file_path: str) -> Tuple[Dict, Optional[str]]:
    """Get audio properties of a file using ffprobe.
    Caches results per file path to avoid repeated calls.

    Returns a tuple: (properties_dict, error_message_or_none)
    - properties_dict: Dictionary with bitrate, sample_rate, channels, file_size.
                     Contains default values if ff<PERSON><PERSON> fails for some properties.
    - error_message_or_none: String with error details if f<PERSON><PERSON><PERSON> fails, else None.
    """
    global _audio_properties_cache
    error_message: Optional[str] = None

    if file_path in _audio_properties_cache:
        cached_data = _audio_properties_cache.get(file_path)
        if cached_data:
            return cached_data, None

    properties = {
        "bitrate": 0,
        "sample_rate": 0,
        "channels": 0,
        "file_size": calculate_file_size(file_path),
    }

    try:
        ffprobe_exe_path = _get_executable_path("ffprobe")
        if not ffprobe_exe_path:
            error_message = (
                "ffprobe executable not found in bundled Resources/bin or system PATH."
            )
            return properties, error_message

        cmd = [
            str(ffprobe_exe_path),
            "-v",
            "error",
            "-select_streams",
            "a:0",
            "-show_entries",
            "stream=bit_rate,sample_rate,channels",
            "-of",
            "json",
            file_path,
        ]

        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=False,
        )

        if result.returncode != 0:
            error_message = f"ffprobe failed with exit code {result.returncode}. Error: {result.stderr.strip()}"
            _audio_properties_cache[file_path] = properties
            return properties, error_message

        data = json.loads(result.stdout)

        if "streams" in data and len(data["streams"]) > 0:
            stream = data["streams"][0]
            if "bit_rate" in stream:
                try:
                    properties["bitrate"] = int(stream["bit_rate"]) // 1000
                except (ValueError, TypeError):
                    pass
            if "sample_rate" in stream:
                try:
                    properties["sample_rate"] = int(stream["sample_rate"])
                except (ValueError, TypeError):
                    pass
            if "channels" in stream:
                properties["channels"] = stream["channels"]

    except json.JSONDecodeError as e:
        error_message = f"Failed to parse ffprobe JSON output: {e}. stdout: {result.stdout.strip() if 'result' in locals() else 'N/A'}"
    except FileNotFoundError:
        error_message = f"Audio file not found: {file_path}"
    except subprocess.SubprocessError as e:
        error_message = f"Subprocess error when running ffprobe: {e}"
    except Exception as e:
        error_message = (
            f"Unexpected error getting audio properties for {file_path}: {e}"
        )

    if not error_message:
        warn_props = []
        for key in ("bitrate", "sample_rate", "channels"):
            if not properties[key]:
                warn_props.append(key)
        # if warn_props: # Intentionally commented out print, will be removed by formatter
            # print(f"[AUDIO PROPERTY WARNING] File {file_path} missing or invalid values for: {', '.join(warn_props)}")
        _audio_properties_cache[file_path] = properties

    return properties, error_message


def _get_resources_bin_dir() -> Path:
    """Determines the path to the bundled 'bin' directory within 'Resources'."""
    if getattr(sys, "frozen", False) and hasattr(sys, "_MEIPASS"):
        base_path = Path(sys._MEIPASS)
    else:
        # In development mode, go up from src/abb to project root
        base_path = Path(__file__).resolve().parent.parent.parent
    return base_path / "Resources" / "bin"


def _get_executable_path(executable_name: str) -> Optional[Path]:
    """Gets the path to an executable, checking bundled, then system PATH."""
    bundled_bin_dir = _get_resources_bin_dir()
    bundled_exe_path = bundled_bin_dir / executable_name

    if bundled_exe_path.exists() and bundled_exe_path.is_file():
        return bundled_exe_path

    system_exe_path_str = shutil.which(executable_name)
    if system_exe_path_str:
        return Path(system_exe_path_str)

    return None


def get_ffmpeg_path() -> Path:
    """Return the path to the bundled FFmpeg binaries."""
    return _get_resources_bin_dir()


def setup_ffmpeg_path() -> None:
    """Add bundled FFmpeg to PATH at runtime."""
    ffmpeg_path = get_ffmpeg_path()

    if not ffmpeg_path.exists():
        raise FileNotFoundError(f"Bundled FFmpeg not found at {ffmpeg_path}")

    os.environ["PATH"] = f"{ffmpeg_path}{os.pathsep}{os.environ.get('PATH', '')}"


def check_ffmpeg_version() -> Optional[str]:
    """Check FFmpeg version and availability."""
    try:
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            return None

        cmd = [str(ffmpeg_exe_path), "-version"]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True
        )
        return result.stdout.strip().split("\n")[0]
    except (subprocess.SubprocessError, FileNotFoundError):
        return None


def codec_available(codec_name: str) -> bool:
    """Check if a specific codec is available in the FFmpeg build."""
    try:
        ffmpeg_exe_path = _get_executable_path("ffmpeg")
        if not ffmpeg_exe_path:
            return False

        cmd = [str(ffmpeg_exe_path), "-codecs"]
        result = subprocess.run(
            cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True, text=True
        )
        return codec_name in result.stdout
    except (subprocess.SubprocessError, FileNotFoundError):
        return False


_command_builder = FFmpegCommandBuilder(codec_available)




def build_ffmpeg_preview_command(
    input_file_path: str,
    metadata: Dict[str, Any],
    settings: Dict[str, Any],
    temp_cover_path: Optional[str] = None,
    duration_seconds: int = 30,
) -> List[str]:
    return _command_builder.build_ffmpeg_preview_command(
        input_file_path, metadata, settings,
        temp_cover_path, duration_seconds
    )


def build_ffmpeg_command(
    input_files: List[str],
    output_file_full_path: str,
    metadata: Dict[str, Any],
    settings: Dict[str, Any],
    ffmpeg_exe_path: str,
    ffprobe_exe_path: Optional[str] = None,
) -> List[str]:
    return _command_builder.build_ffmpeg_command(
        input_files, output_file_full_path, metadata,
        settings, ffmpeg_exe_path, ffprobe_exe_path
    )


def build_ffmpeg_processing_command(
    file_list: List[str],
    output_path: str,
    metadata_dict: Dict[str, Any],
    cover_art_path: Optional[str],
    audio_settings_dict: Dict[str, Any],
    ffmpeg_path: str
) -> List[str]:
    """Build FFmpeg command for processing multiple audio files into single M4B.
    
    Args:
        file_list: List of input audio file paths
        output_path: Full path for output M4B file
        metadata_dict: Metadata to embed (title, artist, etc.)
        cover_art_path: Path to cover art image file
        audio_settings_dict: Audio settings (bitrate, channels, sample_rate)
        ffmpeg_path: Path to FFmpeg executable
        
    Returns:
        List of command arguments for subprocess
    """
    # Prepare metadata with cover art path
    metadata = metadata_dict.copy()
    if cover_art_path:
        metadata['cover_art_temp_path'] = cover_art_path
    
    # Build and return command
    return build_ffmpeg_command(
        input_files=file_list,
        output_file_full_path=output_path,
        metadata=metadata,
        settings=audio_settings_dict,
        ffmpeg_exe_path=ffmpeg_path
    )
