from PySide6.QtWidgets import QApplication
import sys
from src.abb.main_window import MainWindow
from src.abb.controllers.main_controller import MainController

def test_main_controller_integration():
    app = QApplication(sys.argv)
    
    # Create MainWindow
    window = MainWindow()
    
    # Verify MainController is initialized
    assert hasattr(window, '_controller')
    assert isinstance(window._controller, MainController)
    
    # Test file operations
    window.handle_files_dropped(["test_file.mp3"])
    
    # Test metadata operations
    window.update_metadata("title", "Test Title")
    
    # Test settings operations
    window.update_setting("theme", "dark")
    
    print("All tests passed!")
    
    return window

if __name__ == "__main__":
    window = test_main_controller_integration()
    # Keep the window open for manual testing
    # window.show()
    # sys.exit(app.exec())