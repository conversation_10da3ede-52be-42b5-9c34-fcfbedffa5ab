# Project Overview
Audiobook Boss (ABB) - Desktop app for processing audiobooks (mp3, m4a, m4b, AAC)
- Architecture: Service-Oriented MVC (UI → Controller → Services → FFmpeg)
- **Active Refactoring**: Following `docs/specs/Imp_plan_v2.md`
- **Current Status**: Phase 2 INCOMPLETE - 1,451 lines of untested code created
- **CRITICAL**: Phase 2 marked "complete" with 0% test coverage on new modules  
- **PRIORITY**: Create comprehensive tests for all Phase 2 modules before starting phase 3!

## Core Principles
- **Safety First**: No refactoring without CI/CD and golden baseline
- **TDD MANDATORY**: Write failing test BEFORE any new code (Red → Green → Refactor)
- **Coverage Gates**: No new module with <85% coverage, no PR that decreases overall coverage

## Development Workflow
1. **TDD MANDATORY**: Red → Green → Refactor (NO EXCEPTIONS)
2. **Follow Architectural Guidelines**: Module size limits, signal chains, etc.
3. **Coverage Validation**: Check coverage after each module (min 80%)
4. **Use Real Objects**: Minimize mocks (goal: <3 per test file)
5. **Golden Run Testing**: Baseline comparison for regression detection

## PHASE COMPLETION GATES (MANDATORY CHECKLIST)
Agents MUST run this checklist before claiming completion
**NO PHASE can be marked complete without ALL items checked:**

```bash
# 1. Coverage Verification
pytest --cov=abb --cov-report=term-missing
# Verify: No new modules with 0% coverage
# Verify: Overall coverage >= 44% (current baseline)

# 2. All Tests Pass
pytest -v
# Verify: 0 failures, 0 errors

# 3. Linting Clean  
ruff check src/
# Verify: No errors or warnings

# 4. Module-Specific Coverage (for new modules)
pytest --cov=abb.services.core --cov-report=term-missing
pytest --cov=abb.services.adapters --cov-report=term-missing
# Verify: Each new module >= 80% coverage

# 5. Feature Flag Testing (if applicable)
ABB_NEW_META=True python -m pytest
ABB_PURE_SERVICES=True python -m pytest
# Verify: All tests pass with flags enabled

# 6. Golden Run Validation
python scripts/generate_golden_baseline.py --validate
# Verify: Output matches baseline
```
## Essential Commands (double check if these are correct)
```bash
python -m src.abb.main      # Run application
python -m pytest            # Run tests
pytest --cov=abb --cov-report=xml  # Coverage report
ruff check src              # Lint code
scripts/run_reports.sh      # Generate reports

# MANDATORY COVERAGE CHECKS (run before ANY code commit)
pytest --cov=abb --cov-report=term-missing  # Full coverage with missing lines
pytest --cov=abb.[module] --cov-report=term-missing  # Single module coverage
pytest --cov=abb --cov-fail-under=44  # Fail if coverage drops below current
scripts/validate_coverage.sh  # Automated validation (REQUIRED before phase completion)

# Production Note from user: I'm concerned agents may get stuck in a loop of running tests. What happens if a test stalls? How will the agent know to interrupt the test and re-assess the situation?

# Feature Flags (for gradual rollout)
ABB_NEW_META=True python -m src.abb.main      # Test new metadata handler
ABB_PURE_SERVICES=True python -m src.abb.main # Test pure service layer
```

# MANDATORY AGENT PRE-EXECUTION ACCOUNTABILITY GUIDELINES

## Testing Guidelines
**Location:** `@tests/CLAUDE.local.md`
**REQUIREMENTS:**
- P0: ANY AGENT assigned test-related task MUST follow the testing guidelines in `@tests/CLAUDE.local.md`
- Leave no module untested.
- Adhere to Behavior-Driven Development (BDD) and TDD principles.
- Merged code must never decrease overall test coverage.
- If a test stalls >10 seconds, the agent must interrupt the test and re-assess the situation with the user.

## Coding & Architecture Guidelines
**Location:** `@src/CLAUDE.local.md`  
**REQUIREMENTS:**
- P0: ANY agent assigned implementation-related task MUST follow the coding and architecture guidelines in `@src/CLAUDE.local.md`
- Orthogonality & Single Responsibility.
- DRY (Don't Repeat Yourself).
- Simplicity & Readability: The code should explain itself.
- Documentation: Every public class/method requires a single-sentence Google-style docstring explaining the "why," not the "what."

**⚠️ FAILURE TO FOLLOW AFOREMENTIONED GUIDELINES CONSTITUTES CRITICAL IMPLEMENTATION FAILURE ⚠️**

# Work Flow efficiency and parallelization
ALWAYS liberally spawn sub-agents to break down information gathering and complex tasks into atomized parallel processes.