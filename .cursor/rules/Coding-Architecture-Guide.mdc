---
description: When working with any src module or component in src/**
globs: 
alwaysApply: false
---
# Project Coding & Architecture Guide

This guide provides a unified set of principles and guardrails for the project. Following these rules ensures consistency, maintainability, and architectural integrity.

## 1. Core Principles

- **Orthogonality & Single Responsibility**: Each class or service must have one, and only one, clear responsibility. Modules must communicate only through public service/controller APIs or signals.
- **DRY (Don't Repeat Yourself)**: Extract or reuse code on its 3rd repetition. Prefer shared helper functions and constants over duplicated logic.
- **Simplicity & Readability**: Prefer descriptive names over comments, early returns over nested conditions, and explicit logic over implicit magic.

## 2. Module & Class Structure

- **Atomized Modules (File Size)**: Adhere strictly to these size limits. LOC (Lines of Code) counts only non-empty, non-comment lines.

  | Zone   | LOC Range | Action                                                               |
  |--------|-----------|----------------------------------------------------------------------|
  | Green  | <300      | 👍 Optimal size.                                                     |
  | Yellow | 300-450   | Warn the user and propose a decomposition plan.                      |
  | Red    | >450      | **STOP generation.** Emit a plan with ≤3 concrete split options and await user approval. |

- **Method & Class Rules**:
  - **Methods**: Aim for <30 lines. Warn on methods >30 lines; require refactoring for methods >50 lines.
  - **Special Case**: `MainWindow` must be kept under 600 LOC during its refactoring.
  - **Naming**: Use `_service` suffix for service classes and `_widget` for UI widgets.

## 3. Critical Implementation Guardrails

These are non-negotiable rules for system stability and consistency.

- **A. Metadata Source**: **MUST NOT** hard-code FFmpeg tag maps. Use `UnifiedMetadataHandler.get_metadata_mapping()` as the single source of truth.
- **B. Path & Filename Logic**: **MUST** use `PathService` for all output path and filename generation. Do not duplicate sanitization logic.
- **C. Processing Safety**: **MUST** call `ProcessingValidator.validate_all()` before launching a full processing job via `ProcessingService.process_full`.
- **D. File I/O Funnel**: **MUST** perform all file list modifications (add, remove, reorder) through `FileService`.
- **E. Settings Persistence**: Output-related keys **MUST** start with `output_*`. Non-output keys (e.g., `last_input_dir`) are permitted for UX state, but managed via `SettingsManager`.
- **F. Feature Flags**: **MUST** respect any `ABB_*` feature flag present in the environment, wrapping new behaviors in a flag check that defaults to legacy behavior.

## 4. Qt-Specific Patterns

- **Threading & Concurrency**:
  - Offload all blocking/heavy operations to a worker using the `QThread` pattern.
  - **Never** start `QProcess` directly from a `QWidget` or `MainWindow`.
  - State flags (`_is_processing`) **MUST** be reset in a `finally` block to guarantee state safety.
  - Temporary files and resources **MUST** be cleaned up unconditionally in a `finally` block or dedicated `_cleanup()` method.

- **Signal Hygiene**:
  - **Max 3 hops** for any end-to-end signal chain.
  - Services **MUST** emit base names (e.g., `metadata_updated`).
  - UI Widgets **MUST** append `_signal` (e.g., `metadata_updated_signal`).
  - Controllers may use free-form names but should prefer the `_signal` suffix for consistency.
  - New `QObject`-based classes should expose **≤ 5** signals.

## 5. Development & Testing Practices

- **Documentation**: Every public class/method requires a concise Google-style docstring explaining the "why," not the "what."
- **Unit Tests**: New code **MUST** include or update a behavior-level pytest.
- **Dependency Injection**: Pass external executables (e.g., `ffmpeg`) as parameters. New code **MUST NOT** perform global lookups for them.
- **Logging**: Use the project's configured logger (`AudiobookBoss.<ModuleName>`). Bare `print()` statements are forbidden.
- **Type Safety**: `typing.Any` **MUST NOT** be used in public-facing function signatures unless technically unavoidable.

## 6. Refactoring

- **Goal Pattern**: Widgets should hold a direct reference to a controller to access services.
- **Anti-Patterns to Avoid**: Signal chains > 3 hops, services inheriting from `QObject`, UI logic in controllers, scattered metadata logic.
- **Safety Checklist**: Before refactoring, ensure: (1) CI tests pass, (2) a "golden run" output is available for regression testing, (3) changes are wrapped in feature flags where appropriate, and (4) pull requests are small and focused (< 400 LOC).
