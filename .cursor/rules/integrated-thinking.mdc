---
description: 
globs: 
alwaysApply: false
---
# Holistic Multi-Dimensional Analysis Framework
When issues occur more than twice or debugging stalls, STOP and analyze across ALL dimensions:

1. **Technical**: What is the code actually doing? Logic flow, data types, method calls
2. **Architectural**: How do components interact? Signal chains, dependencies, coupling
3. **Testing**: Are mocks/fixtures/environment correct? Setup, teardown, isolation
4. **Framework**: Are there library/plugin conflicts? Qt vs Flask, pytest interactions
5. **Context**: What's the full system state? File structure, git status, recent changes
6. **User Experience**: Does the workflow make sense? Edge cases, error paths