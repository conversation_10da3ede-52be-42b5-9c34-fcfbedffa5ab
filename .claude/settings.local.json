{"permissions": {"allow": ["Bash(grep:*)", "Bash(git commit:*)", "Bash(git checkout:*)", "Bash(git push:*)", "Bash(gh pr create:*)", "Bash(rg:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(shasum:*)", "Bash(ruff check:*)", "<PERSON><PERSON>(cat:*)", "Bash(python -m pytest:*)", "Bash(ABB_NEW_META=true python -m pytest tests/unit/services/test_metadata_service.py::TestMetadataService::test_metadata_service_extract_and_load_metadata_populates_attributes_and_emits_signals -v -s)", "Bash(unset:*)", "Bash(ABB_NEW_META=True python -c \"\nimport sys\nsys.path.append('.')\nfrom src.abb.services.metadata_service import MetadataService\nfrom src.abb.services.unified_metadata_handler import UnifiedMetadataHandler\n\n# Test with flag enabled (new behavior)\nprint('Testing with ABB_NEW_META=True')\nprint('UnifiedMetadataHandler.is_enabled():', UnifiedMetadataHandler.is_enabled())\nservice = MetadataService()\nprint('Service created successfully')\nprint('Service signals:', [signal for signal in dir(service) if 'signal' in signal.lower() or signal.startswith('metadata_')])\n\")", "Bash(ABB_NEW_META=true python -m pytest tests/integration/test_golden.py::test_golden_ffmpeg_command_generation_fast -v --tb=short)", "<PERSON><PERSON>(git worktree:*)", "Bash(git branch:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(cp:*)", "Bash(git add:*)", "Bash(pytest:*)", "Bash(scripts/validate_coverage.sh:*)"], "deny": []}, "enableAllProjectMcpServers": false}