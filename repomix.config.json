{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 52428800}, "output": {"filePath": "repomix-output.md", "style": "markdown", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "files": true, "removeComments": false, "removeEmptyLines": true, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100, "includeDiffs": false}}, "include": [], "ignore": {"useGitignore": false, "useDefaultPatterns": true, "customPatterns": [".html"]}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}