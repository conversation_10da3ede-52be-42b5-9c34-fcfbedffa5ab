#!/usr/bin/env python3
"""
Debug script to isolate the ProcessingService crash.
"""

import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent))

def test_basic_instantiation():
    """Test basic ProcessingService instantiation without mocking."""
    print("=== Testing basic ProcessingService instantiation ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.abb.services.processing_service import ProcessingService
        
        # Create QApplication if needed
        app = QApplication.instance() or QApplication([])
        print("✓ QApplication created")
        
        # Try to create ProcessingService
        print("Creating ProcessingService...")
        service = ProcessingService()
        print("✓ ProcessingService created successfully")
        
        # Check attributes
        print(f"✓ Has process_full: {hasattr(service, 'process_full')}")
        print(f"✓ Has progress signal: {hasattr(service, 'progress')}")
        print(f"✓ Worker thread running: {service._worker_thread.isRunning()}")
        
        # Clean up
        print("Cleaning up...")
        if service._worker_thread.isRunning():
            service._worker_thread.quit()
            service._worker_thread.wait(1000)
        print("✓ Cleanup completed")
        
    except Exception as e:
        print(f"✗ Error during basic instantiation: {e}")
        import traceback
        traceback.print_exc()

def test_with_mocking():
    """Test ProcessingService with mocking like in the actual tests."""
    print("\n=== Testing ProcessingService with mocking ===")
    
    try:
        from unittest.mock import patch, MagicMock
        from PySide6.QtWidgets import QApplication
        from src.abb.services.processing_service import ProcessingService
        
        # Create QApplication if needed
        app = QApplication.instance() or QApplication([])
        print("✓ QApplication created")
        
        # Test with mocking like in the actual test
        with patch('src.abb.processing_worker.ProcessingWorker.start_full_process') as mock_start:
            print("Creating ProcessingService with mocked worker...")
            service = ProcessingService()
            print("✓ ProcessingService created with mocking")
            
            # Try calling a method that would trigger the mock
            print("Calling process_full method...")
            service.process_full(
                ["test.mp3"], 
                "/tmp", 
                "test.m4b", 
                {"title": "Test"}, 
                {"bitrate": 64}
            )
            print("✓ process_full called successfully")
            
            # Clean up
            print("Cleaning up...")
            if service._worker_thread.isRunning():
                service._worker_thread.quit()
                service._worker_thread.wait(1000)
            print("✓ Cleanup completed")
            
    except Exception as e:
        print(f"✗ Error during mocked test: {e}")
        import traceback
        traceback.print_exc()

def test_signal_connections():
    """Test signal connections specifically."""
    print("\n=== Testing signal connections ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.abb.services.processing_service import ProcessingService
        
        # Create QApplication if needed
        app = QApplication.instance() or QApplication([])
        print("✓ QApplication created")
        
        print("Creating ProcessingService...")
        service = ProcessingService()
        print("✓ ProcessingService created")
        
        # Test signal forwarding methods directly
        print("Testing signal forwarding methods...")
        service._on_worker_progress(50)
        print("✓ _on_worker_progress called")
        
        service._on_worker_status("Test status")
        print("✓ _on_worker_status called")
        
        service._on_worker_finished("/test/path")
        print("✓ _on_worker_finished called")
        
        service._on_worker_error("Test error")
        print("✓ _on_worker_error called")
        
        # Clean up
        print("Cleaning up...")
        if service._worker_thread.isRunning():
            service._worker_thread.quit()
            service._worker_thread.wait(1000)
        print("✓ Cleanup completed")
        
    except Exception as e:
        print(f"✗ Error during signal test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_basic_instantiation()
    test_with_mocking()
    test_signal_connections()
    print("\n=== Debug tests completed ===")