#!/bin/bash

# Exit on any error
set -e

# Create reports directory if it doesn't exist
mkdir -p docs/reports

# Get current timestamp for content
TIMESTAMP=$(date "+%Y-%m-%d %H:%M:%S")

# Initialize counter for failures
failures=0

# Function to run a command and check its status
run_command() {
    local cmd="$1"
    local success_msg="$2"
    local fail_msg="$3"
    local output_file="$4"
    local start_time=$(date +%s.%N)
    
    echo "Running: $cmd"
    if eval "$cmd"; then
        local end_time=$(date +%s.%N)
        local duration_ms=$(printf "%.0f" "$(echo "($end_time - $start_time) * 1000" | bc)")
        echo "[SUCCESS] $success_msg"
        # Verify the output file exists and has content
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            # Add timestamp to the beginning of the file
            sed -i '' "1i\\
# Generated on: $TIMESTAMP\\
\\
" "$output_file"
            # Store success status and timing
            echo "SUCCESS:$output_file:$duration_ms" >> /tmp/report_status
        else
            echo "[WARNING] Report file is empty or missing: $output_file"
            failures=$((failures+1))
            # Store failure status and timing
            echo "FAIL:$output_file:$duration_ms" >> /tmp/report_status
        fi
    else
        local end_time=$(date +%s.%N)
        local duration_ms=$(printf "%.0f" "$(echo "($end_time - $start_time) * 1000" | bc)")
        echo "[FAIL] $fail_msg"
        failures=$((failures+1))
        # Store failure status and timing
        echo "FAIL:$output_file:$duration_ms" >> /tmp/report_status
    fi
}

# Function to run consolidated safety net and testing
run_consolidated_testing() {
    echo "🔍 Running Consolidated Safety Net & Testing"
    echo "==========================================="
    
    # Create temporary files for different outputs
    TEMP_PYTEST_LOG=$(mktemp)
    TEMP_RUFF_LOG=$(mktemp)
    
    # 1. Run ruff check first
    echo "Running code quality check..."
    ruff check src > "$TEMP_RUFF_LOG" 2>&1 || true
    
    # 2. Run all tests with coverage
    echo "Running all tests with coverage..."
    python -m pytest --cov=src/abb --cov-report=term-missing --color=no > "$TEMP_PYTEST_LOG" 2>&1 || true
    
    # 3. Combine into testing_log.md with safety net structure at top
    cat > docs/reports/testing_log.md << EOF
# Generated on: $TIMESTAMP

# SAFETY NET STATUS
==================

## Code Quality Check
\`\`\`
$(cat "$TEMP_RUFF_LOG")
\`\`\`

## Test Results & Coverage
$(cat "$TEMP_PYTEST_LOG")
EOF
    
    # Clean up temp files
    rm -f "$TEMP_PYTEST_LOG" "$TEMP_RUFF_LOG"
    
    # Check if testing_log.md was created successfully
    if [ -f "docs/reports/testing_log.md" ] && [ -s "docs/reports/testing_log.md" ]; then
        echo "SUCCESS:docs/reports/testing_log.md:0" >> /tmp/report_status
        echo "[SUCCESS] Consolidated testing completed"
    else
        echo "FAIL:docs/reports/testing_log.md:0" >> /tmp/report_status
        echo "[FAIL] Consolidated testing failed"
        failures=$((failures+1))
    fi
}

# Clear any existing status file
rm -f /tmp/report_status

echo "=== Starting Report Generation at $TIMESTAMP ==="
echo

# Run consolidated safety net and testing
run_consolidated_testing

echo
echo "=== Generating Code Reports ==="
echo

# 1. Run repomix for tests
run_command \
    "repomix --include 'tests/**/*.py' --remove-comments --output docs/reports/repo_tests.md" \
    "repomix (tests) completed" \
    "repomix (tests) command failed to execute" \
    "docs/reports/repo_tests.md"

# 2. Run repomix for src
run_command \
    "repomix --include 'src/**' --output docs/reports/repo_src.md" \
    "repomix (src) completed" \
    "repomix (src) command failed to execute" \
    "docs/reports/repo_src.md"

# Function to extract test results from testing_log.md
extract_test_results() {
    local log_file="$1"
    local test_line
    local passed=0
    local failed=0
    local errors=0
    local skipped=0
    local warnings=0
    
    if [ ! -f "$log_file" ]; then
        echo "0:0:0:0:0"
        return
    fi
    
    # Look for pytest summary line (e.g., "= 45 passed, 2 failed, 1 skipped in 12.34s =")
    test_line=$(grep -E "=[= ]+ .* (passed|failed|error|skipped).* =[= ]+" "$log_file" | tail -1)
    
    if [ -n "$test_line" ]; then
        # Extract numbers for each category
        passed=$(echo "$test_line" | grep -o '[0-9]\+ passed' | grep -o '[0-9]\+' || echo "0")
        failed=$(echo "$test_line" | grep -o '[0-9]\+ failed' | grep -o '[0-9]\+' || echo "0")
        errors=$(echo "$test_line" | grep -o '[0-9]\+ error' | grep -o '[0-9]\+' || echo "0")
        skipped=$(echo "$test_line" | grep -o '[0-9]\+ skipped' | grep -o '[0-9]\+' || echo "0")
        warnings=$(echo "$test_line" | grep -o '[0-9]\+ warning' | grep -o '[0-9]\+' || echo "0")
    fi
    
    echo "$passed:$failed:$errors:$skipped:$warnings"
}

# Function to extract coverage data from testing_log.md
extract_coverage_data() {
    local log_file="$1"
    local coverage_line
    local percentage="0"
    
    if [ ! -f "$log_file" ]; then
        echo "0"
        return
    fi
    
    # Look for TOTAL coverage line (e.g., "TOTAL    1234   567    54%")
    coverage_line=$(grep "TOTAL" "$log_file" | tail -1)
    
    if [ -n "$coverage_line" ]; then
        # Extract percentage (last column with %)
        percentage=$(echo "$coverage_line" | grep -o '[0-9]\+%' | grep -o '[0-9]\+' || echo "0")
    fi
    
    echo "$percentage"
}

# Function to extract lint data from testing_log.md
extract_lint_data() {
    local log_file="$1"
    local issues=0
    local found_line
    
    if [ ! -f "$log_file" ]; then
        echo "0"
        return
    fi
    
    # Count lines that contain "Found" (ruff output format)
    found_line=$(grep "Found [0-9]\+ error" "$log_file" | tail -1)
    if [ -n "$found_line" ]; then
        issues=$(echo "$found_line" | grep -o 'Found [0-9]\+' | grep -o '[0-9]\+' || echo "0")
    fi
    
    echo "$issues"
}

# Function to format test summary
format_test_summary() {
    local test_data="$1"
    local passed=$(echo "$test_data" | cut -d: -f1)
    local failed=$(echo "$test_data" | cut -d: -f2)
    local errors=$(echo "$test_data" | cut -d: -f3)
    local skipped=$(echo "$test_data" | cut -d: -f4)
    local warnings=$(echo "$test_data" | cut -d: -f5)
    
    local total=$((passed + failed + errors))
    local status_icon
    
    if [ $failed -eq 0 ] && [ $errors -eq 0 ]; then
        status_icon="✅"
    else
        status_icon="❌"
    fi
    
    echo "$status_icon Tests: $passed/$total passed"
    if [ $failed -gt 0 ]; then
        echo "    Failed: $failed"
    fi
    if [ $errors -gt 0 ]; then
        echo "    Errors: $errors"
    fi
    if [ $skipped -gt 0 ]; then
        echo "    Skipped: $skipped"
    fi
    if [ $warnings -gt 0 ]; then
        echo "    Warnings: $warnings"
    fi
}

# Function to format coverage summary
format_coverage_summary() {
    local coverage="$1"
    local status_icon
    
    if [ "$coverage" -ge 80 ]; then
        status_icon="✅"
    elif [ "$coverage" -ge 60 ]; then
        status_icon="⚠️"
    else
        status_icon="❌"
    fi
    
    echo "$status_icon Coverage: $coverage%"
}

# Function to format lint summary
format_lint_summary() {
    local issues="$1"
    local status_icon
    
    if [ "$issues" -eq 0 ]; then
        status_icon="✅"
    else
        status_icon="❌"
    fi
    
    echo "$status_icon Code Quality: $issues issues"
}

# Function to determine overall status
determine_overall_status() {
    local test_data="$1"
    local coverage="$2"
    local lint_issues="$3"
    
    local failed=$(echo "$test_data" | cut -d: -f2)
    local errors=$(echo "$test_data" | cut -d: -f3)
    
    # Status levels: PASS, WARNING, FAIL
    if [ $failed -eq 0 ] && [ $errors -eq 0 ] && [ "$lint_issues" -eq 0 ] && [ "$coverage" -ge 80 ]; then
        echo "PASS"
    elif [ $failed -eq 0 ] && [ $errors -eq 0 ] && [ "$coverage" -ge 60 ]; then
        echo "WARNING"
    else
        echo "FAIL"
    fi
}

# Function to get status text with icon
get_status_text() {
    local status="$1"
    
    case "$status" in
        "PASS")
            echo "✅ ALL CHECKS PASSED"
            ;;
        "WARNING")
            echo "⚠️  SOME ISSUES DETECTED"
            ;;
        "FAIL")
            echo "❌ CRITICAL ISSUES FOUND"
            ;;
        *)
            echo "❓ UNKNOWN STATUS"
            ;;
    esac
}

# Function to generate comprehensive summary section
generate_summary_section() {
    local log_file="docs/reports/testing_log.md"
    
    echo
    echo "=================================================================="
    echo "                    SAFETY NET STATUS SUMMARY"
    echo "=================================================================="
    
    # Extract data
    local test_data=$(extract_test_results "$log_file")
    local coverage=$(extract_coverage_data "$log_file")
    local lint_issues=$(extract_lint_data "$log_file")
    
    # Determine overall status
    local overall_status=$(determine_overall_status "$test_data" "$coverage" "$lint_issues")
    
    # Display overall status prominently
    echo
    echo "OVERALL STATUS: $(get_status_text "$overall_status")"
    echo
    echo "Detailed Results:"
    echo "-----------------"
    
    # Display formatted summaries
    format_test_summary "$test_data"
    format_coverage_summary "$coverage"
    format_lint_summary "$lint_issues"
    
    echo
    echo "=================================================================="
}

# Generate the enhanced summary
generate_summary_section
overall_status=$(determine_overall_status "$(extract_test_results 'docs/reports/testing_log.md')" "$(extract_coverage_data 'docs/reports/testing_log.md')" "$(extract_lint_data 'docs/reports/testing_log.md')")

echo
echo "Report Files Generated:"
echo "---------------------"
while IFS=: read -r status file duration_ms; do
    if [ "$status" = "SUCCESS" ]; then
        echo "✓ $(basename "$file"): ${duration_ms}ms"
    else
        echo "⚠ $(basename "$file"): ${duration_ms}ms"
    fi
done < /tmp/report_status

# Clean up
rm -f /tmp/report_status

# Exit based on overall status from enhanced summary
case "$overall_status" in
    "PASS")
        echo
        echo "✅ All safety net checks passed successfully"
        exit 0
        ;;
    "WARNING")
        echo
        echo "⚠️  Some issues detected - review required"
        exit 0  # Still allow CI to pass but with warnings
        ;;
    "FAIL")
        echo
        echo "❌ Critical issues found - immediate attention required"
        exit 1
        ;;
    *)
        # Fallback to original logic if status determination fails
        if [ $failures -eq 0 ]; then
            echo
            echo "✅ All checks passed successfully"
            exit 0
        else
            echo
            echo "⚠ $failures check(s) had issues"
            exit 1
        fi
        ;;
esac
