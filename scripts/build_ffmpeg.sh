#!/bin/bash
# Script to build FFmpeg with libfdk-aac support for macOS
# Creates a universal binary (arm64 + x86_64) for Audiobook Boss

set -e

# Define directories
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_DIR="${PROJECT_ROOT}/build/ffmpeg_build"
INSTALL_DIR="${PROJECT_ROOT}/src/abb/Resources/bin"

# Create directories
mkdir -p "${BUILD_DIR}"
mkdir -p "${INSTALL_DIR}"

# Change to build directory
cd "${BUILD_DIR}"

# Download and extract FFmpeg source
if [ ! -d "ffmpeg" ]; then
    echo "Downloading FFmpeg source..."
    git clone --depth 1 https://git.ffmpeg.org/ffmpeg.git ffmpeg
fi

# Download and build libfdk-aac
if [ ! -d "fdk-aac" ]; then
    echo "Downloading libfdk-aac source..."
    git clone --depth 1 https://github.com/mstorsjo/fdk-aac.git fdk-aac
    
    cd fdk-aac
    
    # Build for arm64
    echo "Building libfdk-aac for arm64..."
    ./autogen.sh
    ./configure --prefix="${BUILD_DIR}/fdk-aac-arm64" --enable-shared=no --enable-static=yes --with-pic=yes CFLAGS="-arch arm64"
    make -j$(sysctl -n hw.ncpu)
    make install
    make distclean
    
    # Build for x86_64
    echo "Building libfdk-aac for x86_64..."
    ./configure --prefix="${BUILD_DIR}/fdk-aac-x86_64" --enable-shared=no --enable-static=yes --with-pic=yes CFLAGS="-arch x86_64"
    make -j$(sysctl -n hw.ncpu)
    make install
    
    cd ..
fi

# Build FFmpeg with libfdk-aac
cd ffmpeg

# Build for arm64
echo "Building FFmpeg for arm64..."
./configure \
    --prefix="${BUILD_DIR}/ffmpeg-arm64" \
    --enable-static \
    --disable-shared \
    --enable-gpl \
    --enable-nonfree \
    --enable-libfdk-aac \
    --extra-cflags="-I${BUILD_DIR}/fdk-aac-arm64/include -arch arm64" \
    --extra-ldflags="-L${BUILD_DIR}/fdk-aac-arm64/lib -arch arm64" \
    --disable-doc \
    --disable-debug

make -j$(sysctl -n hw.ncpu)
make install
make distclean

# Build for x86_64
echo "Building FFmpeg for x86_64..."
./configure \
    --prefix="${BUILD_DIR}/ffmpeg-x86_64" \
    --enable-static \
    --disable-shared \
    --enable-gpl \
    --enable-nonfree \
    --enable-libfdk-aac \
    --extra-cflags="-I${BUILD_DIR}/fdk-aac-x86_64/include -arch x86_64" \
    --extra-ldflags="-L${BUILD_DIR}/fdk-aac-x86_64/lib -arch x86_64" \
    --disable-doc \
    --disable-debug

make -j$(sysctl -n hw.ncpu)
make install

# Create universal binaries
echo "Creating universal binaries..."
mkdir -p "${BUILD_DIR}/ffmpeg-universal/bin"

# Create universal binaries for ffmpeg, ffprobe, and ffplay
for binary in ffmpeg ffprobe; do
    lipo -create \
        "${BUILD_DIR}/ffmpeg-arm64/bin/${binary}" \
        "${BUILD_DIR}/ffmpeg-x86_64/bin/${binary}" \
        -output "${BUILD_DIR}/ffmpeg-universal/bin/${binary}"
done

# Copy universal binaries to the Resources/bin directory
echo "Copying universal binaries to Resources/bin..."
cp -f "${BUILD_DIR}/ffmpeg-universal/bin/"* "${INSTALL_DIR}/"

# Make binaries executable
chmod +x "${INSTALL_DIR}/"*

echo "FFmpeg with libfdk-aac successfully built and installed to ${INSTALL_DIR}"
