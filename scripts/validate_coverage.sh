#!/bin/bash
# Coverage validation script for ABB project
# Usage: scripts/validate_coverage.sh [minimum_coverage]

set -e

CURRENT_DIR=$(pwd)
MIN_COVERAGE=${1:-44}

echo "🧪 ABB Coverage Validation"
echo "=========================="
echo "Minimum required coverage: ${MIN_COVERAGE}%"
echo ""

# 1. Run full test suite
echo "1. Running full test suite..."
if ! python -m pytest -q; then
    echo "❌ TESTS FAILED - Cannot validate coverage with failing tests"
    exit 1
fi
echo "✅ All tests pass"

# 2. Check overall coverage
echo ""
echo "2. Checking overall coverage..."
COVERAGE_OUTPUT=$(python -m pytest --cov=abb --cov-report=term-missing --cov-report=json 2>/dev/null)
COVERAGE_PERCENT=$(python -c "
import json
with open('coverage.json') as f:
    data = json.load(f)
print(int(data['totals']['percent_covered']))
")

echo "Current coverage: ${COVERAGE_PERCENT}%"
if [ "$COVERAGE_PERCENT" -lt "$MIN_COVERAGE" ]; then
    echo "❌ COVERAGE TOO LOW - ${COVERAGE_PERCENT}% < ${MIN_COVERAGE}%"
    echo ""
    echo "Modules with 0% coverage:"
    echo "$COVERAGE_OUTPUT" | grep -E "^\S+\s+\d+\s+\d+\s+0%" || echo "None found"
    exit 1
fi
echo "✅ Coverage meets minimum requirement"

# 3. Check for any 0% coverage modules
echo ""
echo "3. Checking for untested modules..."
ZERO_COVERAGE=$(echo "$COVERAGE_OUTPUT" | grep -E "^\S+\s+\d+\s+\d+\s+0%" | wc -l)
if [ "$ZERO_COVERAGE" -gt 0 ]; then
    echo "❌ FOUND ${ZERO_COVERAGE} MODULES WITH 0% COVERAGE:"
    echo "$COVERAGE_OUTPUT" | grep -E "^\S+\s+\d+\s+\d+\s+0%"
    echo ""
    echo "All modules must have >0% coverage. Write tests before proceeding."
    exit 1
fi
echo "✅ No modules with 0% coverage"

# 4. Check linting
echo ""
echo "4. Checking code quality..."
if ! ruff check src/ --quiet; then
    echo "❌ LINTING ERRORS FOUND"
    ruff check src/
    exit 1
fi
echo "✅ No linting errors"

# Clean up
rm -f coverage.json

echo ""
echo "🎉 ALL VALIDATION CHECKS PASSED"
echo "Coverage: ${COVERAGE_PERCENT}% (>= ${MIN_COVERAGE}%)"
echo "Ready for phase completion or code commit."