#!/usr/bin/env python3
"""
Script to generate golden baseline for regression testing.
This documents how the golden.m4b was created.
"""

import json
import hashlib
import subprocess
import sys
from pathlib import Path

def calculate_sha256(file_path):
    """Calculate SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256.update(byte_block)
    return sha256.hexdigest()

def main():
    # Paths
    project_root = Path(__file__).parent.parent
    golden_dir = project_root / "tests" / "data" / "golden"
    raw_mp3 = golden_dir / "raw.mp3"
    golden_m4b = golden_dir / "golden.m4b"
    golden_json = golden_dir / "golden.json"
    
    if not raw_mp3.exists():
        print(f"Error: {raw_mp3} not found")
        sys.exit(1)
    
    # Settings used for golden run
    bitrate = "64k"
    
    # FFmpeg command (as shown in golden.json)
    cmd = [
        "ffmpeg",
        "-i", str(raw_mp3),
        "-map", "0:a",
        "-map", "0:v?",
        "-c:a", "libfdk_aac",
        "-c:v", "copy",
        "-b:a", bitrate,
        "-map_metadata", "0",
        "-map_chapters", "0",
        "-y",
        str(golden_m4b)
    ]
    
    print("Generating golden baseline...")
    print(f"Command: {' '.join(cmd)}")
    
    # Run FFmpeg
    result = subprocess.run(cmd, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error running FFmpeg: {result.stderr}")
        sys.exit(1)
    
    # Calculate hash
    sha256 = calculate_sha256(golden_m4b)
    print(f"Generated {golden_m4b}")
    print(f"SHA256: {sha256}")
    
    # Update golden.json
    golden_data = {
        "sha256": sha256,
        "ffmpeg_cmd": 'ffmpeg -i "$input_file" -map 0:a -map 0:v? -c:a libfdk_aac -c:v copy -b:a $convert_bit_rate -map_metadata 0 -map_chapters 0 -y "$output_file"',
        "metadata": {
            "title": "Golden Test Audiobook",
            "artist": "Test Author",
            "album": "Test Album",
            "year": "2025",
            "narrator": "Test Narrator",
            "comment": "Golden run test file"
        },
        "settings": {
            "bitrate": 64,
            "channels": 1
        },
        "notes": "This golden run was created with libfdk_aac codec at 64kbps mono"
    }
    
    with open(golden_json, "w") as f:
        json.dump(golden_data, f, indent=2)
    
    print(f"Updated {golden_json}")
    print("Golden baseline generation complete!")

if __name__ == "__main__":
    main()