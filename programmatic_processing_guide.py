#!/usr/bin/env python
"""
Comprehensive guide for processing audiobooks programmatically with ABB.

This script demonstrates multiple approaches:
1. Direct FFmpeg command execution (no Qt dependencies)
2. Using ABB's command builder utilities
3. Understanding the service architecture
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Optional, Any

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.abb.ffmpeg_utils import (
    setup_ffmpeg_path, 
    build_ffmpeg_processing_command, 
    _get_executable_path,
    get_audio_properties
)
from src.abb.metadata_utils import get_duration


class AudiobookProcessor:
    """Standalone audiobook processor without Qt dependencies."""
    
    def __init__(self):
        """Initialize the processor and setup FFmpeg."""
        setup_ffmpeg_path()
        self.ffmpeg_path = _get_executable_path("ffmpeg")
        if not self.ffmpeg_path:
            raise RuntimeError("FFmpeg not found in Resources/bin or PATH")
    
    def process_audiobook(
        self,
        input_files: List[str],
        output_path: str,
        metadata: Dict[str, Any],
        audio_settings: Optional[Dict[str, Any]] = None,
        cover_art_path: Optional[str] = None,
        progress_callback: Optional[callable] = None
    ) -> str:
        """Process audiobook files into M4B format.
        
        Args:
            input_files: List of input audio file paths
            output_path: Full path for output M4B file
            metadata: Metadata dictionary with fields like 'title', 'artist', etc.
            audio_settings: Audio settings (bitrate, channels, sample_rate)
            cover_art_path: Optional path to cover art image
            progress_callback: Optional callback function(percentage: int)
            
        Returns:
            Path to the created M4B file
        """
        # Default audio settings
        if audio_settings is None:
            audio_settings = {
                "bitrate": 64,      # kbps
                "channels": 1,      # mono
                "sample_rate": None # auto
            }
        
        # Validate input files
        for file_path in input_files:
            if not Path(file_path).exists():
                raise FileNotFoundError(f"Input file not found: {file_path}")
        
        # Create output directory
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Build FFmpeg command
        cmd = build_ffmpeg_processing_command(
            file_list=input_files,
            output_path=output_path,
            metadata_dict=metadata,
            cover_art_path=cover_art_path,
            audio_settings_dict=audio_settings,
            ffmpeg_path=str(self.ffmpeg_path)
        )
        
        # Execute with progress monitoring
        return self._execute_ffmpeg(cmd, input_files, progress_callback)
    
    def _execute_ffmpeg(
        self, 
        cmd: List[str], 
        input_files: List[str],
        progress_callback: Optional[callable] = None
    ) -> str:
        """Execute FFmpeg command with optional progress monitoring."""
        # Calculate total duration for progress tracking
        total_duration = sum(get_duration(f) for f in input_files)
        
        # Run FFmpeg process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # Monitor progress
        import re
        time_pattern = re.compile(r"time=(\d{2}):(\d{2}):(\d{2})\.(\d{2})")
        
        while True:
            line = process.stderr.readline()
            if not line:
                break
            
            # Parse progress
            match = time_pattern.search(line)
            if match and progress_callback and total_duration > 0:
                hours, minutes, seconds = map(int, match.groups()[:3])
                current_seconds = hours * 3600 + minutes * 60 + seconds
                percentage = min(100, int((current_seconds / total_duration) * 100))
                progress_callback(percentage)
        
        # Wait for completion
        process.wait()
        
        if process.returncode != 0:
            stderr = process.stderr.read()
            raise RuntimeError(f"FFmpeg failed: {stderr}")
        
        return cmd[-1]  # Return output file path


def example_basic_usage():
    """Basic example of processing an audiobook."""
    processor = AudiobookProcessor()
    
    # Example files (replace with actual paths)
    input_files = [
        "/path/to/chapter1.mp3",
        "/path/to/chapter2.mp3",
        "/path/to/chapter3.mp3"
    ]
    
    # Metadata
    metadata = {
        "title": "Example Audiobook",
        "artist": "John Doe",
        "album": "Example Series",
        "year": "2025",
        "genre": "Fiction",
        "narrator": "Jane Smith",
        "comment": "Created with ABB"
    }
    
    # Process
    output_file = processor.process_audiobook(
        input_files=input_files,
        output_path="/tmp/example_audiobook.m4b",
        metadata=metadata,
        audio_settings={"bitrate": 64, "channels": 1},
        progress_callback=lambda p: print(f"Progress: {p}%")
    )
    
    print(f"Created: {output_file}")


def analyze_audio_files(file_paths: List[str]):
    """Analyze audio files and display properties."""
    print("Audio File Analysis:")
    print("===================")
    
    total_duration = 0
    total_size = 0
    
    for file_path in file_paths:
        props, error = get_audio_properties(file_path)
        duration = get_duration(file_path)
        
        print(f"\nFile: {Path(file_path).name}")
        print(f"  Duration: {duration:.1f} seconds")
        print(f"  Bitrate: {props['bitrate']} kbps")
        print(f"  Sample Rate: {props['sample_rate']} Hz")
        print(f"  Channels: {props['channels']}")
        print(f"  Size: {props['file_size'] / 1024 / 1024:.1f} MB")
        
        if error:
            print(f"  Warning: {error}")
        
        total_duration += duration
        total_size += props['file_size']
    
    print(f"\nTotal Duration: {total_duration:.1f} seconds ({total_duration/60:.1f} minutes)")
    print(f"Total Size: {total_size / 1024 / 1024:.1f} MB")
    
    # Estimate output size
    output_bitrate = 64  # kbps
    estimated_size = (total_duration * output_bitrate * 1000 / 8) / (1024 * 1024)
    print(f"Estimated Output Size (at {output_bitrate}kbps): {estimated_size:.1f} MB")


def show_metadata_mapping():
    """Show how ABB metadata fields map to FFmpeg tags."""
    print("\nABB Metadata → FFmpeg Tag Mapping:")
    print("===================================")
    
    from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler
    
    # Use UnifiedMetadataHandler as single source of truth for mapping
    mappings = UnifiedMetadataHandler.get_metadata_mapping()
    for abb_field, ffmpeg_tag in mappings.items():
        print(f"  {abb_field:20} → {ffmpeg_tag}")
    
    print("\nSpecial handling:")
    print("  artist              → artist, album_artist (if no album_artist set)")
    print("  comment             → comment (fallback to description)")
    print("  series_sort         → album_sort (fallback to sort_album)")


def direct_ffmpeg_example():
    """Example of calling FFmpeg directly without ABB utilities."""
    print("\nDirect FFmpeg Command Example:")
    print("==============================")
    
    # This is what ABB generates internally
    cmd = [
        "ffmpeg",
        "-hide_banner", "-loglevel", "error", "-y",
        # Input files
        "-i", "chapter1.mp3",
        "-i", "chapter2.mp3",
        "-i", "cover.jpg",
        # Concatenate audio streams
        "-filter_complex", "[0:a][1:a]concat=n=2:v=0:a=1[outa]",
        "-map", "[outa]",
        # Map cover art
        "-map", "2:v",
        "-c:v", "copy",
        "-disposition:v", "attached_pic",
        # Audio encoding
        "-c:a", "libfdk_aac",  # or "aac" if libfdk not available
        "-b:a", "64k",
        "-ac", "1",
        # Metadata
        "-metadata", "title=My Audiobook",
        "-metadata", "artist=John Doe",
        "-metadata", "album=My Series",
        "-metadata", "date=2025",
        "-metadata", "genre=Fiction",
        "-metadata", "composer=Jane Narrator",  # narrator maps to composer
        # Output format
        "-f", "mp4",
        "-movflags", "+faststart",
        "output.m4b"
    ]
    
    print("Command:")
    print(" \\\n  ".join(cmd))
    
    print("\n\nTo execute:")
    print("subprocess.run(cmd, check=True)")


if __name__ == "__main__":
    print("ABB Programmatic Processing Guide")
    print("=================================\n")
    
    # Show different approaches
    print("1. Using AudiobookProcessor class (recommended):")
    print("   - No Qt dependencies")
    print("   - Progress monitoring")
    print("   - Error handling")
    print()
    
    print("2. Using ABB utilities directly:")
    print("   - build_ffmpeg_processing_command()")
    print("   - get_audio_properties()")
    print("   - get_duration()")
    print()
    
    print("3. Raw FFmpeg commands:")
    direct_ffmpeg_example()
    
    # Show metadata mapping
    show_metadata_mapping()
    
    # Example analysis (uncomment with real files)
    # analyze_audio_files(["/path/to/audio1.mp3", "/path/to/audio2.mp3"])
    
    print("\n\nNote: The ProcessingService class requires Qt and is designed for GUI integration.")
    print("For programmatic use without GUI, use the AudiobookProcessor class shown above.")