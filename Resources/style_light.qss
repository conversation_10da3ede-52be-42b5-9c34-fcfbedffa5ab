/* Light Mode Stylesheet for Audiobook Boss */

/* Main Window */
QMainWindow {
    background-color: #f5f5f5;
}

/* Splitter */
QSplitter::handle {
    background-color: #d0d0d0;
    width: 1px;
}

/* Panels */
QWidget#left_panel, QWidget#right_panel {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

/* Status Bar */
QStatusBar {
    background-color: #f0f0f0;
    color: #333333;
    border-top: 1px solid #d0d0d0;
}

/* Buttons */
QPushButton {
    background-color: #f0f0f0;
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 5px 10px;
    color: #333333;
}

QPushButton:hover {
    background-color: #e0e0e0;
}

QPushButton:pressed {
    background-color: #d0d0d0;
}

/* List Widget */
QListWidget {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

QListWidget::item {
    padding: 4px;
    border-bottom: 1px solid #f0f0f0;
}

QListWidget::item:selected {
    background-color: #e0e0f0;
    color: #333333;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    background-color: #ffffff;
    text-align: center;
    color: #333333;
}

QProgressBar::chunk {
    background-color: #4a86e8;
}

/* Group Box */
QGroupBox {
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    margin-top: 0.5em;
    padding-top: 0.5em;
    font-weight: bold;
    color: #333333;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 3px;
}

/* Line Edit */
QLineEdit {
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 3px;
    background-color: #ffffff;
    color: #333333;
}

/* Combo Box */
QComboBox {
    border: 1px solid #c0c0c0;
    border-radius: 4px;
    padding: 3px 18px 3px 3px;
    background-color: #ffffff;
    color: #333333;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 15px;
    border-left: 1px solid #c0c0c0;
}

/* Labels */
QLabel {
    color: #333333;
}
