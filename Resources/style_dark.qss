/* Dark Mode Stylesheet for Audiobook Boss */

/* Main Window */
QMainWindow {
    background-color: #2d2d2d;
}

/* Splitter */
QSplitter::handle {
    background-color: #444444;
    width: 1px;
}

/* Panels */
QWidget#left_panel, QWidget#right_panel {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 4px;
}

/* Status Bar */
QStatusBar {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border-top: 1px solid #444444;
}

/* Buttons */
QPushButton {
    background-color: #444444;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 5px 10px;
    color: #e0e0e0;
}

QPushButton:hover {
    background-color: #505050;
}

QPushButton:pressed {
    background-color: #606060;
}

/* List Widget */
QListWidget {
    background-color: #333333;
    border: 1px solid #444444;
    border-radius: 4px;
    color: #e0e0e0;
}

QListWidget::item {
    padding: 4px;
    border-bottom: 1px solid #3a3a3a;
}

QListWidget::item:selected {
    background-color: #4a4a6a;
    color: #ffffff;
}

/* Progress Bar */
QProgressBar {
    border: 1px solid #555555;
    border-radius: 4px;
    background-color: #333333;
    text-align: center;
    color: #e0e0e0;
}

QProgressBar::chunk {
    background-color: #4a86e8;
}

/* Group Box */
QGroupBox {
    border: 1px solid #444444;
    border-radius: 4px;
    margin-top: 0.5em;
    padding-top: 0.5em;
    font-weight: bold;
    color: #e0e0e0;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 3px;
}

/* Line Edit */
QLineEdit {
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 3px;
    background-color: #3a3a3a;
    color: #e0e0e0;
}

/* Combo Box */
QComboBox {
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 3px 18px 3px 3px;
    background-color: #3a3a3a;
    color: #e0e0e0;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 15px;
    border-left: 1px solid #555555;
}

/* Labels */
QLabel {
    color: #e0e0e0;
}
