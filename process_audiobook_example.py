#!/usr/bin/env python
"""
Example script showing how to process audiobook files programmatically using ABB.

This demonstrates:
1. Direct service usage without GUI
2. The exact FFmpeg command structure
3. How to provide input files and get output files
"""

import os
import sys
from pathlib import Path

# Add project root to path
PROJECT_ROOT = Path(__file__).parent
sys.path.insert(0, str(PROJECT_ROOT))

from src.abb.ffmpeg_utils import setup_ffmpeg_path, build_ffmpeg_processing_command, _get_executable_path
from src.abb.metadata_utils import get_duration


def process_audiobook_direct(
    input_files: list[str],
    output_directory: str,
    output_filename: str,
    metadata: dict,
    audio_settings: dict,
    cover_art_path: str = None
) -> str:
    """Process audiobook files directly using FFmpeg command.
    
    Args:
        input_files: List of input audio file paths
        output_directory: Directory for output file
        output_filename: Name for output file (e.g. "MyBook.m4b")
        metadata: Dictionary with keys like 'title', 'artist', 'album', etc.
        audio_settings: Dictionary with 'bitrate', 'channels', 'sample_rate'
        cover_art_path: Optional path to cover art image
        
    Returns:
        Full path to output file
    """
    # Ensure output directory exists
    os.makedirs(output_directory, exist_ok=True)
    output_path = os.path.join(output_directory, output_filename)
    
    # Get FFmpeg path
    ffmpeg_path = _get_executable_path("ffmpeg")
    if not ffmpeg_path:
        raise RuntimeError("FFmpeg not found")
    
    # Build the command
    cmd = build_ffmpeg_processing_command(
        file_list=input_files,
        output_path=output_path,
        metadata_dict=metadata,
        cover_art_path=cover_art_path,
        audio_settings_dict=audio_settings,
        ffmpeg_path=str(ffmpeg_path)
    )
    
    print("FFmpeg command:")
    print(" ".join(cmd))
    print()
    
    # Execute the command
    import subprocess
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"Success! Output file: {output_path}")
        return output_path
    else:
        print(f"Error: {result.stderr}")
        raise RuntimeError(f"FFmpeg failed with code {result.returncode}")


def example_usage():
    """Example of processing audiobook files."""
    # Setup FFmpeg path
    setup_ffmpeg_path()
    
    # Example input files (you would replace with actual files)
    input_files = [
        "/path/to/chapter1.mp3",
        "/path/to/chapter2.mp3",
        "/path/to/chapter3.mp3"
    ]
    
    # Metadata for the audiobook
    metadata = {
        "title": "My Audiobook",
        "artist": "John Author",
        "album": "My Audiobook Series",
        "year": "2025",
        "genre": "Fiction",
        "narrator": "Jane Narrator",
        "series": "Book Series",
        "series_pos": "1",
        "comment": "Processed with ABB"
    }
    
    # Audio settings
    audio_settings = {
        "bitrate": 64,      # kbps
        "channels": 1,      # 1=mono, 2=stereo
        "sample_rate": 22050  # Hz (optional, can be None for auto)
    }
    
    # Process the audiobook
    try:
        output_file = process_audiobook_direct(
            input_files=input_files,
            output_directory="/path/to/output",
            output_filename="MyAudiobook.m4b",
            metadata=metadata,
            audio_settings=audio_settings,
            cover_art_path="/path/to/cover.jpg"  # Optional
        )
        print(f"Audiobook created: {output_file}")
    except Exception as e:
        print(f"Error: {e}")


def show_ffmpeg_command_structure():
    """Show the exact FFmpeg command structure used by ABB."""
    print("FFmpeg Command Structure Used by ABB:")
    print("=====================================")
    print()
    print("Basic structure:")
    print("ffmpeg -hide_banner -loglevel error -y \\")
    print("  -i input1.mp3 -i input2.mp3 ... \\")
    print("  -i cover.jpg \\")
    print("  -filter_complex '[0:a][1:a]...concat=n=N:v=0:a=1[outa]' \\")
    print("  -map '[outa]' \\")
    print("  -map 'N:v' -c:v copy -disposition:v attached_pic \\")
    print("  -c:a libfdk_aac (or aac) \\")
    print("  -b:a 64k -ac 1 -ar 22050 \\")
    print("  -metadata title='Book Title' \\")
    print("  -metadata artist='Author Name' \\")
    print("  ... (more metadata) \\")
    print("  -f mp4 -movflags +faststart \\")
    print("  output.m4b")
    print()
    print("Key points:")
    print("- Multiple input files are concatenated using filter_complex")
    print("- Cover art is attached as a video stream with attached_pic disposition")
    print("- Audio codec is libfdk_aac if available, otherwise aac")
    print("- Output format is MP4 with faststart flag for streaming")
    print("- Metadata is mapped from ABB fields to FFmpeg tags")


if __name__ == "__main__":
    print("ABB Processing Service Test")
    print("===========================")
    print()
    
    # Show command structure
    show_ffmpeg_command_structure()
    print()
    
    # Uncomment to run example (requires actual files)
    # example_usage()
    
    # For testing with actual files, you can modify the paths:
    print("To test with actual files, modify the input_files list and run:")
    print("python process_audiobook_example.py")