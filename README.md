# Audiobook Boss (ABB)

A desktop application for processing audiobook files with a user-friendly interface.

## Features

- Import multiple audio files (MP3, M4A, M4B, AAC)
- Edit metadata (title, author, narrator, etc.)
- Add or extract cover art
- Configure output settings (bitrate, channels, etc.)
- Process files into a single M4B audiobook file
- Generate and preview short samples

## Project Map

For a comprehensive overview of the project structure, architecture, and development workflow, please see the [Project Map](docs/specs/PROJECT_MAP.md).

## Getting Started

### Prerequisites

- Python 3.8 or higher
- PySide6 (Qt for Python)
- FFmpeg with libfdk_aac support (bundled in Resources/bin)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Run the application:
   ```
   python src/abb/main.py
   ```

## Development

This project follows Test-Driven Development (TDD) principles and uses the Model-View-Controller (MVC) architectural pattern. For more details on the development workflow, see the [Project Map](docs/specs/PROJECT_MAP.md#development-workflow).

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/path/to/test_file.py

# Run tests with coverage report
pytest --cov=src
```

## Documentation

- [Implementation Plan](docs/specs/Implementation_plan.md)
- [Refactoring Plan](docs/specs/working_refractor_plan.md)
- [Test Refactoring Plan](docs/specs/final_test_refactor_plan.md)