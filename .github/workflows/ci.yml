name: Python CI

on:
  push:
    branches: [ "dev", "main" ]
  pull_request:
    branches: [ "dev" ]
  schedule:
    # Nightly run at 02:00 UTC for full test suite
    - cron: '0 2 * * *'

jobs:
  # Fast tests - always run for quick feedback
  fast-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov ruff

    - name: Lint with ruff
      run: |
        ruff check src

    - name: Run unit tests
      run: |
        pytest tests/unit -v

    - name: Run fast integration tests
      run: |
        pytest tests/integration/test_file_import_integration.py tests/integration/test_file_properties_integration.py -v

    - name: Generate fast test coverage
      run: |
        pytest tests/unit tests/integration/test_file_import_integration.py tests/integration/test_file_properties_integration.py --cov=abb --cov-report=xml --cov-report=html

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: |
          coverage.xml
          htmlcov/

  # Slow tests - conditional execution
  slow-tests:
    runs-on: ubuntu-latest
    if: >
      github.event_name == 'schedule' ||
      github.ref == 'refs/heads/main' ||
      (github.event_name == 'pull_request' && github.base_ref == 'dev') ||
      contains(github.event.head_commit.message, '[ci full]')
    
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov ruff

    - name: Run full test suite
      run: |
        pytest tests/ -v

    - name: Generate full coverage report
      run: |
        pytest tests/ --cov=abb --cov-report=xml --cov-report=html

    - name: Upload full coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./coverage.xml
        fail_ci_if_error: true

    - name: Upload full coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: full-coverage-report
        path: |
          coverage.xml
          htmlcov/

  # Golden baseline test - only on main branch changes
  golden-baseline:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || contains(github.event.head_commit.message, '[ci golden]')
    
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: 3.9

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov

    - name: Run golden baseline test
      run: |
        pytest tests/integration/test_golden.py -v

    - name: Upload golden test artifacts
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: golden-test-results
        path: tests/data/golden/