{"python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.packageManager": "uv", "python.linting.enabled": true, "python.linting.lintOnSave": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.linting.mypyEnabled": true, "ruff.enable": true, "ruff.nativeServer": true, "ruff.importStrategy": "fromEnvironment", "ruff.lint.select": ["E", "F", "I", "B", "D"], "ruff.lineLength": 100, "ruff.configuration": {"format": {"quote-style": "double", "indent-style": "space", "docstring-code-format": true, "line-ending": "auto"}, "lint": {"pydocstyle": {"convention": "google"}, "isort": {"known-first-party": ["abb"]}}}, "[python]": {"editor.defaultFormatter": "charliermarsh.ruff", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.ruff": "explicit", "source.organizeImports.ruff": "explicit"}}, "editor.formatOnSave": true}