import hashlib
import json
import pathlib
import time
from unittest.mock import <PERSON>Mock, patch

import pytest
from PySide6.QtCore import <PERSON><PERSON><PERSON><PERSON><PERSON>, QTimer

# Path to the golden run data
GOLDEN_DATA_PATH = pathlib.Path(__file__).parent.parent / "data" / "golden"
GOLDEN_JSON_PATH = GOLDEN_DATA_PATH / "golden.json"
RAW_MP3_PATH = GOLDEN_DATA_PATH / "raw.mp3"
GOLDEN_M4B_PATH = GOLDEN_DATA_PATH / "golden.m4b"

@pytest.fixture
def golden_data():
    """Fixture to load the golden run data from JSON."""
    if not GOLDEN_JSON_PATH.exists():
        pytest.skip("Golden run data not found. Run the baseline generation first.")
    with open(GOLDEN_JSON_PATH, "r") as f:
        return json.load(f)

@pytest.fixture
def qt_application(qt_app):
    """Use pytest-qt's application fixture from conftest.py."""
    return qt_app

def calculate_sha256(file_path: pathlib.Path) -> str:
    """Calculates the SHA256 hash of a file."""
    sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256.update(byte_block)
    return sha256.hexdigest()

@pytest.mark.fast
def test_golden_ffmpeg_command_generation_fast(golden_data):
    """Fast test: Validate FFmpeg command generation without Qt dependencies.
    
    This test runs in <100ms and validates command structure only.
    """
    start_time = time.time()
    
    from src.abb.ffmpeg.command_builder import FFmpegCommandBuilder
    
    # Mock codec availability check to ensure libfdk_aac is used
    def mock_codec_check(codec_name):
        return codec_name == "libfdk_aac"
    
    builder = FFmpegCommandBuilder(mock_codec_check)
    
    # Build command using golden data
    cmd = builder.build_ffmpeg_command(
        input_files=[str(RAW_MP3_PATH)],
        output_file_full_path=str(GOLDEN_DATA_PATH / "test_output.m4b"),
        metadata=golden_data["metadata"],
        settings=golden_data["settings"],
        ffmpeg_exe_path="ffmpeg",
        ffprobe_exe_path=None
    )
    
    # Verify key command elements
    assert "ffmpeg" in cmd
    assert str(RAW_MP3_PATH) in cmd
    assert "-c:a" in cmd
    assert "libfdk_aac" in cmd
    assert "-b:a" in cmd
    assert "64k" in cmd  # bitrate from settings
    assert "-ac" in cmd
    assert "1" in cmd  # channels from settings
    
    # Verify metadata
    assert "-metadata" in cmd
    assert "title=Golden Test Audiobook" in " ".join(cmd)
    assert "artist=Test Author" in " ".join(cmd)
    
    # Ensure test runs fast
    elapsed = time.time() - start_time
    assert elapsed < 0.1, f"Fast test took {elapsed:.3f}s, should be <0.1s"

@pytest.mark.slow
@pytest.mark.integration
def test_processing_service_with_golden_data_mocked(golden_data, tmp_path, qt_application):
    """Slow test: Test processing service with mocked FFmpeg execution.
    
    This test uses Qt components and validates the full service integration.
    """
    from src.abb.services.processing_service import ProcessingService
    
    # Create a mock that simulates successful FFmpeg execution
    with patch('src.abb.processing_worker.QProcess') as mock_qprocess_class:
        mock_process = MagicMock()
        mock_qprocess_class.return_value = mock_process
        
        # Mock the process execution
        mock_process.state.return_value = MagicMock(return_value=0)  # Not running
        mock_process.exitCode.return_value = 0  # Success
        mock_process.exitStatus.return_value = 0  # Normal exit
        
        # Create processor
        processor = ProcessingService()
        
        # Track signals
        finished_signal_received = []
        error_signal_received = []
        
        try:
            processor.finished.connect(lambda path: finished_signal_received.append(path))
            processor.error.connect(lambda msg: error_signal_received.append(msg))
            
            # Process with golden data
            processor.process_full(
                input_files=[str(RAW_MP3_PATH)],
                output_path=str(tmp_path),
                output_filename="test_output.m4b",
                metadata=golden_data["metadata"],
                settings=golden_data["settings"]
            )
            
            # Verify FFmpeg was called
            assert mock_process.start.called, "FFmpeg process should have been started"
            
        finally:
            # Always clean up resources
            try:
                processor.cleanup_worker_resources()
            except Exception as e:
                # Log cleanup error but don't fail test
                print(f"Warning: Cleanup error: {e}")

@pytest.mark.slow
@pytest.mark.integration
@pytest.mark.skip(reason="Requires actual FFmpeg execution - enable for full integration test")
def test_refactored_output_matches_golden_hash(golden_data, tmp_path, qt_application):
    """Test if the refactored app produces bit-identical output to golden.m4b.
    
    This test requires FFmpeg to be installed and will actually process audio.
    Run with: pytest -m "slow" --disable-warnings -v
    """
    from src.abb.services.processing_service import ProcessingService
    
    # Define the output path for the new conversion
    output_file = tmp_path / "test_output.m4b"
    
    # Create processor
    processor = ProcessingService()
    
    # Set up event loop to wait for processing
    loop = QEventLoop()
    processing_finished = False
    processing_error = None
    
    def on_finished(path):
        nonlocal processing_finished
        processing_finished = True
        loop.quit()
    
    def on_error(msg):
        nonlocal processing_error
        processing_error = msg
        loop.quit()
    
    try:
        processor.finished.connect(on_finished)
        processor.error.connect(on_error)
        
        # Start processing
        processor.process_full(
            input_files=[str(RAW_MP3_PATH)],
            output_path=str(tmp_path),
            output_filename="test_output.m4b",
            metadata=golden_data["metadata"],
            settings=golden_data["settings"]
        )
        
        # Set timeout (60 seconds for actual FFmpeg processing)
        timeout_timer = QTimer()
        timeout_timer.setSingleShot(True)
        timeout_timer.timeout.connect(loop.quit)
        timeout_timer.start(60000)
        
        # Wait for completion
        loop.exec()
        
        # Check for timeout
        if not processing_finished and processing_error is None:
            pytest.fail("Processing timed out after 60 seconds")
        
        # Check for processing error
        if processing_error:
            pytest.fail(f"Processing failed with error: {processing_error}")
        
        # Verify output exists
        assert output_file.exists(), f"Output file {output_file} was not created"
        
        # Calculate the hash of the newly generated file
        new_hash = calculate_sha256(output_file)
        
        # Get the golden hash from our fixture
        golden_hash = golden_data["sha256"]
        
        # Assert that the hashes are identical
        assert new_hash == golden_hash, f"Hash mismatch: got {new_hash}, expected {golden_hash}"
        
    finally:
        # Always clean up resources
        try:
            processor.cleanup_worker_resources()
        except Exception as e:
            # Log cleanup error but don't fail test
            print(f"Warning: Cleanup error: {e}")