import pytest
import logging
from pathlib import Path
from PySide6.QtTest import QSignalSpy

from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager
from src.abb.ui.widgets.left_panel_widget import LeftPanelWidget


class TestFileImportIntegration:
    """Test integration between components for file import"""
    
    def test_file_drop_updates_ui_through_controller(self, qtbot, tmp_path):
        """Dropping files updates UI through proper controller flow"""
        # Create a settings manager for the controller
        settings_file = tmp_path / "settings.json"
        settings_manager = SettingsManager(str(settings_file), {})
        
        # Set up components
        controller = MainController(settings_manager=settings_manager)
        left_panel = LeftPanelWidget()
        qtbot.addWidget(left_panel)
        
        # Connect signals
        left_panel.files_dropped_signal.connect(controller.add_files)
        controller.file_list_updated_signal.connect(left_panel.update_file_list_display)
        
        # Create test file
        test_file = tmp_path / "test.mp3"
        test_file.touch()
        
        # Simulate file drop
        left_panel.files_dropped_signal.emit([str(test_file)])
        qtbot.wait(100)
        
        # Verify UI updated
        assert left_panel.file_list_widget.count() == 1
        assert left_panel.file_list_widget.item(0).text() == "test.mp3"
