import pytest
import logging
from unittest.mock import patch
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager
from src.abb.services.file_service import FileService

class TestFilePropertiesIntegration:
    """Test integration between file selection and property display"""
    
    def test_file_selection_triggers_metadata_extraction(self, qtbot, tmp_path):
        """Selecting a file triggers metadata extraction"""
        # Create a settings manager for the controller
        settings_file = tmp_path / "settings.json"
        settings_manager = SettingsManager(str(settings_file), {})
        
        # Initialize controller with settings manager
        controller = MainController(settings_manager=settings_manager)
        
        test_file = tmp_path / "file.mp3"
        test_file.write_bytes(b"x" * 100)
        
        # Add file first
        controller.add_files([str(test_file)])
        
        # Mock metadata extraction
        with patch.object(controller._metadata_service, 'extract_metadata') as mock_extract:
            mock_extract.return_value = {
                'title': 'Test',
                'artist': 'Artist'
            }
            
            # Spy on the signal
            spy = QSignalSpy(controller.selected_file_data_changed)
            
            # Select the file
            controller.handle_file_selection_changed([str(test_file)])
            
            # Should have extracted metadata
            mock_extract.assert_called_once_with(str(test_file))
            
            # Should have emitted signal
            assert spy.count() == 1 