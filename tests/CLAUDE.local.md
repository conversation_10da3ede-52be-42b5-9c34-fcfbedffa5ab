# Testing Guidelines

## 🚨 CRITICAL REQUIREMENT: TDD MANDATORY 🚨
**RED → <PERSON><PERSON><PERSON> → REFACTOR - NO EXCEPTIONS**
- **BEFORE writing ANY new code**: Write failing test first
- **NO module can be created without corresponding test file**
- **Coverage gate**: Any module with 0% coverage = CRITICAL FAILURE
- **Agent accountability**: Agents creating untested code violate project standards

## Core Testing Philosophy
- Test behavior, not implementation
- Use real objects over mocks when possible
- Isolate external dependencies only
- Focus on user workflows and outcomes
- **Goal**: Average < 3 mocks per test file

## Critical Testing Infrastructure
1. **CI/CD Required**: Never refactor without automated tests
2. **Coverage Tracking**: `pytest --cov=abb --cov-report=xml`
3. **Golden Run**: Baseline comparison for regression detection
4. **PR Validation**: All tests must pass before merge
5. **Coverage Gate**: Run `scripts/validate_coverage.sh` before ANY code commit
6. **Zero Tolerance**: Any 0% coverage module blocks all further development

## Mock Reduction Strategy
1. **Identify over-mocked tests** (>5 mocks = red flag)
2. **Replace with real objects** where safe
3. **Create test fixtures** for common scenarios
4. **Mock only external deps** (filesystem, FFmpeg)
5. **Track progress**: Count mocks per file, aim for <3 average

## Testing Patterns

### Behavior Testing Example
```python
# BAD: Testing implementation
def test_signal_connection():
    assert widget.signal.connect.called_with(controller.slot)

# GOOD: Testing behavior
def test_file_appears_in_list():
    controller.add_files(['test.mp3'])
    assert 'test.mp3' in widget.file_list
```

### Service Testing Without Qt
```python
# Pure Python service with callbacks
class FileService:
    def add_files(self, files, on_success=None):
        # logic here
        if on_success:
            on_success(processed_files)

# Test without Qt
def test_file_service():
    result = []
    service = FileService()
    service.add_files(['test.mp3'], on_success=result.append)
    assert len(result) == 1
```

## Integration Test Structure
```python
# tests/integration/test_user_workflows.py
def test_complete_audiobook_workflow():
    # 1. Add files
    app.add_files(['chapter1.mp3', 'chapter2.mp3'])
    
    # 2. Verify properties displayed
    assert app.total_duration == '2:30:00'
    
    # 3. Process with settings
    app.process(bitrate='64k', channels='mono')
    
    # 4. Verify output
    assert Path('output.m4b').exists()
```

## Golden Run Testing
```python
# tests/test_golden_run.py
def test_golden_run_regression():
    """Ensure output matches baseline after refactoring."""
    # 1. Load golden baseline
    with open('tests/data/golden/golden.json') as f:
        golden = json.load(f)
    
    # 2. Process test file
    output = process_audiobook('tests/data/golden/sample.mp3', {
        'bitrate': '64k',
        'channels': 'mono'
    })
    
    # 3. Compare SHA256
    output_hash = hashlib.sha256(output.read()).hexdigest()
    assert output_hash == golden['sha256'], "Output changed!"
```

## Test Priorities
1. **Safety First**: Golden run must pass before any refactoring
2. **Core flows**: Add files → Process → Output
3. **Error handling**: Invalid files, disk space, permissions
4. **Edge cases**: Empty files, huge files, special characters
5. **UI interactions**: Drag/drop, selection, progress

## Fixture Guidelines
- One fixture per common scenario
- Minimal setup, clear naming
- Reusable across test modules
- Document assumptions clearly
- **Avoid complex fixtures**: If >20 lines, consider real objects

## Refactoring Test Strategy
1. **Before Changes**: Write tests for current behavior
2. **During Changes**: Update tests incrementally
3. **After Changes**: Add integration tests for new patterns
4. **Regression**: Always run golden comparison