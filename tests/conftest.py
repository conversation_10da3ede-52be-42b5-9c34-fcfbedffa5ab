import pytest
import tempfile
import logging
from pathlib import Path
from PySide6.QtCore import QSettings, Qt
from PySide6.QtWidgets import <PERSON><PERSON>ox<PERSON><PERSON><PERSON>, QSplitter, QStatusBar, QWidget
from src.abb.main_window import MainWindow

@pytest.fixture(scope="session")
def qt_app(qapp):
    """
    Session-scoped Qt application for PySide6 tests.
    This fixture relies on and returns pytest-qt's `qapp` fixture.
    """
    return qapp

@pytest.fixture
def sample_audio_files_with_properties(tmp_path):
    """Create sample audio files and their expected properties for property/size tests."""
    files = []
    # File 1
    file1 = tmp_path / "chapter01.mp3"
    file1.write_bytes(b"dummy" * 1000)
    files.append({
        'path': str(file1),
        'properties': {
            'bitrate': 320,
            'sample_rate': 48000,
            'channels': 2,
            'duration': 180.5,
            'file_size': file1.stat().st_size
        }
    })
    # File 2
    file2 = tmp_path / "chapter02.m4a"
    file2.write_bytes(b"dummy" * 500)
    files.append({
        'path': str(file2),
        'properties': {
            'bitrate': 128,
            'sample_rate': 44100,
            'channels': 1,
            'duration': 240.0,
            'file_size': file2.stat().st_size
        }
    })
    return files

@pytest.fixture
def main_window_with_monkeypatch(qtbot, monkeypatch, tmp_path):
    """MainWindow with system dependency checks disabled for tests."""
    monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
    
    # Create a temporary settings file for tests
    settings_file = tmp_path / "settings.json"
    settings_file.parent.mkdir(exist_ok=True)
    
    # Patch the MainWindow to use the temporary settings file
    def mock_init(self):
        super(MainWindow, self).__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(1280, 820)
        
        self.settings = QSettings("AudiobookBoss", "ABB")
        self.logger = logging.getLogger("AudiobookBoss.MainWindow")
        
        self.app_state = {
            "file_list": [],
            "metadata": {},
            "settings": {},
            "processing_state": {},
        }
        
        # Use temporary settings file for tests
        from src.abb.services.settings_manager import SettingsManager
        self._settings_manager = SettingsManager(str(settings_file), self.app_state["settings"])
        
        # Initialize controller with settings manager
        from src.abb.controllers.main_controller import MainController
        self._controller = MainController(settings_manager=self._settings_manager)
        
        # Continue with normal initialization
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(12, 12, 12, 10)
        self.main_layout.setSpacing(8)
        
        self.splitter = QSplitter(Qt.Horizontal)
        
        self.left_panel = QWidget()
        self.left_panel.setObjectName("left_panel")
        self.right_panel = QWidget()
        self.right_panel.setObjectName("right_panel")
        
        self._setup_left_panel()
        self._setup_right_panel()
        
        self._setup_property_labels()
        self.setup_controller_connections()
        
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        self.splitter.setSizes([33, 67])
        
        self.main_layout.addWidget(self.splitter)
        
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        self._setup_menu_bar()
        self.apply_theme()
    
    monkeypatch.setattr(MainWindow, '__init__', mock_init)
    
    # Create the window with the patched initialization
    window = MainWindow()
    qtbot.addWidget(window)
    return window

# Add new fixtures here as your new tests require them.
