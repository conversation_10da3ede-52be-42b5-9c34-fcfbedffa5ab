"""Comprehensive tests for QtServiceAdapterBase.

This module tests the foundation adapter class that provides common patterns
and utilities for bridging pure Python services with Qt signal/slot mechanisms.

Critical behaviors tested:
- Thread-safe signal emission
- Callback registration and management
- Error handling and propagation
- Resource cleanup and memory management
- Weak reference tracking
- Thread pool management
- Abstract method enforcement
"""

import gc
import threading
import time
from typing import List
from unittest.mock import Mock

import pytest
from PySide6.QtCore import QObject, Signal

from src.abb.services.adapters.base_adapter import QtServiceAdapterBase, QtServiceAdapterMixin


class MockCoreService:
    """Mock core service for testing adapters."""

    def __init__(self):
        """Initialize mock core service."""
        self.data = "test_data"
        self.call_count = 0

    def test_method(self):
        """Test method for adapter delegation."""
        self.call_count += 1
        return f"result_{self.call_count}"


class ConcreteTestAdapter(QtServiceAdapterBase[MockCoreService]):
    """Concrete implementation of QtServiceAdapterBase for testing."""

    # Test signals
    test_signal = Signal(str)
    data_signal = Signal(dict)

    def __init__(self, core_service: MockCoreService, parent: QObject = None):
        """Initialize test adapter."""
        super().__init__(core_service, parent)
        self.initialize_called = False
        self.cleanup_called = False
        self.initialize_adapter()

    def initialize_adapter(self) -> None:
        """Initialize adapter-specific functionality."""
        self.initialize_called = True

    def cleanup_adapter(self) -> None:
        """Clean up adapter-specific resources."""
        self.cleanup_called = True

    def trigger_test_signal(self, message: str) -> None:
        """Helper method to trigger test signal."""
        self.emit_signal_safe(self.test_signal, message)

    def get_core_service(self) -> MockCoreService:
        """Get the core service for testing."""
        service = self.core_service
        if service is None:
            raise RuntimeError("Core service has been garbage collected")
        return service


class CallbackCapture:
    """Utility for capturing callback invocations."""

    def __init__(self):
        """Initialize callback capture."""
        self.calls: List[tuple] = []
        self.call_count = 0
        self._lock = threading.Lock()

    def callback(self, *args, **kwargs):
        """Capture callback invocation."""
        with self._lock:
            self.calls.append((args, kwargs))
            self.call_count += 1

    def error_callback(self, error: Exception, context: str = ""):
        """Capture error callback invocation."""
        with self._lock:
            self.calls.append(("error", error, context))
            self.call_count += 1

    def reset(self):
        """Reset captured calls."""
        with self._lock:
            self.calls.clear()
            self.call_count = 0


class TestQtServiceAdapterBase:
    """Test suite for QtServiceAdapterBase foundation functionality."""

    @pytest.fixture
    def mock_core_service(self):
        """Create a mock core service for testing."""
        return MockCoreService()

    @pytest.fixture
    def adapter(self, mock_core_service):
        """Create a test adapter instance."""
        adapter = ConcreteTestAdapter(mock_core_service)
        return adapter

    @pytest.fixture
    def callback_capture(self):
        """Create a callback capture utility."""
        return CallbackCapture()

    def test_initialization(self, adapter, mock_core_service):
        """Test adapter initializes correctly with core service."""
        # Verify core service is stored as weak reference
        assert adapter.core_service is mock_core_service
        assert adapter._core_service_ref() is mock_core_service

        # Verify initialization state
        assert not adapter.is_destroyed
        assert adapter.initialize_called
        assert not adapter.cleanup_called

        # Verify internal structures are initialized
        assert isinstance(adapter._callbacks, dict)
        assert isinstance(adapter._error_handlers, set)
        assert isinstance(adapter._cleanup_callbacks, list)
        assert adapter._thread_pool is not None
        assert adapter._signal_queue_timer is not None

    def test_adapter_lifecycle_signals(self, mock_core_service, qtbot):
        """Test adapter emits lifecycle signals correctly."""
        adapter = ConcreteTestAdapter(mock_core_service)

        # Test destruction signal
        with qtbot.waitSignal(adapter.adapter_destroyed, timeout=1000):
            adapter.cleanup()

    def test_core_service_weak_reference(self, adapter):
        """Test core service is stored as weak reference to prevent cycles."""
        core_service = adapter.core_service
        assert core_service is not None

        # Delete the original reference
        original_service = adapter._core_service_ref()
        del core_service

        # Service should still be accessible through adapter
        assert adapter.core_service is original_service

        # When original service is deleted, weak reference should return None
        del original_service
        gc.collect()  # Force garbage collection

        # Note: In practice, the service might still be referenced elsewhere
        # This test verifies the weak reference mechanism is in place
        assert hasattr(adapter, "_core_service_ref")
        assert callable(adapter._core_service_ref)

    def test_callback_registration_and_management(self, adapter, callback_capture):
        """Test callback registration, emission, and cleanup."""
        event_name = "test_event"

        # Register callback
        adapter.register_callback(event_name, callback_capture.callback)
        assert event_name in adapter._callbacks
        assert len(adapter._callbacks[event_name]) == 1

        # Emit to callbacks
        test_args = ("arg1", "arg2")
        test_kwargs = {"key": "value"}
        adapter.emit_to_callbacks(event_name, *test_args, **test_kwargs)

        assert callback_capture.call_count == 1
        assert callback_capture.calls[0] == (test_args, test_kwargs)

        # Unregister callback
        result = adapter.unregister_callback(event_name, callback_capture.callback)
        assert result is True
        assert len(adapter._callbacks[event_name]) == 0

        # Try to unregister non-existent callback
        result = adapter.unregister_callback(event_name, callback_capture.callback)
        assert result is False

    def test_callback_weak_reference_cleanup(self, adapter):
        """Test callbacks are stored as weak references and cleaned up."""
        event_name = "test_event"

        def test_callback():
            pass

        # Register callback
        adapter.register_callback(event_name, test_callback)
        assert len(adapter._callbacks[event_name]) == 1

        # Delete callback reference
        del test_callback
        gc.collect()

        # Emit to callbacks - should clean up dead references
        adapter.emit_to_callbacks(event_name)

        # Dead reference should be removed
        assert len(adapter._callbacks[event_name]) == 0

    def test_thread_safe_signal_emission_main_thread(self, adapter, qtbot):
        """Test signal emission from main thread."""
        test_message = "test_message"

        # Emit signal from main thread
        with qtbot.waitSignal(adapter.test_signal, timeout=1000) as blocker:
            adapter.trigger_test_signal(test_message)

        # Verify signal was emitted with correct data
        assert blocker.args == [test_message]

    def test_thread_safe_signal_emission_background_thread(self, adapter, qtbot):
        """Test signal emission from background thread gets queued."""
        test_message = "background_message"
        signal_emitted = threading.Event()
        received_args = []

        def signal_handler(*args):
            received_args.extend(args)
            signal_emitted.set()

        adapter.test_signal.connect(signal_handler)

        def background_emit():
            adapter.trigger_test_signal(test_message)

        # Emit from background thread
        thread = threading.Thread(target=background_emit)
        thread.start()
        thread.join()

        # Wait for signal to be processed
        assert signal_emitted.wait(timeout=2.0)
        assert received_args == [test_message]

    def test_error_handling_and_propagation(self, adapter, qtbot, callback_capture):
        """Test error handling and propagation through adapter."""
        # Register error handler
        adapter.register_error_handler(callback_capture.error_callback)

        # Test service error handling
        test_error = ValueError("Test error")
        test_context = "test_context"

        with qtbot.waitSignal(adapter.error_occurred, timeout=1000) as blocker:
            adapter.handle_service_error(test_error, test_context)

        # Verify error signal was emitted
        error_type, error_message = blocker.args
        assert error_type == "ValueError"
        assert test_context in error_message
        assert str(test_error) in error_message

        # Verify error handler was called
        assert callback_capture.call_count == 1
        call_args = callback_capture.calls[0]
        assert call_args[0] == "error"
        assert call_args[1] is test_error
        assert call_args[2] == test_context

    def test_async_operation_execution(self, adapter):
        """Test async operations are executed in thread pool."""
        result_container = []
        execution_thread = []

        def async_function(value):
            execution_thread.append(threading.current_thread())
            result_container.append(value * 2)
            return value * 2

        # Run async operation
        adapter.run_async(async_function, 5)

        # Wait for completion
        time.sleep(0.1)

        # Verify operation was executed
        assert len(result_container) == 1
        assert result_container[0] == 10

        # Verify it ran in a different thread
        assert len(execution_thread) == 1
        assert execution_thread[0] != threading.current_thread()

    def test_async_operation_error_handling(self, adapter, qtbot):
        """Test async operation error handling."""

        def failing_function():
            raise ValueError("Async error")

        # Run failing async operation
        with qtbot.waitSignal(adapter.error_occurred, timeout=1000) as blocker:
            adapter.run_async(failing_function)

        # Verify error signal was emitted
        error_type, error_message = blocker.args
        assert error_type == "async_error"
        assert "Async error" in error_message

    def test_cleanup_callback_management(self, adapter):
        """Test cleanup callbacks are registered and executed."""
        cleanup_called = []

        def cleanup_callback():
            cleanup_called.append(True)

        # Add cleanup callback
        adapter.add_cleanup_callback(cleanup_callback)
        assert len(adapter._cleanup_callbacks) == 1

        # Cleanup adapter
        adapter.cleanup()

        # Verify cleanup callback was called
        assert len(cleanup_called) == 1
        assert cleanup_called[0] is True

    def test_resource_cleanup_on_destruction(self, adapter, qtbot):
        """Test all resources are cleaned up on adapter destruction."""

        # Add some callbacks and handlers
        def test_callback():
            pass

        def error_handler(error, context):
            pass

        adapter.register_callback("test", test_callback)
        adapter.register_error_handler(error_handler)

        # Verify resources are present
        assert len(adapter._callbacks) > 0
        assert len(adapter._error_handlers) > 0
        assert not adapter._thread_pool._shutdown

        # Cleanup adapter
        with qtbot.waitSignal(adapter.adapter_destroyed, timeout=1000):
            adapter.cleanup()

        # Verify resources are cleaned up
        assert adapter.is_destroyed
        assert len(adapter._callbacks) == 0
        assert len(adapter._error_handlers) == 0
        assert len(adapter._cleanup_callbacks) == 0
        assert adapter._thread_pool._shutdown

    def test_destroyed_adapter_behavior(self, adapter):
        """Test adapter behavior after destruction."""
        # Cleanup adapter
        adapter.cleanup()
        assert adapter.is_destroyed

        # Test operations on destroyed adapter
        callback_capture = CallbackCapture()

        # Should not register callbacks
        adapter.register_callback("test", callback_capture.callback)
        assert len(adapter._callbacks) == 0

        # Should not emit signals
        adapter.emit_signal_safe(adapter.test_signal, "test")
        # No assertion needed - should not crash

        # Should not run async operations
        adapter.run_async(lambda: None)
        # No assertion needed - should not crash

    def test_callback_error_handling(self, adapter, qtbot):
        """Test error handling in callbacks."""

        def failing_callback(*args, **kwargs):
            raise RuntimeError("Callback error")

        # Register failing callback
        adapter.register_callback("test_event", failing_callback)

        # Emit to callbacks - should handle error gracefully
        with qtbot.waitSignal(adapter.error_occurred, timeout=1000) as blocker:
            adapter.emit_to_callbacks("test_event", "test_arg")

        # Verify error was handled
        error_type, error_message = blocker.args
        assert error_type == "callback_error"
        assert "Callback error" in error_message

    def test_signal_queue_processing(self, adapter):
        """Test queued signal processing mechanism."""
        # Verify signal queue is initially empty
        assert len(adapter._signal_queue) == 0

        # Add signals to queue manually (simulating background thread)
        with adapter._signal_queue_lock:
            adapter._signal_queue.append((adapter.test_signal, ("queued_message",)))

        # Process queued signals
        adapter._process_queued_signals()

        # Verify queue is cleared
        assert len(adapter._signal_queue) == 0

    def test_concurrent_callback_operations(self, adapter):
        """Test thread safety of callback operations."""
        callback_calls = []

        def thread_safe_callback(*args):
            callback_calls.append(args)

        # Register callback from multiple threads
        def register_callback():
            adapter.register_callback("concurrent_test", thread_safe_callback)

        def emit_callback():
            adapter.emit_to_callbacks("concurrent_test", "test_data")

        # Run concurrent operations
        threads = []
        for _ in range(5):
            t1 = threading.Thread(target=register_callback)
            t2 = threading.Thread(target=emit_callback)
            threads.extend([t1, t2])

        for thread in threads:
            thread.start()

        for thread in threads:
            thread.join()

        # Verify no exceptions occurred and callbacks were called
        assert len(callback_calls) >= 0  # Some callbacks should have been called


class TestQtServiceAdapterMixin:
    """Test suite for QtServiceAdapterMixin utility functions."""

    @pytest.fixture
    def mock_adapter(self):
        """Create a mock adapter for mixin testing."""
        adapter = Mock(spec=QtServiceAdapterBase)
        adapter._logger = Mock()
        return adapter

    def test_create_error_handler(self, mock_adapter):
        """Test error handler creation utility."""
        error_signal = Mock(spec=Signal)

        # Create error handler
        handler = QtServiceAdapterMixin.create_error_handler(mock_adapter, error_signal)

        # Test error handling
        test_error = ValueError("Test error")
        test_context = "test_context"
        handler(test_error, test_context)

        # Verify emit_signal_safe was called correctly
        mock_adapter.emit_signal_safe.assert_called_once_with(
            error_signal, "ValueError", f"{test_context}: {str(test_error)}"
        )

    def test_create_callback_wrapper(self, mock_adapter):
        """Test callback wrapper creation utility."""
        test_signal = Mock(spec=Signal)

        # Create callback wrapper
        wrapper = QtServiceAdapterMixin.create_callback_wrapper(mock_adapter, test_signal)

        # Test callback with args only
        wrapper("arg1", "arg2")
        mock_adapter.emit_signal_safe.assert_called_with(test_signal, "arg1", "arg2")

        # Test callback with kwargs (should log warning)
        mock_adapter.reset_mock()
        wrapper("arg1", key="value")
        mock_adapter.emit_signal_safe.assert_called_with(test_signal, "arg1")
        mock_adapter._logger.warning.assert_called_once()
