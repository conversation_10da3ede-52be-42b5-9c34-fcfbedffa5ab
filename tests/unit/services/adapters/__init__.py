"""Test package for service adapter layer.

This package contains comprehensive tests for Qt service adapters that bridge
pure Python core services with Qt signal/slot mechanisms.

Test modules:
- test_base_adapter: Foundation adapter testing
- test_settings_manager_adapter: Settings adapter testing  
- test_file_service_adapter: File service adapter testing
- test_metadata_handler_adapter: Metadata adapter testing
- test_processing_service_adapter: Processing adapter testing
"""
