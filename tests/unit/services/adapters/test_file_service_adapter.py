"""Comprehensive tests for QtFileServiceAdapter.

This module tests the Qt adapter for FileServiceCore that maintains
backwards compatibility with the original FileService interface.

Critical behaviors tested:
- Qt signal emission for file operations
- Core service delegation
- Callback-to-signal translation
- Backwards compatibility
- Error handling
- Thread safety
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest
from PySide6.QtCore import QObject

from src.abb.services.adapters.file_service_adapter import QtFileServiceAdapter
from src.abb.services.core.file_service_core import FileServiceCore


class TestQtFileServiceAdapter:
    """Test suite for QtFileServiceAdapter functionality."""

    @pytest.fixture
    def temp_audio_files(self):
        """Create temporary audio files for testing."""
        files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(mode="w", suffix=".mp3", delete=False) as f:
                f.write(f"fake audio content {i}")
                files.append(f.name)

        yield files

        # Cleanup
        for file_path in files:
            Path(file_path).unlink(missing_ok=True)

    @pytest.fixture
    def adapter(self):
        """Create a QtFileServiceAdapter instance for testing."""
        return QtFileServiceAdapter()

    @pytest.fixture
    def mock_core_service(self):
        """Create a mock core service for testing."""
        return Mock(spec=FileServiceCore)

    def test_initialization(self, adapter):
        """Test adapter initializes correctly with core service."""
        # Verify adapter is a QObject
        assert isinstance(adapter, QObject)

        # Verify core service is created
        assert adapter._core is not None
        assert isinstance(adapter._core, FileServiceCore)

        # Verify initial state
        assert adapter.get_files() == []
        assert adapter.get_combined_size() == 0  # Returns int, not string
        assert adapter.format_combined_size() == "0 B"  # This returns formatted string

    def test_add_files_delegates_to_core(self, adapter):
        """Test add_files delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)
        test_files = ["/path/to/file1.mp3", "/path/to/file2.mp3"]

        # Call adapter method
        result = adapter.add_files(test_files)

        # Verify delegation (adapter creates its own callbacks)
        adapter._core.add_files.assert_called_once()
        call_args = adapter._core.add_files.call_args
        assert call_args[0][0] == test_files  # First argument should be the files
        assert callable(call_args[0][1])  # Second argument should be success callback
        assert callable(call_args[0][2])  # Third argument should be error callback

    def test_get_files_delegates_to_core(self, adapter):
        """Test get_files delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)
        expected_files = ["/path/to/file1.mp3", "/path/to/file2.mp3"]
        adapter._core.get_files.return_value = expected_files

        # Call adapter method
        result = adapter.get_files()

        # Verify delegation
        adapter._core.get_files.assert_called_once()
        assert result == expected_files

    def test_get_combined_size_delegates_to_core(self, adapter):
        """Test get_combined_size delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)
        expected_size = 1572864  # Size in bytes
        adapter._core.get_combined_size.return_value = expected_size

        # Call adapter method
        result = adapter.get_combined_size()

        # Verify delegation
        adapter._core.get_combined_size.assert_called_once()
        assert result == expected_size

    def test_remove_file_delegates_to_core(self, adapter):
        """Test remove_file delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)
        test_index = 1

        # Call adapter method
        adapter.remove_file(test_index)

        # Verify delegation (adapter creates its own callbacks)
        adapter._core.remove_file.assert_called_once()
        call_args = adapter._core.remove_file.call_args
        assert call_args[0][0] == test_index  # First argument should be the index
        assert callable(call_args[0][1])  # Second argument should be success callback
        assert callable(call_args[0][2])  # Third argument should be error callback

    def test_clear_files_delegates_to_core(self, adapter):
        """Test clear_files delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Call adapter method
        adapter.clear_files()

        # Verify delegation
        adapter._core.clear_files.assert_called_once_with(
            success_callback=adapter._on_files_changed, error_callback=adapter._on_files_error
        )

    def test_reorder_files_delegates_to_core(self, adapter):
        """Test reorder_files delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)
        test_indices = [2, 0, 1]

        # Call adapter method
        adapter.reorder_files(test_indices)

        # Verify delegation
        adapter._core.reorder_files.assert_called_once_with(
            test_indices,
            success_callback=adapter._on_files_changed,
            error_callback=adapter._on_files_error,
        )

    def test_files_changed_signal_emission_on_add(self, adapter, qtbot):
        """Test files_changed signal is emitted correctly on add_files."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Configure mock to call success callback
        def mock_add_files(files, success_callback, error_callback):
            success_callback(files)

        adapter._core.add_files.side_effect = mock_add_files
        adapter._core.get_files.return_value = ["/path/to/file1.mp3"]

        test_files = ["/path/to/file1.mp3"]

        # Test signal emission
        with qtbot.waitSignal(adapter.files_changed, timeout=1000) as blocker:
            adapter.add_files(test_files)

        # Verify signal was emitted with file list
        assert isinstance(blocker.args[0], list)

    def test_files_changed_signal_emission(self, adapter, qtbot):
        """Test files_changed signal is emitted correctly."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Configure mock to call success callback
        def mock_remove_file(index, success_callback, error_callback):
            success_callback()

        adapter._core.remove_file.side_effect = mock_remove_file

        # Test signal emission
        with qtbot.waitSignal(adapter.files_changed, timeout=1000):
            adapter.remove_file(0)

    def test_combined_size_changed_signal_emission(self, adapter, qtbot):
        """Test combined_size_changed_signal is emitted correctly."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Configure mock to call success callback
        def mock_add_files(files, success_callback, error_callback):
            success_callback(files)

        adapter._core.add_files.side_effect = mock_add_files
        adapter._core.format_combined_size.return_value = "1.5 MB"

        test_files = ["/path/to/file1.mp3"]

        # Test signal emission
        with qtbot.waitSignal(adapter.combined_size_changed_signal, timeout=1000) as blocker:
            adapter.add_files(test_files)

        # Verify signal was emitted with correct size
        assert blocker.args == ["1.5 MB"]

    def test_error_handling_maintains_compatibility(self, adapter):
        """Test error handling maintains backwards compatibility."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Configure mock to call error callback
        def mock_add_files(files, success_callback, error_callback):
            error_callback("Test error message")

        adapter._core.add_files.side_effect = mock_add_files

        test_files = ["/path/to/file1.mp3"]

        # Should not raise exception (maintains original behavior)
        try:
            result = adapter.add_files(test_files)
            # Should return empty list on error
            assert result == []
        except Exception as e:
            pytest.fail(f"add_files should not raise exceptions, but raised: {e}")

    def test_backwards_compatibility_interface(self, adapter):
        """Test adapter maintains exact interface of original FileService."""
        # Verify all expected methods exist
        assert hasattr(adapter, "add_files")
        assert hasattr(adapter, "get_files")
        assert hasattr(adapter, "get_combined_size")
        assert hasattr(adapter, "remove_file")
        assert hasattr(adapter, "clear_files")
        assert hasattr(adapter, "reorder_files")

        # Verify signals exist
        assert hasattr(adapter, "files_changed")
        assert hasattr(adapter, "combined_size_changed_signal")

    def test_thread_safety_signal_emission(self, adapter, qtbot):
        """Test signal emission works correctly from different contexts."""
        # Mock the core service
        adapter._core = Mock(spec=FileServiceCore)

        # Configure mock to call success callback
        def mock_add_files(files, success_callback, error_callback):
            success_callback(files)

        adapter._core.add_files.side_effect = mock_add_files

        # Test multiple rapid signal emissions
        test_files1 = ["/path/to/file1.mp3"]
        test_files2 = ["/path/to/file2.mp3"]

        with qtbot.waitSignal(adapter.files_added, timeout=1000):
            adapter.add_files(test_files1)

        with qtbot.waitSignal(adapter.files_added, timeout=1000):
            adapter.add_files(test_files2)

    def test_adapter_with_real_core_service(self, adapter, temp_audio_files):
        """Test adapter works correctly with real core service."""
        # Test adding files
        initial_count = len(adapter.get_files())
        assert initial_count == 0

        # Add files (note: will fail validation since temp files aren't real audio)
        # But we can test the interface
        adapter.add_files(temp_audio_files[:1])

        # Test getting combined size
        size = adapter.get_combined_size()
        assert isinstance(size, str)
        assert "B" in size  # Should contain byte unit

    def test_error_handling_maintains_original_behavior(self, adapter):
        """Test error handling maintains original FileService behavior."""
        # Mock the core service to simulate various error conditions
        adapter._core = Mock(spec=FileServiceCore)

        # Test that errors don't propagate (original behavior)
        def mock_add_files_with_error(files, success_callback, error_callback):
            error_callback("Simulated error")

        adapter._core.add_files.side_effect = mock_add_files_with_error

        # Should not raise exception (maintains original behavior)
        try:
            adapter.add_files(["/fake/file.mp3"])
        except Exception as e:
            pytest.fail(f"add_files should not raise exceptions, but raised: {e}")

    def test_core_service_integration(self, adapter):
        """Test integration between adapter and core service."""
        # Test that adapter properly integrates with core service
        initial_files = adapter.get_files()
        assert initial_files == []

        # Test combined size calculation
        size = adapter.get_combined_size()
        assert size == "0 B"  # Empty file list should be 0 bytes
