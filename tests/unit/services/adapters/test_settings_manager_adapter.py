"""Comprehensive tests for QtSettingsManagerAdapter.

This module tests the Qt adapter for SettingsManagerCore that maintains
backwards compatibility with the original SettingsManager interface.

Critical behaviors tested:
- Qt signal emission for settings changes
- Core service delegation
- Callback-to-signal translation
- Backwards compatibility
- Error handling
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest
from PySide6.QtCore import QObject

from src.abb.services.adapters.settings_manager_adapter import QtSettingsManagerAdapter
from src.abb.services.core.settings_manager_core import SettingsManagerCore


class TestQtSettingsManagerAdapter:
    """Test suite for QtSettingsManagerAdapter functionality."""

    @pytest.fixture
    def temp_settings_file(self):
        """Create a temporary settings file for testing."""
        with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
            f.write('{"test_setting": "test_value"}')
            temp_path = f.name

        yield temp_path

        # Cleanup
        Path(temp_path).unlink(missing_ok=True)

    @pytest.fixture
    def default_settings(self):
        """Create default settings for testing."""
        return {"output_directory": "/default/output", "bitrate": "64k", "channels": "mono"}

    @pytest.fixture
    def adapter(self, temp_settings_file, default_settings):
        """Create a QtSettingsManagerAdapter instance for testing."""
        return QtSettingsManagerAdapter(temp_settings_file, default_settings)

    @pytest.fixture
    def mock_core_service(self):
        """Create a mock core service for testing."""
        return Mock(spec=SettingsManagerCore)

    def test_initialization(self, adapter, temp_settings_file, default_settings):
        """Test adapter initializes correctly with core service."""
        # Verify adapter is a QObject
        assert isinstance(adapter, QObject)

        # Verify core service is created
        assert adapter._core is not None
        assert isinstance(adapter._core, SettingsManagerCore)

        # Verify settings file path is stored for backwards compatibility
        assert adapter.settings_file_path == temp_settings_file

    def test_get_setting_delegates_to_core(self, adapter):
        """Test get_setting delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=SettingsManagerCore)
        adapter._core.get_setting.return_value = "test_value"

        # Call adapter method
        result = adapter.get_setting("test_key", "default_value")

        # Verify delegation
        adapter._core.get_setting.assert_called_once_with("test_key", "default_value")
        assert result == "test_value"

    def test_get_all_settings_delegates_to_core(self, adapter):
        """Test get_all_settings delegates to core service."""
        # Mock the core service
        adapter._core = Mock(spec=SettingsManagerCore)
        expected_settings = {"key1": "value1", "key2": "value2"}
        adapter._core.get_all_settings.return_value = expected_settings

        # Call adapter method
        result = adapter.get_all_settings()

        # Verify delegation
        adapter._core.get_all_settings.assert_called_once()
        assert result == expected_settings

    def test_set_setting_emits_signal_on_success(self, adapter, qtbot):
        """Test set_setting emits settings_changed signal on successful update."""
        # Mock the core service
        adapter._core = Mock(spec=SettingsManagerCore)

        # Configure mock to call success callback
        def mock_set_setting(name, value, success_callback, error_callback):
            success_callback(name, value)

        adapter._core.set_setting.side_effect = mock_set_setting

        # Test signal emission
        with qtbot.waitSignal(adapter.settings_changed, timeout=1000) as blocker:
            adapter.set_setting("test_key", "test_value")

        # Verify signal was emitted with correct arguments
        assert blocker.args == ["test_key", "test_value"]

        # Verify core service was called
        adapter._core.set_setting.assert_called_once()
        call_args = adapter._core.set_setting.call_args
        assert call_args[0] == ("test_key", "test_value")

    def test_set_setting_handles_error_gracefully(self, adapter):
        """Test set_setting handles errors without raising exceptions."""
        # Mock the core service
        adapter._core = Mock(spec=SettingsManagerCore)

        # Configure mock to call error callback
        def mock_set_setting(name, value, success_callback, error_callback):
            error_callback("Test error message")

        adapter._core.set_setting.side_effect = mock_set_setting

        # Call set_setting - should not raise exception
        adapter.set_setting("test_key", "test_value")

        # Verify core service was called
        adapter._core.set_setting.assert_called_once()

    def test_backwards_compatibility_interface(self, adapter):
        """Test adapter maintains exact interface of original SettingsManager."""
        # Verify all expected methods exist
        assert hasattr(adapter, "get_setting")
        assert hasattr(adapter, "set_setting")
        assert hasattr(adapter, "get_all_settings")
        assert hasattr(adapter, "settings_file_path")

        # Verify signal exists
        assert hasattr(adapter, "settings_changed")

        # Verify method signatures match original
        import inspect

        # get_setting should accept name and optional default_value
        sig = inspect.signature(adapter.get_setting)
        params = list(sig.parameters.keys())
        assert "name" in params
        assert "default_value" in params

        # set_setting should accept name and value
        sig = inspect.signature(adapter.set_setting)
        params = list(sig.parameters.keys())
        assert "name" in params
        assert "value" in params

    def test_signal_emission_thread_safety(self, adapter, qtbot):
        """Test signal emission works correctly from different contexts."""
        # Mock the core service
        adapter._core = Mock(spec=SettingsManagerCore)

        # Configure mock to call success callback
        def mock_set_setting(name, value, success_callback, error_callback):
            success_callback(name, value)

        adapter._core.set_setting.side_effect = mock_set_setting

        # Test multiple rapid signal emissions
        with qtbot.waitSignal(adapter.settings_changed, timeout=1000):
            adapter.set_setting("key1", "value1")

        with qtbot.waitSignal(adapter.settings_changed, timeout=1000):
            adapter.set_setting("key2", "value2")

    def test_adapter_with_real_core_service(self, temp_settings_file, default_settings, qtbot):
        """Test adapter works correctly with real core service."""
        # Create adapter with real core service
        adapter = QtSettingsManagerAdapter(temp_settings_file, default_settings)

        # Test setting and getting values
        initial_value = adapter.get_setting("bitrate")
        assert initial_value == "64k"  # From default settings

        # Test setting new value with signal emission
        with qtbot.waitSignal(adapter.settings_changed, timeout=1000) as blocker:
            adapter.set_setting("bitrate", "128k")

        # Verify signal was emitted correctly
        assert blocker.args == ["bitrate", "128k"]

        # Verify value was actually set
        new_value = adapter.get_setting("bitrate")
        assert new_value == "128k"

    def test_error_handling_maintains_original_behavior(self, adapter):
        """Test error handling maintains original SettingsManager behavior."""
        # Mock the core service to simulate various error conditions
        adapter._core = Mock(spec=SettingsManagerCore)

        # Test that errors don't propagate (original behavior)
        def mock_set_setting_with_error(name, value, success_callback, error_callback):
            error_callback("Simulated error")

        adapter._core.set_setting.side_effect = mock_set_setting_with_error

        # Should not raise exception (maintains original behavior)
        try:
            adapter.set_setting("test_key", "test_value")
        except Exception as e:
            pytest.fail(f"set_setting should not raise exceptions, but raised: {e}")

    def test_initialization_with_none_defaults(self, temp_settings_file):
        """Test adapter initialization with None default settings."""
        adapter = QtSettingsManagerAdapter(temp_settings_file, None)

        # Should initialize successfully
        assert adapter._core is not None
        assert adapter.settings_file_path == temp_settings_file

    def test_core_service_integration(self, adapter):
        """Test integration between adapter and core service."""
        # Test that adapter properly integrates with core service
        original_value = adapter.get_setting("test_setting")

        # Set a new value
        adapter.set_setting("new_key", "new_value")

        # Verify the value can be retrieved
        retrieved_value = adapter.get_setting("new_key")
        assert retrieved_value == "new_value"

        # Verify all settings include both original and new
        all_settings = adapter.get_all_settings()
        assert "test_setting" in all_settings
        assert "new_key" in all_settings
