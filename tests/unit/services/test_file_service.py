import pytest
from pathlib import Path
from PySide6.QtTest import QSignalSpy
from unittest.mock import Mock

from src.abb.services.file_service import FileService


class TestFileService:
    """Unit tests for FileService component"""
    
    def test_add_files_filters_extensions(self, tmp_path):
        """FileService only accepts valid audio extensions"""
        service = FileService()
        
        # Create test files
        valid = tmp_path / "test.mp3"
        invalid = tmp_path / "test.txt"
        valid.touch()
        invalid.touch()
        
        # Add files
        added = service.add_files([str(valid), str(invalid)])
        
        # Only valid file added
        assert len(added) == 1
        assert added[0] == str(valid.resolve())
    
    def test_add_files_prevents_duplicates(self, tmp_path):
        """FileService prevents duplicate files"""
        service = FileService()
        
        file = tmp_path / "test.mp3"
        file.touch()
        
        # Add same file twice
        service.add_files([str(file)])
        added = service.add_files([str(file)])
        
        # Second add should return empty
        assert len(added) == 0
        assert len(service.get_files()) == 1
    
    def test_reorder_files(self, tmp_path):
        """FileService can reorder files"""
        service = FileService()
        
        # Add multiple files
        files = []
        for i in range(3):
            f = tmp_path / f"file{i}.mp3"
            f.touch()
            files.append(str(f))
        
        service.add_files(files)
        
        # Get current files and create new order (move index 2 to index 0)
        current_files = service.get_files()
        new_order = [current_files[2], current_files[0], current_files[1]]
        service.reorder_files(new_order)
        
        result = service.get_files()
        assert Path(result[0]).name == "file2.mp3"
        assert Path(result[1]).name == "file0.mp3"
        assert Path(result[2]).name == "file1.mp3"
    
    def test_file_service_reorder_files_updates_internal_list_and_emits_signal(self, tmp_path):
        """FileService.reorder_files accepts list of paths, updates internal list and emits signal"""
        service = FileService()
        
        # Create test files
        file_a = tmp_path / "file_a.mp3"
        file_b = tmp_path / "file_b.mp3"
        file_c = tmp_path / "file_c.mp3"
        file_a.touch()
        file_b.touch()
        file_c.touch()
        
        # Initialize FileService with predefined list
        initial_files = [str(file_a), str(file_b), str(file_c)]
        service.add_files(initial_files)
        
        # Connect mock slot to files_changed signal
        mock_slot = Mock()
        service.files_changed.connect(mock_slot)
        
        # Define new order
        new_order_paths = [str(file_c), str(file_a), str(file_b)]
        
        # Call reorder_files with new order
        service.reorder_files(new_order_paths)
        
        # Assert internal file list matches new order
        current_files = service.get_files()
        assert current_files == [str(file_c.resolve()), str(file_a.resolve()), str(file_b.resolve())]
        
        # Assert files_changed signal was emitted exactly once
        mock_slot.assert_called_once()
        # Verify the signal was called with the correct file list
        mock_slot.assert_called_with([str(file_c.resolve()), str(file_a.resolve()), str(file_b.resolve())])
