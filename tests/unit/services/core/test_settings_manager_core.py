"""Tests for SettingsManagerCore - Pure Python settings manager without Qt dependencies.

This module tests the SettingsManagerCore implementation which provides:
- JSON-based settings persistence
- Thread-safe settings management
- Callback-based communication (no Qt signals)
- Settings validation for specific setting types
- Error handling for file I/O operations

Following TDD principles and established testing patterns from other core services.
"""

import json
import os
import tempfile
import threading
from unittest.mock import Mock, mock_open, patch

import pytest

from src.abb.services.core.settings_manager_core import SettingsManagerCore


class TestSettingsManagerCore:
    """Test suite for SettingsManagerCore functionality."""

    @pytest.fixture
    def temp_settings_file(self):
        """Create a temporary settings file for testing."""
        fd, path = tempfile.mkstemp(suffix=".json")
        os.close(fd)
        yield path
        # Cleanup
        if os.path.exists(path):
            os.remove(path)

    @pytest.fixture
    def default_settings(self):
        """Default settings for testing."""
        return {
            "output_bitrate": 128,
            "output_directory": "/tmp/output",
            "output_filename_pattern": 0,
            "some_other_setting": "default_value",
        }

    def test_initialization_with_defaults_no_file(self, temp_settings_file, default_settings):
        """Test initialization with default settings when no file exists."""
        # Ensure file doesn't exist
        if os.path.exists(temp_settings_file):
            os.remove(temp_settings_file)

        manager = SettingsManagerCore(temp_settings_file, default_settings)

        assert manager.get_setting("output_bitrate") == 128
        assert manager.get_setting("output_directory") == "/tmp/output"
        assert manager.get_setting("output_filename_pattern") == 0
        assert manager.get_setting("some_other_setting") == "default_value"

    def test_initialization_loads_from_existing_file(self, temp_settings_file, default_settings):
        """Test initialization loads and merges settings from existing file."""
        # Create file with some settings
        file_settings = {
            "output_bitrate": 96,
            "output_directory": "/home/<USER>/music",
            "new_setting_from_file": "loaded",
        }
        with open(temp_settings_file, "w") as f:
            json.dump(file_settings, f)

        manager = SettingsManagerCore(temp_settings_file, default_settings)

        # File settings should override defaults
        assert manager.get_setting("output_bitrate") == 96
        assert manager.get_setting("output_directory") == "/home/<USER>/music"
        # Default settings should be preserved if not in file
        assert manager.get_setting("output_filename_pattern") == 0
        assert manager.get_setting("some_other_setting") == "default_value"
        # New settings from file should be loaded
        assert manager.get_setting("new_setting_from_file") == "loaded"

    @patch("src.abb.services.core.settings_manager_core.logger")
    def test_initialization_handles_corrupt_json_file(
        self, mock_logger, temp_settings_file, default_settings
    ):
        """Test initialization handles corrupt JSON file gracefully."""
        # Create corrupt JSON file
        with open(temp_settings_file, "w") as f:
            f.write("this is not valid json")

        manager = SettingsManagerCore(temp_settings_file, default_settings)

        # Should fall back to defaults
        assert manager.get_setting("output_bitrate") == 128
        assert manager.get_setting("output_directory") == "/tmp/output"

        # Should log warning
        mock_logger.warning.assert_called_with(
            f"Corrupt settings file: {temp_settings_file}. Using default settings."
        )

    @patch("src.abb.services.core.settings_manager_core.logger")
    def test_initialization_handles_io_error(
        self, mock_logger, temp_settings_file, default_settings
    ):
        """Test initialization handles I/O errors gracefully."""
        # Create file but make it unreadable
        with open(temp_settings_file, "w") as f:
            json.dump({"test": "value"}, f)

        # Mock open to raise IOError
        with patch("builtins.open", side_effect=IOError("Permission denied")):
            manager = SettingsManagerCore(temp_settings_file, default_settings)

        # Should fall back to defaults
        assert manager.get_setting("output_bitrate") == 128

        # Should log error
        mock_logger.error.assert_called_with(
            f"Error reading settings file {temp_settings_file}: Permission denied"
        )

    def test_get_setting_returns_correct_values(self, temp_settings_file):
        """Test get_setting returns correct values and defaults."""
        manager = SettingsManagerCore(temp_settings_file)

        # Test with no settings
        assert manager.get_setting("nonexistent") is None
        assert manager.get_setting("nonexistent", "default") == "default"

        # Add a setting and test retrieval
        success_callback = Mock()
        manager.set_setting("test_key", "test_value", success_callback)

        assert manager.get_setting("test_key") == "test_value"
        assert manager.get_setting("test_key", "default") == "test_value"

    def test_set_setting_valid_values(self, temp_settings_file):
        """Test set_setting with valid values."""
        manager = SettingsManagerCore(temp_settings_file)
        success_callback = Mock()
        error_callback = Mock()

        # Test setting a valid value
        manager.set_setting("test_key", "test_value", success_callback, error_callback)

        # Should call success callback
        success_callback.assert_called_once_with("test_key", "test_value")
        error_callback.assert_not_called()

        # Should persist to memory
        assert manager.get_setting("test_key") == "test_value"

        # Should persist to file
        with open(temp_settings_file, "r") as f:
            saved_settings = json.load(f)
            assert saved_settings["test_key"] == "test_value"

    def test_set_setting_validation_output_bitrate(self, temp_settings_file):
        """Test set_setting validation for output_bitrate."""
        manager = SettingsManagerCore(temp_settings_file)
        success_callback = Mock()
        error_callback = Mock()

        # Test valid bitrate
        manager.set_setting("output_bitrate", 128, success_callback, error_callback)
        success_callback.assert_called_once_with("output_bitrate", 128)
        error_callback.assert_not_called()
        assert manager.get_setting("output_bitrate") == 128

        # Reset mocks
        success_callback.reset_mock()
        error_callback.reset_mock()

        # Test invalid bitrate
        manager.set_setting("output_bitrate", 999, success_callback, error_callback)
        success_callback.assert_not_called()
        error_callback.assert_called_once()
        # Should not update the setting
        assert manager.get_setting("output_bitrate") == 128  # Previous valid value

    def test_set_setting_validation_output_directory(self, temp_settings_file):
        """Test set_setting validation for output_directory."""
        manager = SettingsManagerCore(temp_settings_file)
        success_callback = Mock()
        error_callback = Mock()

        # Test valid directory
        manager.set_setting("output_directory", "/valid/path", success_callback, error_callback)
        success_callback.assert_called_once_with("output_directory", "/valid/path")
        error_callback.assert_not_called()

        # Reset mocks
        success_callback.reset_mock()
        error_callback.reset_mock()

        # Test invalid directory (empty string)
        manager.set_setting("output_directory", "", success_callback, error_callback)
        success_callback.assert_not_called()
        error_callback.assert_called_once()

        # Reset mocks
        success_callback.reset_mock()
        error_callback.reset_mock()

        # Test invalid directory (non-string)
        manager.set_setting("output_directory", 123, success_callback, error_callback)
        success_callback.assert_not_called()
        error_callback.assert_called_once()

    def test_set_setting_validation_output_filename_pattern(self, temp_settings_file):
        """Test set_setting validation for output_filename_pattern."""
        manager = SettingsManagerCore(temp_settings_file)
        success_callback = Mock()
        error_callback = Mock()

        # Test valid patterns
        for pattern in [0, 1, 2]:
            success_callback.reset_mock()
            error_callback.reset_mock()

            manager.set_setting(
                "output_filename_pattern", pattern, success_callback, error_callback
            )
            success_callback.assert_called_once_with("output_filename_pattern", pattern)
            error_callback.assert_not_called()
            assert manager.get_setting("output_filename_pattern") == pattern

        # Test invalid pattern
        success_callback.reset_mock()
        error_callback.reset_mock()

        manager.set_setting("output_filename_pattern", 5, success_callback, error_callback)
        success_callback.assert_not_called()
        error_callback.assert_called_once()
        # Should retain last valid value
        assert manager.get_setting("output_filename_pattern") == 2

    def test_get_all_settings(self, temp_settings_file, default_settings):
        """Test get_all_settings returns copy of all settings."""
        manager = SettingsManagerCore(temp_settings_file, default_settings)

        all_settings = manager.get_all_settings()

        # Should contain all default settings
        assert all_settings["output_bitrate"] == 128
        assert all_settings["output_directory"] == "/tmp/output"
        assert all_settings["output_filename_pattern"] == 0
        assert all_settings["some_other_setting"] == "default_value"

        # Should be a copy (modifying returned dict shouldn't affect internal state)
        all_settings["output_bitrate"] = 999
        assert manager.get_setting("output_bitrate") == 128

    def test_thread_safety(self, temp_settings_file):
        """Test thread safety of settings operations."""
        manager = SettingsManagerCore(temp_settings_file)
        results = []
        errors = []

        def worker(thread_id):
            try:
                success_callback = Mock()
                error_callback = Mock()

                # Each thread sets a unique setting
                setting_name = f"thread_{thread_id}_setting"
                setting_value = f"value_{thread_id}"

                manager.set_setting(setting_name, setting_value, success_callback, error_callback)

                # Verify the setting was set
                retrieved_value = manager.get_setting(setting_name)
                results.append((thread_id, setting_name, setting_value, retrieved_value))

            except Exception as e:
                errors.append((thread_id, str(e)))

        # Create and start multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=5.0)

        # Verify no errors occurred
        assert len(errors) == 0, f"Thread errors: {errors}"

        # Verify all settings were set correctly
        assert len(results) == 10
        for thread_id, setting_name, expected_value, actual_value in results:
            assert actual_value == expected_value, f"Thread {thread_id} setting mismatch"

    @patch("src.abb.services.core.settings_manager_core.logger")
    def test_save_settings_handles_io_error(self, mock_logger, temp_settings_file):
        """Test _save_settings handles I/O errors gracefully."""
        manager = SettingsManagerCore(temp_settings_file)

        # Mock open to raise IOError during save
        with patch("builtins.open", mock_open()) as mock_file:
            mock_file.side_effect = IOError("Disk full")

            success_callback = Mock()
            error_callback = Mock()

            manager.set_setting("test_key", "test_value", success_callback, error_callback)

            # Should log error
            mock_logger.error.assert_called_with(
                f"Error writing settings to file {temp_settings_file}: Disk full"
            )

    def test_set_setting_exception_handling(self, temp_settings_file):
        """Test set_setting handles unexpected exceptions."""
        manager = SettingsManagerCore(temp_settings_file)
        success_callback = Mock()
        error_callback = Mock()

        # Mock _validate_setting to raise an exception
        with patch.object(manager, "_validate_setting", side_effect=Exception("Validation error")):
            manager.set_setting("test_key", "test_value", success_callback, error_callback)

            # Should call error callback
            error_callback.assert_called_once()
            success_callback.assert_not_called()

            # Error message should contain the exception details
            error_message = error_callback.call_args[0][0]
            assert "Error setting 'test_key' to 'test_value'" in error_message
            assert "Validation error" in error_message

    @patch("src.abb.services.core.settings_manager_core.logger")
    def test_initialization_handles_file_not_found_during_read(
        self, mock_logger, temp_settings_file, default_settings
    ):
        """Test initialization handles FileNotFoundError during file read (race condition)."""
        # Create file to pass os.path.exists check
        with open(temp_settings_file, "w") as f:
            json.dump({"test": "value"}, f)

        # Mock open to raise FileNotFoundError (simulating file deletion between exists check and open)
        with patch("builtins.open", side_effect=FileNotFoundError("File was deleted")):
            manager = SettingsManagerCore(temp_settings_file, default_settings)

        # Should fall back to defaults
        assert manager.get_setting("output_bitrate") == 128

        # Should log warning about file not found
        mock_logger.warning.assert_called_with(
            "Settings file not found: %s. Using default settings.", temp_settings_file
        )
