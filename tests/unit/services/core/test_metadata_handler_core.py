"""Comprehensive tests for MetadataHandlerCore.

This module tests the pure Python MetadataHandlerCore implementation,
focusing on critical path behaviors and achieving >95% coverage.

Critical behaviors tested:
- Metadata extraction from audio files
- Field updates and state persistence
- FFmpeg metadata format conversion
- Cover art loading and validation
- Thread safety for concurrent operations
- Error handling and categorization
"""

import threading
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional
from unittest.mock import patch

import pytest

from src.abb.services.core.metadata_handler_core import MetadataHandlerCore


class CallbackCapture:
    """Utility for capturing and verifying callback invocations."""

    def __init__(self):
        """Initialize the callback capture."""
        self.success_calls: List[Any] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()

    def success_callback(self, *args, **kwargs):
        """Capture successful callback invocations."""
        with self._lock:
            self.success_calls.append((args, kwargs))
            self.call_count += 1

    def error_callback(self, error_msg: str):
        """Capture error callback invocations."""
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1

    def reset(self):
        """Reset all captured calls."""
        with self._lock:
            self.success_calls.clear()
            self.error_calls.clear()
            self.call_count = 0

    @property
    def has_success_calls(self) -> bool:
        """Check if any success callbacks were called."""
        return len(self.success_calls) > 0

    @property
    def has_error_calls(self) -> bool:
        """Check if any error callbacks were called."""
        return len(self.error_calls) > 0

    @property
    def last_success_args(self) -> Optional[tuple]:
        """Get the arguments from the last success callback."""
        return self.success_calls[-1][0] if self.success_calls else None

    @property
    def last_error_message(self) -> Optional[str]:
        """Get the last error message."""
        return self.error_calls[-1] if self.error_calls else None


class ThreadSafetyTester:
    """Utilities for testing thread safety of core services."""

    @staticmethod
    def run_concurrent_operations(operations: List[Callable], num_threads: int = 5) -> List[Any]:
        """Run multiple operations concurrently and collect results."""
        results = []
        exceptions = []
        threads = []

        def worker(operation, result_list, exception_list):
            try:
                result = operation()
                result_list.append(result)
            except Exception as e:
                exception_list.append(e)

        # Start threads
        for operation in operations:
            thread = threading.Thread(target=worker, args=(operation, results, exceptions))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        if exceptions:
            raise exceptions[0]  # Re-raise first exception

        return results


class TempCoverArtFiles:
    """Create temporary cover art files for testing."""

    def __init__(self, tmp_path: Path):
        """Initialize with a temporary directory path."""
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []

    def create_jpg_file(self, name: str = "cover.jpg", size_bytes: int = 1024) -> Path:
        """Create a temporary JPG file with minimal header."""
        file_path = self.tmp_path / name
        # Minimal JPEG header
        jpeg_header = b"\xff\xd8\xff\xe0\x00\x10JFIF"
        with open(file_path, "wb") as f:
            f.write(jpeg_header + b"\x00" * (size_bytes - len(jpeg_header)))
        self.created_files.append(file_path)
        return file_path

    def create_png_file(self, name: str = "cover.png", size_bytes: int = 1024) -> Path:
        """Create a temporary PNG file with minimal header."""
        file_path = self.tmp_path / name
        # Minimal PNG header
        png_header = b"\x89PNG\r\n\x1a\n"
        with open(file_path, "wb") as f:
            f.write(png_header + b"\x00" * (size_bytes - len(png_header)))
        self.created_files.append(file_path)
        return file_path

    def create_invalid_image(self, name: str = "invalid.jpg", size_bytes: int = 100) -> Path:
        """Create a temporary invalid image file."""
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"invalid_image_data" + b"\x00" * (size_bytes - 18))
        self.created_files.append(file_path)
        return file_path


def create_test_metadata() -> Dict[str, Any]:
    """Create sample metadata for testing."""
    return {
        "title": "Test Audiobook",
        "artist": "Test Author",
        "narrator": "Test Narrator",
        "genre": "Fiction",
        "year": "2023",
        "description": "A test audiobook for unit testing",
        "publisher": "Test Publisher",
        "series": "Test Series",
        "series_pos": "1",
        "album": "Test Album",
    }


class TestMetadataHandlerCore:
    """Test suite for MetadataHandlerCore critical path behaviors."""

    @pytest.fixture
    def handler(self):
        """Create a fresh MetadataHandlerCore instance for each test."""
        return MetadataHandlerCore()

    @pytest.fixture
    def callback_capture(self):
        """Create a callback capture utility for each test."""
        return CallbackCapture()

    @pytest.fixture
    def temp_cover_files(self, tmp_path):
        """Create temporary cover art files for testing."""
        return TempCoverArtFiles(tmp_path)

    def test_initialization(self, handler):
        """Test MetadataHandlerCore initializes with empty state."""
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
        assert not handler.has_metadata()

    def test_update_field_modifies_state_correctly(self, handler, callback_capture):
        """Test metadata field updates persist correctly."""
        # Update a field
        handler.update_field(
            "title", "New Title", callback_capture.success_callback, callback_capture.error_callback
        )

        # Verify callback was called
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls

        # Verify state was updated
        metadata = handler.get_current_metadata()
        assert metadata["title"] == "New Title"
        assert handler.has_metadata()

        # Verify callback received updated metadata
        callback_metadata = callback_capture.last_success_args[0]
        assert callback_metadata["title"] == "New Title"

    def test_get_for_ffmpeg_format_conversion(self, handler, callback_capture):
        """Test FFmpeg metadata format mapping accuracy."""
        # Set up test metadata with various fields
        test_metadata = create_test_metadata()

        # Update multiple fields
        for field, value in test_metadata.items():
            handler.update_field(field, value, callback_capture.success_callback)

        # Get FFmpeg format
        ffmpeg_metadata = handler.get_for_ffmpeg()

        # Verify key mappings from ABB_TO_FFMPEG_METADATA_MAP_GENERAL
        assert ffmpeg_metadata["title"] == "Test Audiobook"
        assert ffmpeg_metadata["date"] == "2023"  # year -> date
        assert ffmpeg_metadata["genre"] == "Fiction"
        assert ffmpeg_metadata["composer"] == "Test Narrator"  # narrator -> composer
        assert ffmpeg_metadata["mood"] == "Test Series"  # series -> mood
        assert ffmpeg_metadata["track"] == "1"  # series_pos -> track
        assert ffmpeg_metadata["description"] == "A test audiobook for unit testing"

        # Verify special artist handling
        assert ffmpeg_metadata["artist"] == "Test Author"
        assert ffmpeg_metadata["album_artist"] == "Test Author"  # Auto-set from artist

    def test_clear_metadata_resets_state(self, handler, callback_capture):
        """Test clearing metadata resets all state."""
        # Set up some metadata first
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        assert handler.has_metadata()

        # Clear metadata
        callback_capture.reset()
        handler.clear_metadata(callback_capture.success_callback, callback_capture.error_callback)

        # Verify callback was called
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls

        # Verify state was cleared
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
        assert not handler.has_metadata()

    def test_set_cover_art_validates_and_stores(self, handler, callback_capture, temp_cover_files):
        """Test cover art loading, validation, and storage."""
        # Create a valid cover art file
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")

        # Set cover art
        handler.set_cover_art(
            str(cover_file), callback_capture.success_callback, callback_capture.error_callback
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify callback was called with correct path
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.last_success_args[0] == str(cover_file)

        # Verify state was updated
        assert handler.get_cover_art_path() == str(cover_file)
        assert handler.get_cover_art_data() is not None
        assert len(handler.get_cover_art_data()) > 0

    def test_set_cover_art_clears_when_empty_path(
        self, handler, callback_capture, temp_cover_files
    ):
        """Test cover art clearing with empty path."""
        # First set a cover art
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        handler.set_cover_art(str(cover_file), callback_capture.success_callback)
        time.sleep(0.1)

        # Clear cover art with empty path
        callback_capture.reset()
        handler.set_cover_art(
            "", callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)

        # Verify callback was called with None
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.last_success_args[0] is None

        # Verify state was cleared
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None

    def test_set_cover_art_validates_file_format(self, handler, callback_capture, temp_cover_files):
        """Test cover art format validation."""
        # Create an invalid format file
        invalid_file = temp_cover_files.tmp_path / "invalid.txt"
        with open(invalid_file, "w") as f:
            f.write("This is not an image")

        # Try to set invalid cover art
        handler.set_cover_art(
            str(invalid_file), callback_capture.success_callback, callback_capture.error_callback
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify error was reported
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Unsupported cover art format" in callback_capture.last_error_message

        # Verify state was not changed
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None

    def test_set_cover_art_handles_missing_file(self, handler, callback_capture):
        """Test cover art handling of non-existent files."""
        # Try to set non-existent cover art
        handler.set_cover_art(
            "/non/existent/file.jpg",
            callback_capture.success_callback,
            callback_capture.error_callback,
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify error was reported
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Cover art file not found" in callback_capture.last_error_message

    @patch("src.abb.services.core.metadata_handler_core.extract_metadata")
    @patch("src.abb.services.core.metadata_handler_core.apply_metadata_defaults")
    def test_load_from_file_extracts_all_metadata(
        self, mock_apply_defaults, mock_extract, handler, callback_capture, tmp_path
    ):
        """Test all metadata fields extracted from audio files."""
        # Create a temporary audio file
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")

        # Mock the metadata extraction
        mock_metadata = create_test_metadata()
        mock_extract.return_value = mock_metadata
        mock_apply_defaults.return_value = mock_metadata

        # Load metadata from file
        handler.load_from_file(
            str(audio_file), callback_capture.success_callback, callback_capture.error_callback
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify callback was called with metadata
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls

        # Verify metadata was loaded
        loaded_metadata = callback_capture.last_success_args[0]
        assert loaded_metadata["title"] == "Test Audiobook"
        assert loaded_metadata["artist"] == "Test Author"

        # Verify internal state was updated
        assert handler.has_metadata()
        assert handler.get_current_file_path() == str(audio_file)
        assert handler.get_current_metadata() == mock_metadata

    def test_load_from_file_handles_missing_file(self, handler, callback_capture):
        """Test load_from_file handling of non-existent files."""
        # Try to load from non-existent file
        handler.load_from_file(
            "/non/existent/file.mp3",
            callback_capture.success_callback,
            callback_capture.error_callback,
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify error was reported
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "File not found" in callback_capture.last_error_message

    @patch("src.abb.services.core.metadata_handler_core.extract_metadata")
    def test_load_from_file_handles_extraction_error(
        self, mock_extract, handler, callback_capture, tmp_path
    ):
        """Test load_from_file handling of metadata extraction errors."""
        # Create a temporary file
        audio_file = tmp_path / "corrupt.mp3"
        audio_file.write_bytes(b"corrupt_data")

        # Mock extraction to raise an exception
        mock_extract.side_effect = Exception("Extraction failed")

        # Try to load metadata
        handler.load_from_file(
            str(audio_file), callback_capture.success_callback, callback_capture.error_callback
        )

        # Wait for threaded operation to complete
        time.sleep(0.1)

        # Verify error was reported
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Failed to load metadata" in callback_capture.last_error_message

    def test_get_field_returns_correct_values(self, handler, callback_capture):
        """Test get_field method returns correct values."""
        # Set up some metadata
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        handler.update_field("artist", "Test Artist", callback_capture.success_callback)

        # Test getting existing fields
        assert handler.get_field("title") == "Test Title"
        assert handler.get_field("artist") == "Test Artist"

        # Test getting non-existent field with default
        assert handler.get_field("non_existent") is None
        assert handler.get_field("non_existent", "default_value") == "default_value"

    def test_reset_state_clears_everything(self, handler, callback_capture, temp_cover_files):
        """Test reset_state clears all internal state."""
        # Set up some state
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        handler.set_cover_art(str(cover_file), callback_capture.success_callback)
        time.sleep(0.1)

        # Verify state is set
        assert handler.has_metadata()
        assert handler.get_cover_art_path() is not None

        # Reset state
        handler.reset_state()

        # Verify everything is cleared
        assert not handler.has_metadata()
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None

    def test_thread_safety_concurrent_operations(self, handler, callback_capture):
        """Test concurrent metadata operations are thread-safe."""

        # Define concurrent operations
        def update_title():
            capture = CallbackCapture()
            handler.update_field(
                "title", "Concurrent Title", capture.success_callback, capture.error_callback
            )
            return capture

        def update_artist():
            capture = CallbackCapture()
            handler.update_field(
                "artist", "Concurrent Artist", capture.success_callback, capture.error_callback
            )
            return capture

        def get_metadata():
            return handler.get_current_metadata()

        def check_has_metadata():
            return handler.has_metadata()

        # Run operations concurrently
        operations = [update_title, update_artist, get_metadata, check_has_metadata] * 3
        results = ThreadSafetyTester.run_concurrent_operations(operations)

        # Verify no exceptions occurred and service state is consistent
        assert len(results) == 12
        final_metadata = handler.get_current_metadata()
        assert "title" in final_metadata or "artist" in final_metadata  # At least one should be set

    def test_ffmpeg_metadata_special_mappings(self, handler, callback_capture):
        """Test special FFmpeg metadata mappings and priority handling."""
        # Test comment/description priority
        handler.update_field("comment", "Test Comment", callback_capture.success_callback)
        handler.update_field("description", "Test Description", callback_capture.success_callback)

        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["comment"] == "Test Comment"  # comment takes priority

        # Clear comment and test description fallback
        handler.update_field("comment", "", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["comment"] == "Test Description"  # description as fallback

        # Test series_sort priority for album_sort
        handler.update_field("series_sort", "Series Sort Value", callback_capture.success_callback)
        handler.update_field("sort_album", "Album Sort Value", callback_capture.success_callback)

        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["album_sort"] == "Series Sort Value"  # series_sort takes priority

    def test_update_field_error_handling(self, handler, callback_capture):
        """Test update_field error handling."""
        # Mock an exception during field update
        with patch.object(handler, "_lock") as mock_lock:
            mock_lock.__enter__.side_effect = RuntimeError("Lock error")

            handler.update_field(
                "title",
                "Test Title",
                callback_capture.success_callback,
                callback_capture.error_callback,
            )

            # Verify error was reported
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to update field 'title'" in callback_capture.last_error_message

    def test_clear_metadata_error_handling(self, handler, callback_capture):
        """Test clear_metadata error handling."""
        # Mock an exception during clear operation
        with patch(
            "src.abb.services.core.metadata_handler_core.clear_metadata_cache"
        ) as mock_clear_cache:
            mock_clear_cache.side_effect = RuntimeError("Cache clear error")

            handler.clear_metadata(
                callback_capture.success_callback, callback_capture.error_callback
            )

            # Verify error was reported
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to clear metadata" in callback_capture.last_error_message

    def test_set_cover_art_read_error_handling(self, handler, callback_capture, temp_cover_files):
        """Test set_cover_art handling of file read errors."""
        # Create a valid cover art file
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")

        # Mock file read to raise an exception
        with patch("builtins.open", side_effect=IOError("Read error")):
            handler.set_cover_art(
                str(cover_file), callback_capture.success_callback, callback_capture.error_callback
            )

            # Wait for threaded operation to complete
            time.sleep(0.1)

            # Verify error was reported
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to read cover art file" in callback_capture.last_error_message

    def test_get_metadata_mapping_class_method(self):
        """Test get_metadata_mapping class method."""
        mapping = MetadataHandlerCore.get_metadata_mapping()

        # Verify it returns the expected mapping
        assert mapping["title"] == "title"
        assert mapping["year"] == "date"
        assert mapping["narrator"] == "composer"
        assert mapping["series"] == "mood"

        # Verify it's a copy (modifying it doesn't affect the original)
        mapping["test"] = "test_value"
        original_mapping = MetadataHandlerCore.get_metadata_mapping()
        assert "test" not in original_mapping

    def test_ffmpeg_metadata_empty_values_filtered(self, handler, callback_capture):
        """Test that empty/None values are filtered from FFmpeg metadata."""
        # Set some fields with empty values
        handler.update_field("title", "", callback_capture.success_callback)
        handler.update_field("artist", None, callback_capture.success_callback)
        handler.update_field("genre", "Fiction", callback_capture.success_callback)
        handler.update_field("year", "   ", callback_capture.success_callback)  # Whitespace only

        ffmpeg_metadata = handler.get_for_ffmpeg()

        # Verify empty values are filtered out
        assert "title" not in ffmpeg_metadata  # Empty string
        assert "artist" not in ffmpeg_metadata  # None value
        assert "date" not in ffmpeg_metadata  # Whitespace only
        assert ffmpeg_metadata["genre"] == "Fiction"  # Valid value included

    def test_ffmpeg_metadata_sort_album_fallback(self, handler, callback_capture):
        """Test sort_album fallback when series_sort is not set."""
        # Set only sort_album (no series_sort)
        handler.update_field("sort_album", "Album Sort Value", callback_capture.success_callback)

        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["album_sort"] == "Album Sort Value"  # sort_album as fallback

    def test_set_cover_art_general_exception_handling(
        self, handler, callback_capture, temp_cover_files
    ):
        """Test set_cover_art general exception handling."""
        # Create a valid cover art file
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")

        # Mock Path to raise an exception
        with patch("src.abb.services.core.metadata_handler_core.Path") as mock_path:
            mock_path.side_effect = RuntimeError("Path error")

            handler.set_cover_art(
                str(cover_file), callback_capture.success_callback, callback_capture.error_callback
            )

            # Wait for threaded operation to complete
            time.sleep(0.1)

            # Verify error was reported
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to set cover art" in callback_capture.last_error_message

    @patch("src.abb.services.core.metadata_handler_core.extract_cover")
    def test_load_from_file_with_cover_art_extraction(
        self, mock_extract_cover, handler, callback_capture, tmp_path
    ):
        """Test load_from_file with successful cover art extraction."""
        # Create a temporary audio file
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")

        # Mock successful cover art extraction
        mock_cover_data = b"fake_cover_data"
        mock_extract_cover.return_value = mock_cover_data

        # Mock the metadata extraction
        with patch("src.abb.services.core.metadata_handler_core.extract_metadata") as mock_extract:
            with patch(
                "src.abb.services.core.metadata_handler_core.apply_metadata_defaults"
            ) as mock_apply_defaults:
                mock_metadata = create_test_metadata()
                mock_extract.return_value = mock_metadata
                mock_apply_defaults.return_value = mock_metadata

                # Load metadata from file
                handler.load_from_file(
                    str(audio_file),
                    callback_capture.success_callback,
                    callback_capture.error_callback,
                )

                # Wait for threaded operation to complete
                time.sleep(0.1)

                # Verify cover art was extracted and stored
                assert handler.get_cover_art_data() == mock_cover_data
                assert handler.get_cover_art_path() == str(audio_file)

    @patch("src.abb.services.core.metadata_handler_core.extract_cover")
    def test_load_from_file_cover_art_extraction_failure(
        self, mock_extract_cover, handler, callback_capture, tmp_path
    ):
        """Test load_from_file handling cover art extraction failure gracefully."""
        # Create a temporary audio file
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")

        # Mock cover art extraction to raise an exception
        mock_extract_cover.side_effect = Exception("Cover extraction failed")

        # Mock the metadata extraction
        with patch("src.abb.services.core.metadata_handler_core.extract_metadata") as mock_extract:
            with patch(
                "src.abb.services.core.metadata_handler_core.apply_metadata_defaults"
            ) as mock_apply_defaults:
                mock_metadata = create_test_metadata()
                mock_extract.return_value = mock_metadata
                mock_apply_defaults.return_value = mock_metadata

                # Load metadata from file
                handler.load_from_file(
                    str(audio_file),
                    callback_capture.success_callback,
                    callback_capture.error_callback,
                )

                # Wait for threaded operation to complete
                time.sleep(0.1)

                # Verify metadata loading succeeded despite cover art failure
                assert callback_capture.has_success_calls
                assert not callback_capture.has_error_calls

                # Verify cover art is None due to extraction failure
                assert handler.get_cover_art_data() is None
                assert handler.get_cover_art_path() is None

    def test_factory_function(self):
        """Test the create_metadata_handler_core factory function."""
        from src.abb.services.core.metadata_handler_core import create_metadata_handler_core

        handler = create_metadata_handler_core()
        assert isinstance(handler, MetadataHandlerCore)
        assert handler.get_current_metadata() == {}

    def test_feature_flag_function(self):
        """Test the is_core_metadata_enabled feature flag function."""
        # Test default (should be False)
        import os

        from src.abb.services.core.metadata_handler_core import is_core_metadata_enabled

        with patch.dict(os.environ, {}, clear=True):
            assert not is_core_metadata_enabled()

        # Test enabled
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            assert is_core_metadata_enabled()

        # Test case insensitive
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "TRUE"}):
            assert is_core_metadata_enabled()

        # Test disabled
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            assert not is_core_metadata_enabled()
