"""Comprehensive tests for ProcessingServiceCore.

This module tests the pure Python ProcessingServiceCore implementation,
focusing on FFmpeg integration, threading, progress monitoring, and cleanup.

Critical behaviors tested:
- FFmpeg command generation and execution
- Progress monitoring and callback patterns
- Cancellation and cleanup logic
- Error handling and categorization
- Thread safety for concurrent operations
- Preview generation workflow
"""

import os
import threading
import time
from pathlib import Path
from typing import Callable, List, Optional
from unittest.mock import Mock, patch

import pytest

from src.abb.services.core.path_service_core import PathServiceCore
from src.abb.services.core.processing_service_core import ProcessingServiceCore


class MockFFmpegProcess:
    """Mock subprocess.Popen that simulates FFmpeg behavior."""

    def __init__(self, returncode: int = 0, stderr_output: str = "", stdout_output: str = ""):
        """Initialize mock FFmpeg process.

        Args:
            returncode: Process exit code (0 for success)
            stderr_output: Simulated stderr output for progress monitoring
            stdout_output: Simulated stdout output
        """
        self.returncode = returncode
        self.stderr_output = stderr_output
        self.stdout_output = stdout_output
        self.terminated = False
        self.killed = False
        self._stderr_lines = stderr_output.split("\n") if stderr_output else []
        self._stderr_index = 0

        # Mock file objects
        self.stderr = Mock()
        self.stdout = Mock()

        # Configure stderr readline to return lines sequentially
        self.stderr.readline.side_effect = self._readline_stderr

    def _readline_stderr(self):
        """Simulate reading lines from stderr."""
        if self._stderr_index < len(self._stderr_lines):
            line = self._stderr_lines[self._stderr_index] + "\n"
            self._stderr_index += 1
            return line
        return ""  # EOF

    def poll(self):
        """Check if process has terminated."""
        return self.returncode if self._stderr_index >= len(self._stderr_lines) else None

    def communicate(self):
        """Wait for process completion and return output."""
        return self.stdout_output, self.stderr_output

    def terminate(self):
        """Terminate the process."""
        self.terminated = True
        self.returncode = -15  # SIGTERM

    def kill(self):
        """Kill the process."""
        self.killed = True
        self.returncode = -9  # SIGKILL

    def wait(self, timeout=None):
        """Wait for process completion."""
        if timeout:
            time.sleep(min(0.1, timeout))  # Simulate brief wait
        return self.returncode


class FFmpegOutputSimulator:
    """Generates realistic FFmpeg stderr output for progress testing."""

    @staticmethod
    def create_progress_output(duration_seconds: float, progress_points: int = 10) -> str:
        """Create realistic FFmpeg progress output.

        Args:
            duration_seconds: Total duration of processing
            progress_points: Number of progress updates to generate

        Returns:
            Simulated FFmpeg stderr output with progress information
        """
        lines = []

        # Initial FFmpeg banner
        lines.append("ffmpeg version 4.4.0 Copyright (c) 2000-2021 the FFmpeg developers")
        lines.append("built with gcc 9 (Ubuntu 9.3.0-17ubuntu1~20.04)")

        # Progress updates
        for i in range(progress_points):
            progress_time = (
                (duration_seconds * i) / (progress_points - 1) if progress_points > 1 else 0
            )
            hours = int(progress_time // 3600)
            minutes = int((progress_time % 3600) // 60)
            seconds = progress_time % 60

            lines.append(
                f"frame= {i * 100:4d} fps= 0.0 q=-1.0 size= {i * 1024:7d}kB "
                f"time={hours:02d}:{minutes:02d}:{seconds:06.3f} bitrate= 64.0kbits/s speed=1.0x"
            )

        # Final completion line
        lines.append(
            "video:0kB audio:12345kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.123456%"
        )

        return "\n".join(lines)

    @staticmethod
    def create_error_output(error_message: str) -> str:
        """Create FFmpeg error output.

        Args:
            error_message: Error message to include

        Returns:
            Simulated FFmpeg error output
        """
        return f"""ffmpeg version 4.4.0 Copyright (c) 2000-2021 the FFmpeg developers
built with gcc 9 (Ubuntu 9.3.0-17ubuntu1~20.04)
{error_message}: No such file or directory"""


class ProcessingCallbackCapture:
    """Extended callback capture for processing-specific callbacks."""

    def __init__(self):
        """Initialize the processing callback capture."""
        self.progress_calls: List[int] = []
        self.status_calls: List[str] = []
        self.complete_calls: List[str] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()

    def progress_callback(self, progress: int):
        """Capture progress callback invocations."""
        with self._lock:
            self.progress_calls.append(progress)
            self.call_count += 1

    def status_callback(self, status: str):
        """Capture status callback invocations."""
        with self._lock:
            self.status_calls.append(status)
            self.call_count += 1

    def complete_callback(self, output_file: str):
        """Capture completion callback invocations."""
        with self._lock:
            self.complete_calls.append(output_file)
            self.call_count += 1

    def error_callback(self, error_msg: str):
        """Capture error callback invocations."""
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1

    def reset(self):
        """Reset all captured calls."""
        with self._lock:
            self.progress_calls.clear()
            self.status_calls.clear()
            self.complete_calls.clear()
            self.error_calls.clear()
            self.call_count = 0

    @property
    def has_progress_calls(self) -> bool:
        """Check if any progress callbacks were called."""
        return len(self.progress_calls) > 0

    @property
    def has_status_calls(self) -> bool:
        """Check if any status callbacks were called."""
        return len(self.status_calls) > 0

    @property
    def has_complete_calls(self) -> bool:
        """Check if any completion callbacks were called."""
        return len(self.complete_calls) > 0

    @property
    def has_error_calls(self) -> bool:
        """Check if any error callbacks were called."""
        return len(self.error_calls) > 0

    @property
    def last_progress(self) -> Optional[int]:
        """Get the last progress value."""
        return self.progress_calls[-1] if self.progress_calls else None

    @property
    def last_status(self) -> Optional[str]:
        """Get the last status message."""
        return self.status_calls[-1] if self.status_calls else None

    @property
    def last_complete_file(self) -> Optional[str]:
        """Get the last completion file path."""
        return self.complete_calls[-1] if self.complete_calls else None

    @property
    def last_error_message(self) -> Optional[str]:
        """Get the last error message."""
        return self.error_calls[-1] if self.error_calls else None


class ThreadingTestHelper:
    """Utilities for testing threading behavior safely."""

    @staticmethod
    def wait_for_condition(
        condition_func: Callable[[], bool], timeout: float = 5.0, interval: float = 0.1
    ) -> bool:
        """Wait for a condition to become true.

        Args:
            condition_func: Function that returns True when condition is met
            timeout: Maximum time to wait in seconds
            interval: Check interval in seconds

        Returns:
            True if condition was met, False if timeout occurred
        """
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            time.sleep(interval)
        return False

    @staticmethod
    def wait_for_thread_completion(thread: threading.Thread, timeout: float = 5.0) -> bool:
        """Wait for a thread to complete.

        Args:
            thread: Thread to wait for
            timeout: Maximum time to wait in seconds

        Returns:
            True if thread completed, False if timeout occurred
        """
        thread.join(timeout)
        return not thread.is_alive()


class TempAudioFiles:
    """Create temporary audio files for testing."""

    def __init__(self, tmp_path: Path):
        """Initialize with a temporary directory path."""
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []

    def create_mp3_file(self, name: str = "test.mp3", size_bytes: int = 1024) -> Path:
        """Create a temporary MP3 file with specified size."""
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ID3" + b"\x00" * (size_bytes - 3))  # Minimal MP3 header
        self.created_files.append(file_path)
        return file_path

    def create_multiple_files(self, count: int = 3, prefix: str = "test") -> List[Path]:
        """Create multiple test audio files."""
        files = []
        for i in range(count):
            file_path = self.create_mp3_file(f"{prefix}_{i}.mp3", 1024 * (i + 1))
            files.append(file_path)
        return files


class TestProcessingServiceCore:
    """Test suite for ProcessingServiceCore critical path behaviors."""

    @pytest.fixture
    def path_service(self):
        """Create a PathServiceCore instance for dependency injection."""
        return PathServiceCore()

    @pytest.fixture
    def service(self, path_service):
        """Create a fresh ProcessingServiceCore instance for each test."""
        return ProcessingServiceCore(path_service)

    @pytest.fixture
    def callback_capture(self):
        """Create a processing callback capture utility for each test."""
        return ProcessingCallbackCapture()

    @pytest.fixture
    def temp_audio_files(self, tmp_path):
        """Create temporary audio files for testing."""
        return TempAudioFiles(tmp_path)

    def test_initialization_with_valid_path_service(self, path_service):
        """Test ProcessingServiceCore initializes correctly with dependencies."""
        service = ProcessingServiceCore(path_service)

        assert service._path_service is path_service
        assert service._validator is not None
        assert service._lock is not None
        assert service._is_processing is False
        assert service._cancel_event is not None
        assert service._process is None
        assert service._processing_thread is None
        assert service._progress_thread is None
        assert service._temp_files == []
        assert service._output_file is None
        assert service._total_duration_seconds == 0.0
        assert service._ffmpeg_path is not None  # Should detect FFmpeg path

    def test_is_processing_property(self, service):
        """Test is_processing property reflects internal state."""
        assert service.is_processing() is False

        # Simulate processing state
        with service._lock:
            service._is_processing = True

        assert service.is_processing() is True

    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_path_detection(self, mock_get_path, path_service):
        """Test FFmpeg executable path detection."""
        mock_get_path.return_value = "/usr/bin/ffmpeg"

        service = ProcessingServiceCore(path_service)
        assert service._ffmpeg_path == "/usr/bin/ffmpeg"

        mock_get_path.assert_called_once_with("ffmpeg")

    def test_process_prevents_concurrent_operations(
        self, service, callback_capture, temp_audio_files
    ):
        """Test that process() prevents concurrent processing operations."""
        # Simulate processing in progress
        with service._lock:
            service._is_processing = True

        # Try to start another process
        files = [str(temp_audio_files.create_mp3_file())]
        service.process(
            input_files=files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Should immediately call error callback
        assert callback_capture.has_error_calls
        assert "Processing already in progress" in callback_capture.last_error_message

    def test_process_preview_prevents_concurrent_operations(
        self, service, callback_capture, temp_audio_files
    ):
        """Test that process_preview() prevents concurrent operations."""
        # Simulate processing in progress
        with service._lock:
            service._is_processing = True

        # Try to start preview
        input_file = str(temp_audio_files.create_mp3_file())
        service.process_preview(
            input_file=input_file,
            metadata={},
            settings={},
            duration_seconds=30,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Should immediately call error callback
        assert callback_capture.has_error_calls
        assert "Processing already in progress" in callback_capture.last_error_message

    def test_cancel_when_not_processing(self, service):
        """Test cancel() when no processing is active."""
        success_called = threading.Event()

        def success_callback():
            success_called.set()

        service.cancel(success_callback)

        # Should immediately call success callback
        assert success_called.wait(timeout=1.0)

    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    @patch("src.abb.services.core.processing_service_core.get_duration")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    @patch("os.makedirs")
    def test_process_successful_completion(
        self,
        mock_makedirs,
        mock_get_path,
        mock_get_duration,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
        tmp_path,
    ):
        """Test successful processing workflow."""
        # Setup mocks
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_get_duration.return_value = 120.0  # 2 minutes
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "output.m4b"]

        # Create mock FFmpeg process
        progress_output = FFmpegOutputSimulator.create_progress_output(120.0, 5)
        mock_process = MockFFmpegProcess(returncode=0, stderr_output=progress_output)
        mock_popen.return_value = mock_process

        # Create test files
        input_files = [str(f) for f in temp_audio_files.create_multiple_files(2)]
        output_path = str(tmp_path / "output")
        output_filename = "test_audiobook.m4b"

        # Start processing
        service.process(
            input_files=input_files,
            output_path=output_path,
            output_filename=output_filename,
            metadata={"title": "Test Book", "author": "Test Author"},
            settings={"bitrate": 64, "format": "m4b"},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Wait for processing to complete
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_complete_calls or callback_capture.has_error_calls,
            timeout=5.0,
        )

        # Verify successful completion
        assert callback_capture.has_complete_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.has_progress_calls
        assert callback_capture.has_status_calls

        # Verify final progress is 100%
        assert callback_capture.last_progress == 100

        # Verify output file path
        expected_output = os.path.join(output_path, output_filename)
        assert callback_capture.last_complete_file == expected_output

        # Verify mocks were called correctly
        mock_makedirs.assert_called_once_with(output_path, exist_ok=True)
        mock_build_command.assert_called_once()
        mock_popen.assert_called_once()

    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_preview_command")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_process_preview_successful_completion(
        self,
        mock_get_path,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
    ):
        """Test successful preview generation workflow."""
        # Setup mocks
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "-t", "30", "preview.m4b"]

        # Create mock FFmpeg process
        progress_output = FFmpegOutputSimulator.create_progress_output(30.0, 3)
        mock_process = MockFFmpegProcess(returncode=0, stderr_output=progress_output)
        mock_popen.return_value = mock_process

        # Create test file
        input_file = str(temp_audio_files.create_mp3_file())

        # Start preview processing
        service.process_preview(
            input_file=input_file,
            metadata={"title": "Test Book"},
            settings={"bitrate": 64},
            duration_seconds=30,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Wait for processing to complete
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_complete_calls or callback_capture.has_error_calls,
            timeout=5.0,
        )

        # Verify successful completion
        assert callback_capture.has_complete_calls
        assert not callback_capture.has_error_calls

        # Verify mocks were called correctly
        mock_build_command.assert_called_once()
        mock_popen.assert_called_once()

    def test_progress_parsing_from_ffmpeg_output(self, service):
        """Test progress parsing from FFmpeg stderr output."""
        # Test various FFmpeg progress line formats
        test_cases = [
            (
                "frame= 1000 fps= 0.0 q=-1.0 size= 1024kB time=00:01:00.000 bitrate= 64.0kbits/s speed=1.0x",
                50,
            ),
            (
                "frame= 2000 fps= 0.0 q=-1.0 size= 2048kB time=00:02:00.000 bitrate= 64.0kbits/s speed=1.0x",
                100,
            ),
            (
                "frame= 500 fps= 0.0 q=-1.0 size= 512kB time=00:00:30.000 bitrate= 64.0kbits/s speed=1.0x",
                25,
            ),
            ("Invalid line without time", None),
            ("", None),
        ]

        # Set total duration for percentage calculation
        service._total_duration_seconds = 120.0  # 2 minutes

        for line, expected_progress in test_cases:
            result = service._parse_ffmpeg_progress(line)
            if expected_progress is not None:
                assert result == expected_progress
            else:
                assert result is None

    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    @patch("src.abb.services.core.processing_service_core.get_duration")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_process_error_handling(
        self,
        mock_get_path,
        mock_get_duration,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
    ):
        """Test handling of FFmpeg process errors."""
        # Setup mocks
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_get_duration.return_value = 120.0
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "output.m4b"]

        # Create mock FFmpeg process that fails
        error_output = FFmpegOutputSimulator.create_error_output("Input file not found")
        mock_process = MockFFmpegProcess(returncode=1, stderr_output=error_output)
        mock_popen.return_value = mock_process

        # Create test files
        input_files = [str(temp_audio_files.create_mp3_file())]

        # Start processing
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Wait for processing to complete
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )

        # Verify error handling
        assert callback_capture.has_error_calls
        assert not callback_capture.has_complete_calls
        assert "FFmpeg failed" in callback_capture.last_error_message
        assert "exit code 1" in callback_capture.last_error_message

    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_not_found_error(
        self, mock_get_path, service, callback_capture, temp_audio_files, path_service
    ):
        """Test error handling when FFmpeg executable is not found."""
        # Mock FFmpeg not found
        mock_get_path.return_value = None

        # Create new service instance to trigger FFmpeg detection
        service = ProcessingServiceCore(path_service)

        # Create test files
        input_files = [str(temp_audio_files.create_mp3_file())]

        # Start processing
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Wait for error
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )

        # Verify error handling
        assert callback_capture.has_error_calls
        assert "FFmpeg executable not found" in callback_capture.last_error_message

    def test_cancel_terminates_process_gracefully(self, service):
        """Test that cancel() terminates FFmpeg process gracefully."""
        # Create a mock process
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.kill = Mock()
        mock_process.wait = Mock(return_value=0)

        # Set up service state to simulate active processing
        service._is_processing = True
        service._process = mock_process
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._processing_thread.is_alive = Mock(return_value=False)
        service._progress_thread.is_alive = Mock(return_value=False)

        # Test cancellation
        cancel_success = threading.Event()

        def cancel_callback():
            cancel_success.set()

        service.cancel(cancel_callback)

        # Wait for cancellation to complete
        assert cancel_success.wait(timeout=5.0)

        # Wait for cleanup to complete
        assert ThreadingTestHelper.wait_for_condition(
            lambda: not service.is_processing(), timeout=2.0
        )

        # Verify process was terminated gracefully
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called()
        assert not service.is_processing()

    def test_cancel_force_kills_unresponsive_process(self, service):
        """Test that cancel() force kills unresponsive FFmpeg process."""
        import subprocess

        # Create a mock process that doesn't respond to terminate
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.kill = Mock()

        # Mock wait to simulate timeout on terminate
        def mock_wait(timeout=None):
            if timeout and timeout <= 2.0:
                raise subprocess.TimeoutExpired("ffmpeg", timeout)
            return 0

        mock_process.wait = Mock(side_effect=mock_wait)

        # Set up service state to simulate active processing
        service._is_processing = True
        service._process = mock_process
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._processing_thread.is_alive = Mock(return_value=False)
        service._progress_thread.is_alive = Mock(return_value=False)

        # Test cancellation
        cancel_success = threading.Event()

        def cancel_callback():
            cancel_success.set()

        service.cancel(cancel_callback)

        # Wait for cancellation to complete
        assert cancel_success.wait(timeout=10.0)

        # Wait for cleanup to complete
        assert ThreadingTestHelper.wait_for_condition(
            lambda: not service.is_processing(), timeout=2.0
        )

        # Verify process was terminated and then killed
        mock_process.terminate.assert_called_once()
        mock_process.kill.assert_called_once()
        assert not service.is_processing()

    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_after_successful_processing(self, mock_remove, mock_exists, service, tmp_path):
        """Test cleanup behavior after successful processing."""
        mock_exists.return_value = True

        # Set up service state as if processing completed successfully
        temp_file1 = str(tmp_path / "temp1.tmp")
        temp_file2 = str(tmp_path / "temp2.tmp")
        output_file = str(tmp_path / "output.m4b")

        service._temp_files = [temp_file1, temp_file2, output_file]
        service._output_file = output_file

        # Call cleanup method
        service._cleanup_temp_files_only()

        # Verify only temp files were removed, not output file
        expected_calls = [((temp_file1,), {}), ((temp_file2,), {})]
        mock_remove.assert_has_calls(expected_calls, any_order=True)
        assert mock_remove.call_count == 2

        # Verify output file remains in temp files list
        assert service._temp_files == [output_file]

    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_after_cancellation(self, mock_remove, mock_exists, service, tmp_path):
        """Test cleanup behavior after cancellation."""
        mock_exists.return_value = True

        # Set up service state as if processing was cancelled
        temp_file1 = str(tmp_path / "temp1.tmp")
        temp_file2 = str(tmp_path / "temp2.tmp")
        output_file = str(tmp_path / "output.m4b")

        service._temp_files = [temp_file1, temp_file2, output_file]
        service._output_file = output_file

        # Call cleanup method
        service._cleanup_after_cancel()

        # Verify all files were removed including output file
        expected_calls = [((temp_file1,), {}), ((temp_file2,), {}), ((output_file,), {})]
        mock_remove.assert_has_calls(expected_calls, any_order=True)
        assert mock_remove.call_count == 4  # 3 temp files + 1 output file

        # Verify all state was cleared
        assert service._temp_files == []
        assert service._output_file is None

    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_handles_file_removal_errors(self, mock_remove, mock_exists, service, tmp_path):
        """Test cleanup gracefully handles file removal errors."""
        mock_exists.return_value = True
        mock_remove.side_effect = OSError("Permission denied")

        # Set up service state
        temp_file = str(tmp_path / "temp.tmp")
        service._temp_files = [temp_file]

        # Call cleanup - should not raise exception
        service._cleanup_temp_files_only()

        # Verify removal was attempted
        mock_remove.assert_called_once_with(temp_file)

    def test_thread_safety_state_management(self, service, temp_audio_files):
        """Test thread safety of processing state management."""

        def check_processing_state():
            return service.is_processing()

        def try_start_processing():
            files = [str(temp_audio_files.create_mp3_file())]
            capture = ProcessingCallbackCapture()

            service.process(
                input_files=files,
                output_path="/tmp",
                output_filename="test.m4b",
                metadata={},
                settings={},
                progress_callback=capture.progress_callback,
                status_callback=capture.status_callback,
                complete_callback=capture.complete_callback,
                error_callback=capture.error_callback,
            )
            return capture

        # Run multiple threads trying to start processing
        operations = [check_processing_state, try_start_processing] * 5
        results = []

        # Use ThreadingTestHelper to run concurrent operations
        def run_operations():
            for operation in operations:
                try:
                    result = operation()
                    results.append(result)
                except Exception as e:
                    results.append(e)

        threads = []
        for _ in range(3):
            thread = threading.Thread(target=run_operations)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            assert ThreadingTestHelper.wait_for_thread_completion(thread, timeout=5.0)

        # Verify no exceptions occurred and state is consistent
        assert len(results) > 0
        final_state = service.is_processing()
        assert isinstance(final_state, bool)

    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    def test_invalid_command_generation_error(
        self, mock_build_command, service, callback_capture, temp_audio_files
    ):
        """Test error handling when FFmpeg command generation fails."""
        # Mock command generation failure
        mock_build_command.return_value = None

        # Create test files
        input_files = [str(temp_audio_files.create_mp3_file())]

        # Start processing
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )

        # Wait for error
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )

        # Verify error handling
        assert callback_capture.has_error_calls
        assert "Failed to build FFmpeg command" in callback_capture.last_error_message

    def test_processing_state_cleanup_on_completion(self, service):
        """Test that processing state is properly cleaned up on completion."""
        # Simulate processing state
        service._is_processing = True
        service._process = Mock()
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._progress_callback = Mock()
        service._status_callback = Mock()
        service._complete_callback = Mock()
        service._error_callback = Mock()

        # Call cleanup
        service._cleanup_processing()

        # Verify all state was cleared
        assert service._is_processing is False
        assert service._process is None
        assert service._processing_thread is None
        assert service._progress_thread is None
        assert service._progress_callback is None
        assert service._status_callback is None
        assert service._complete_callback is None
        assert service._error_callback is None
