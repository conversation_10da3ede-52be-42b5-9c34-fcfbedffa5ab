"""Tests for PathServiceCore - Pure Python path and filename generation service.

This module tests the PathServiceCore implementation which provides:
- Output directory path calculation with subdirectory patterns
- Filename generation using different naming patterns
- Path and filename sanitization for cross-platform compatibility

Following TDD principles and established testing patterns from other core services.
"""

import pytest

from src.abb.services.core.path_service_core import PathServiceCore


class TestPathServiceCore:
    """Test suite for PathServiceCore functionality."""

    @pytest.fixture
    def service(self):
        """Create PathServiceCore instance for testing."""
        return PathServiceCore()

    def test_initialization(self, service):
        """Test PathServiceCore initializes correctly."""
        assert service is not None
        assert hasattr(service, "calculate_output_path")
        assert hasattr(service, "generate_output_filename")
        assert callable(service.calculate_output_path)
        assert callable(service.generate_output_filename)

    # ===== Output Path Calculation Tests =====

    def test_calculate_output_path_no_subdirectory_pattern(self, service):
        """Test output path calculation without subdirectory pattern."""
        base_dir = "/output"
        metadata = {"artist": "<PERSON> Doe", "series": "Mystery Series"}

        result = service.calculate_output_path(base_dir, metadata, False)

        assert result == "/output"

    def test_calculate_output_path_with_artist_only(self, service):
        """Test output path calculation with artist subdirectory."""
        base_dir = "/output"
        metadata = {"artist": "John Doe"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output/John Doe"

    def test_calculate_output_path_with_artist_and_series(self, service):
        """Test output path calculation with artist and series subdirectories."""
        base_dir = "/output"
        metadata = {"artist": "John Doe", "series": "Mystery Series"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output/John Doe/Mystery Series"

    def test_calculate_output_path_with_series_only(self, service):
        """Test output path calculation with series but no artist."""
        base_dir = "/output"
        metadata = {"series": "Mystery Series"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output/Mystery Series"

    def test_calculate_output_path_empty_metadata(self, service):
        """Test output path calculation with empty metadata."""
        base_dir = "/output"
        metadata = {}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output"

    def test_calculate_output_path_none_values(self, service):
        """Test output path calculation with None values in metadata."""
        base_dir = "/output"
        metadata = {"artist": None, "series": None}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output"

    def test_calculate_output_path_whitespace_values(self, service):
        """Test output path calculation with whitespace-only values."""
        base_dir = "/output"
        metadata = {"artist": "   ", "series": "\t\n"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output"

    # ===== Filename Generation Tests =====

    def test_generate_filename_pattern_0_title_with_year(self, service):
        """Test filename generation pattern 0: Title (Year)."""
        metadata = {"title": "The Great Book", "year": "2023"}

        result = service.generate_output_filename(metadata, 0)

        assert result == "The Great Book (2023).m4b"

    def test_generate_filename_pattern_0_title_without_year(self, service):
        """Test filename generation pattern 0: Title without year."""
        metadata = {"title": "The Great Book"}

        result = service.generate_output_filename(metadata, 0)

        assert result == "The Great Book.m4b"

    def test_generate_filename_pattern_1_author_and_title(self, service):
        """Test filename generation pattern 1: Author - Title."""
        metadata = {"artist": "John Doe", "title": "The Great Book"}

        result = service.generate_output_filename(metadata, 1)

        assert result == "John Doe - The Great Book.m4b"

    def test_generate_filename_pattern_1_title_only(self, service):
        """Test filename generation pattern 1: Title only when no author."""
        metadata = {"title": "The Great Book"}

        result = service.generate_output_filename(metadata, 1)

        assert result == "The Great Book.m4b"

    def test_generate_filename_pattern_2_series_and_title(self, service):
        """Test filename generation pattern 2: Series - Title."""
        metadata = {"series": "Mystery Series", "title": "The Great Book"}

        result = service.generate_output_filename(metadata, 2)

        assert result == "Mystery Series - The Great Book.m4b"

    def test_generate_filename_pattern_2_title_only(self, service):
        """Test filename generation pattern 2: Title only when no series."""
        metadata = {"title": "The Great Book"}

        result = service.generate_output_filename(metadata, 2)

        assert result == "The Great Book.m4b"

    def test_generate_filename_fallback_empty_metadata(self, service):
        """Test filename generation fallback with empty metadata."""
        metadata = {}

        result = service.generate_output_filename(metadata, 0)

        assert result == "audiobook.m4b"

    def test_generate_filename_fallback_invalid_pattern(self, service):
        """Test filename generation with invalid pattern ID."""
        metadata = {"title": "The Great Book"}

        result = service.generate_output_filename(metadata, 999)

        assert result == "audiobook.m4b"

    def test_generate_filename_fallback_empty_title(self, service):
        """Test filename generation fallback with empty title."""
        metadata = {"title": "", "artist": "John Doe"}

        result = service.generate_output_filename(metadata, 0)

        assert result == "audiobook.m4b"

    def test_generate_filename_fallback_whitespace_title(self, service):
        """Test filename generation fallback with whitespace-only title."""
        metadata = {"title": "   ", "artist": "John Doe"}

        result = service.generate_output_filename(metadata, 1)

        assert result == "audiobook.m4b"

    # ===== Character Sanitization Tests =====

    def test_sanitize_path_removes_invalid_characters(self, service):
        """Test path sanitization removes invalid filesystem characters."""
        test_path = 'Test<>:"/\\|?*Path'

        result = service._sanitize_path(test_path)

        # All invalid characters should be replaced with underscores
        assert result == "Test_________Path"
        assert "<" not in result
        assert ">" not in result
        assert ":" not in result
        assert '"' not in result
        assert "/" not in result
        assert "\\" not in result
        assert "|" not in result
        assert "?" not in result
        assert "*" not in result

    def test_sanitize_filename_uses_same_rules(self, service):
        """Test filename sanitization uses same rules as path sanitization."""
        test_filename = 'Test<>:"/\\|?*File.m4b'

        result = service._sanitize_filename(test_filename)

        assert result == "Test_________File.m4b"

    def test_calculate_output_path_sanitizes_artist_and_series(self, service):
        """Test output path calculation sanitizes artist and series names."""
        base_dir = "/output"
        metadata = {"artist": "John<Doe", "series": "Mystery*Series"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/output/John_Doe/Mystery_Series"

    def test_generate_filename_sanitizes_all_fields(self, service):
        """Test filename generation sanitizes all metadata fields."""
        metadata = {"artist": "John<Doe", "title": "The*Great?Book", "year": "20:23"}

        result = service.generate_output_filename(metadata, 1)

        assert result == "John_Doe - The_Great_Book.m4b"

    # ===== Safe String Conversion Tests =====

    def test_safe_str_handles_none_values(self, service):
        """Test _safe_str method handles None values correctly."""
        assert service._safe_str(None) == ""
        assert service._safe_str("test") == "test"
        assert service._safe_str(123) == "123"
        assert service._safe_str(0) == "0"
        assert service._safe_str(False) == "False"

    # ===== Edge Cases and Error Handling =====

    def test_generate_filename_invalid_empty_results(self, service):
        """Test filename generation handles invalid empty results."""
        # Test cases that would result in invalid filenames
        test_cases = [
            {"title": "", "year": ""},  # Would create "().m4b"
            {"title": " ", "year": " "},  # Would create "( ).m4b"
            {"artist": "", "title": ""},  # Would create " - .m4b"
        ]

        for metadata in test_cases:
            for pattern_id in [0, 1, 2]:
                result = service.generate_output_filename(metadata, pattern_id)
                assert result == "audiobook.m4b"
                assert result.strip() != ""
                assert result.strip() not in [".m4b", "().m4b", "( ).m4b"]

    def test_path_calculation_with_complex_base_dir(self, service):
        """Test path calculation with complex base directory paths."""
        base_dir = "/complex/path/with spaces/output"
        metadata = {"artist": "John Doe", "series": "Mystery Series"}

        result = service.calculate_output_path(base_dir, metadata, True)

        assert result == "/complex/path/with spaces/output/John Doe/Mystery Series"

    def test_filename_generation_with_numeric_metadata(self, service):
        """Test filename generation with numeric metadata values."""
        metadata = {"title": 123, "artist": 456, "year": 2023}

        result = service.generate_output_filename(metadata, 1)

        assert result == "456 - 123.m4b"

    def test_generate_filename_fallback_edge_case_coverage(self, service):
        """Test filename generation fallback for edge case coverage."""
        # Test case that specifically triggers the fallback on line 104
        # Using a title that results in "().m4b" after processing
        metadata = {"title": "()", "artist": "", "year": ""}

        result = service.generate_output_filename(metadata, 0)

        # Should fallback to default because "().m4b" is in the invalid list
        assert result == "audiobook.m4b"
