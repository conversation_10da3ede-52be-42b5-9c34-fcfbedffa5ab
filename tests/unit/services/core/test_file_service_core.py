"""Comprehensive tests for FileServiceCore.

This module tests the pure Python FileServiceCore implementation,
focusing on critical path behaviors and achieving >95% coverage.

Critical behaviors tested:
- File format validation (mp3, m4a, m4b, AAC)
- Combined size calculation accuracy
- File reordering integrity
- Thread safety for concurrent operations
- Callback invocation patterns
- Error handling and categorization
"""

import os
import threading
from pathlib import Path
from typing import Any, Callable, List, Optional
from unittest.mock import patch

import pytest

from src.abb.services.core.file_service_core import FileServiceCore
from src.abb.services.interfaces import ErrorCategory, ServiceError


class CallbackCapture:
    """Utility for capturing and verifying callback invocations."""

    def __init__(self):
        """Initialize the callback capture."""
        self.success_calls: List[Any] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()

    def success_callback(self, *args, **kwargs):
        """Capture successful callback invocations."""
        with self._lock:
            self.success_calls.append((args, kwargs))
            self.call_count += 1

    def error_callback(self, error_msg: str):
        """Capture error callback invocations."""
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1

    def reset(self):
        """Reset all captured calls."""
        with self._lock:
            self.success_calls.clear()
            self.error_calls.clear()
            self.call_count = 0

    @property
    def has_success_calls(self) -> bool:
        """Check if any success callbacks were called."""
        return len(self.success_calls) > 0

    @property
    def has_error_calls(self) -> bool:
        """Check if any error callbacks were called."""
        return len(self.error_calls) > 0

    @property
    def last_success_args(self) -> Optional[tuple]:
        """Get the arguments from the last success callback."""
        return self.success_calls[-1][0] if self.success_calls else None

    @property
    def last_error_message(self) -> Optional[str]:
        """Get the last error message."""
        return self.error_calls[-1] if self.error_calls else None


class ThreadSafetyTester:
    """Utilities for testing thread safety of core services."""

    @staticmethod
    def run_concurrent_operations(operations: List[Callable], num_threads: int = 5) -> List[Any]:
        """Run multiple operations concurrently and collect results."""
        results = []
        exceptions = []
        threads = []

        def worker(operation, result_list, exception_list):
            try:
                result = operation()
                result_list.append(result)
            except Exception as e:
                exception_list.append(e)

        # Start threads
        for operation in operations:
            thread = threading.Thread(target=worker, args=(operation, results, exceptions))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        if exceptions:
            raise exceptions[0]  # Re-raise first exception

        return results


class TempAudioFiles:
    """Create temporary audio files for testing."""

    def __init__(self, tmp_path: Path):
        """Initialize with a temporary directory path."""
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []

    def create_mp3_file(self, name: str = "test.mp3", size_bytes: int = 1024) -> Path:
        """Create a temporary MP3 file with specified size."""
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ID3" + b"\x00" * (size_bytes - 3))  # Minimal MP3 header
        self.created_files.append(file_path)
        return file_path

    def create_m4a_file(self, name: str = "test.m4a", size_bytes: int = 1024) -> Path:
        """Create a temporary M4A file with specified size."""
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ftyp" + b"\x00" * (size_bytes - 4))  # Minimal M4A header
        self.created_files.append(file_path)
        return file_path

    def create_invalid_file(self, name: str = "test.txt", size_bytes: int = 100) -> Path:
        """Create a temporary invalid audio file."""
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"invalid" + b"\x00" * (size_bytes - 7))
        self.created_files.append(file_path)
        return file_path

    def create_multiple_files(self, count: int = 3, prefix: str = "test") -> List[Path]:
        """Create multiple test audio files."""
        files = []
        for i in range(count):
            if i % 2 == 0:
                file_path = self.create_mp3_file(f"{prefix}_{i}.mp3", 1024 * (i + 1))
            else:
                file_path = self.create_m4a_file(f"{prefix}_{i}.m4a", 1024 * (i + 1))
            files.append(file_path)
        return files


class TestFileServiceCore:
    """Test suite for FileServiceCore critical path behaviors."""

    @pytest.fixture
    def service(self):
        """Create a fresh FileServiceCore instance for each test."""
        return FileServiceCore()

    @pytest.fixture
    def callback_capture(self):
        """Create a callback capture utility for each test."""
        return CallbackCapture()

    @pytest.fixture
    def temp_audio_files(self, tmp_path):
        """Create temporary audio files for testing."""
        return TempAudioFiles(tmp_path)

    def test_initialization(self, service):
        """Test FileServiceCore initializes with empty state."""
        assert service.get_files() == []
        assert service.get_file_count() == 0
        assert service.get_combined_size() == 0
        assert service.format_combined_size() == "0 B"

    def test_add_files_validates_audio_formats(self, service, callback_capture, temp_audio_files):
        """Test only mp3, m4a, m4b, AAC files accepted."""
        # Create valid audio files
        mp3_file = temp_audio_files.create_mp3_file("test.mp3")
        m4a_file = temp_audio_files.create_m4a_file("test.m4a")
        m4b_file = temp_audio_files.create_m4a_file("test.m4b")  # M4B is M4A container
        aac_file = temp_audio_files.create_m4a_file("test.aac")

        # Create invalid file
        invalid_file = temp_audio_files.create_invalid_file("test.txt")

        # Test adding valid files
        valid_files = [str(mp3_file), str(m4a_file), str(m4b_file), str(aac_file)]
        service.add_files(
            valid_files, callback_capture.success_callback, callback_capture.error_callback
        )

        # Should accept all valid files
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 4
        assert service.get_file_count() == 4

        # Reset and test invalid file
        callback_capture.reset()
        service.add_files(
            [str(invalid_file)], callback_capture.success_callback, callback_capture.error_callback
        )

        # Should reject invalid file but still call success callback with empty list
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0
        assert callback_capture.has_error_calls
        assert "Invalid audio file format" in callback_capture.last_error_message

    def test_get_combined_size_accuracy(self, service, callback_capture, temp_audio_files):
        """Test combined size calculation correctness."""
        # Create files with known sizes
        file1 = temp_audio_files.create_mp3_file("file1.mp3", 1000)
        file2 = temp_audio_files.create_m4a_file("file2.m4a", 2000)
        file3 = temp_audio_files.create_mp3_file("file3.mp3", 3000)

        # Add files and verify size calculation
        service.add_files(
            [str(file1), str(file2), str(file3)],
            callback_capture.success_callback,
            callback_capture.error_callback,
        )

        expected_size = 1000 + 2000 + 3000
        assert service.get_combined_size() == expected_size

        # Test formatted size display
        formatted = service.format_combined_size()
        assert "KB" in formatted or "B" in formatted

    def test_reorder_files_maintains_integrity(self, service, callback_capture, temp_audio_files):
        """Test file order changes don't corrupt list."""
        # Create and add files
        files = temp_audio_files.create_multiple_files(3, "reorder_test")
        file_paths = [str(f) for f in files]

        service.add_files(file_paths, callback_capture.success_callback)
        original_order = service.get_files()

        # Test reordering
        new_order = [original_order[2], original_order[0], original_order[1]]
        callback_capture.reset()

        service.reorder_files(
            new_order, callback_capture.success_callback, callback_capture.error_callback
        )

        # Verify reordering succeeded
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_files() == new_order
        assert service.get_file_count() == 3

    def test_thread_safety_concurrent_operations(self, service, temp_audio_files):
        """Test concurrent add/remove operations are safe."""
        # Create multiple files for concurrent operations
        files = temp_audio_files.create_multiple_files(10, "concurrent_test")
        file_paths = [str(f) for f in files]

        # Define concurrent operations
        def add_operation():
            capture = CallbackCapture()
            service.add_files(file_paths[:5], capture.success_callback, capture.error_callback)
            return capture

        def size_operation():
            return service.get_combined_size()

        def count_operation():
            return service.get_file_count()

        # Run operations concurrently
        operations = [add_operation, size_operation, count_operation] * 3
        results = ThreadSafetyTester.run_concurrent_operations(operations)

        # Verify no exceptions occurred and service state is consistent
        assert len(results) == 9
        final_count = service.get_file_count()
        assert final_count >= 0  # Should be non-negative
        assert service.get_combined_size() >= 0  # Should be non-negative

    def test_callback_invocation_patterns(self, service, callback_capture, temp_audio_files):
        """Test success/error callbacks triggered correctly."""
        # Test successful operation
        valid_file = temp_audio_files.create_mp3_file("valid.mp3")
        service.add_files(
            [str(valid_file)], callback_capture.success_callback, callback_capture.error_callback
        )

        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.call_count == 1

        # Test error operation (non-existent file)
        callback_capture.reset()
        service.add_files(
            ["/non/existent/file.mp3"],
            callback_capture.success_callback,
            callback_capture.error_callback,
        )

        assert callback_capture.has_success_calls  # Still called with empty list
        assert callback_capture.has_error_calls
        assert "File not found" in callback_capture.last_error_message

    def test_remove_file_by_index(self, service, callback_capture, temp_audio_files):
        """Test safe file removal from list."""
        # Add files first
        files = temp_audio_files.create_multiple_files(3, "remove_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        assert service.get_file_count() == 3
        original_files = service.get_files()

        # Remove middle file
        callback_capture.reset()
        service.remove_file(1, callback_capture.success_callback, callback_capture.error_callback)

        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_file_count() == 2

        # Verify correct file was removed
        remaining_files = service.get_files()
        assert original_files[0] in remaining_files
        assert original_files[1] not in remaining_files
        assert original_files[2] in remaining_files

    def test_remove_file_invalid_index(self, service, callback_capture, temp_audio_files):
        """Test error handling for invalid remove index."""
        # Add one file
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        service.add_files([str(file1)], callback_capture.success_callback)

        # Try to remove with invalid index
        callback_capture.reset()
        service.remove_file(5, callback_capture.success_callback, callback_capture.error_callback)

        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Invalid index" in callback_capture.last_error_message
        assert service.get_file_count() == 1  # File should still be there

    def test_file_validation_and_normalization(self, service, callback_capture, temp_audio_files):
        """Test path normalization and file existence validation."""
        # Create file with relative path
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        relative_path = os.path.relpath(str(file1))

        service.add_files(
            [relative_path], callback_capture.success_callback, callback_capture.error_callback
        )

        # Should normalize to absolute path
        assert callback_capture.has_success_calls
        added_files = service.get_files()
        assert len(added_files) == 1
        assert os.path.isabs(added_files[0])

    def test_duplicate_file_handling(self, service, callback_capture, temp_audio_files):
        """Test that duplicate files are not added twice."""
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        file_path = str(file1)

        # Add file first time
        service.add_files([file_path], callback_capture.success_callback)
        assert service.get_file_count() == 1

        # Try to add same file again
        callback_capture.reset()
        service.add_files([file_path], callback_capture.success_callback)

        # Should not add duplicate
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0  # No files added
        assert service.get_file_count() == 1  # Count unchanged

    def test_clear_files(self, service, callback_capture, temp_audio_files):
        """Test clearing all files from the managed list."""
        # Add some files
        files = temp_audio_files.create_multiple_files(3, "clear_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        assert service.get_file_count() == 3

        # Clear files
        callback_capture.reset()
        service.clear_files(callback_capture.success_callback, callback_capture.error_callback)

        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_file_count() == 0
        assert service.get_files() == []
        assert service.get_combined_size() == 0

    def test_validate_files_removes_missing(self, service, callback_capture, temp_audio_files):
        """Test that validate_files removes files that no longer exist."""
        # Add files
        files = temp_audio_files.create_multiple_files(3, "validate_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        assert service.get_file_count() == 3

        # Delete one file from filesystem
        files[1].unlink()

        # Validate files
        callback_capture.reset()
        service.validate_files(callback_capture.success_callback, callback_capture.error_callback)

        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls

        # Should have removed the deleted file
        removed_files = callback_capture.last_success_args[0]
        assert len(removed_files) == 1
        assert str(files[1]) in removed_files
        assert service.get_file_count() == 2

    def test_reorder_files_validation_error(self, service, callback_capture, temp_audio_files):
        """Test reorder_files with invalid new order."""
        # Add files
        files = temp_audio_files.create_multiple_files(2, "reorder_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        # Try to reorder with different files
        invalid_order = ["/some/other/file.mp3", str(files[0])]
        callback_capture.reset()

        service.reorder_files(
            invalid_order, callback_capture.success_callback, callback_capture.error_callback
        )

        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "must contain exactly the same files" in callback_capture.last_error_message

    def test_format_combined_size_units(self, service, callback_capture, temp_audio_files):
        """Test formatted size display with different units."""
        # Test bytes
        small_file = temp_audio_files.create_mp3_file("small.mp3", 500)
        service.add_files([str(small_file)], callback_capture.success_callback)
        assert "B" in service.format_combined_size()

        # Test KB
        service.clear_files(callback_capture.success_callback)
        kb_file = temp_audio_files.create_mp3_file("kb.mp3", 2048)  # 2KB
        service.add_files([str(kb_file)], callback_capture.success_callback)
        formatted = service.format_combined_size()
        assert "KB" in formatted

        # Test MB
        service.clear_files(callback_capture.success_callback)
        mb_file = temp_audio_files.create_mp3_file("mb.mp3", 2 * 1024 * 1024)  # 2MB
        service.add_files([str(mb_file)], callback_capture.success_callback)
        formatted = service.format_combined_size()
        assert "MB" in formatted

    def test_error_categorization(self, service, callback_capture):
        """Test proper ServiceError categorization."""
        # Test add_files with non-existent file - doesn't raise, just skips the file
        service.add_files(["/non/existent/file.mp3"], callback_capture.success_callback)
        # Should succeed with empty list (no files added)
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0

        # Test remove with invalid index - should raise ServiceError
        with pytest.raises(ServiceError) as exc_info:
            service.remove_file(999, callback_capture.success_callback)

        assert exc_info.value.category == ErrorCategory.PROCESSING_ERROR

        # Test path normalization error - should raise ServiceError
        with pytest.raises(ServiceError) as exc_info:
            service._normalize_path("\x00invalid\x00path")

        assert exc_info.value.category == ErrorCategory.VALIDATION_ERROR

    def test_concurrent_add_remove_operations(self, service, temp_audio_files):
        """Test concurrent add and remove operations for thread safety."""
        # Create files for testing
        files = temp_audio_files.create_multiple_files(5, "concurrent_add_remove")
        file_paths = [str(f) for f in files]

        # Add initial files
        capture = CallbackCapture()
        service.add_files(file_paths[:3], capture.success_callback)

        def add_more_files():
            capture = CallbackCapture()
            service.add_files(file_paths[3:], capture.success_callback, capture.error_callback)
            return capture

        def remove_file():
            capture = CallbackCapture()
            try:
                service.remove_file(0, capture.success_callback, capture.error_callback)
            except:
                pass  # Ignore errors from concurrent access
            return capture

        def get_count():
            return service.get_file_count()

        # Run concurrent operations
        operations = [add_more_files, remove_file, get_count] * 2
        ThreadSafetyTester.run_concurrent_operations(operations)

        # Verify service is in a consistent state
        final_count = service.get_file_count()
        assert final_count >= 0
        assert len(service.get_files()) == final_count

    def test_file_access_errors_during_add(self, service, callback_capture, temp_audio_files):
        """Test handling of file access errors during add operation."""
        # Create a file and then make it inaccessible
        valid_file = temp_audio_files.create_mp3_file("accessible.mp3")

        # Test with a path that will cause OSError during normalization
        import tempfile

        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a path that's too long or has invalid characters
            invalid_path = os.path.join(temp_dir, "a" * 300 + ".mp3")  # Very long path

            service.add_files(
                [str(valid_file), invalid_path],
                callback_capture.success_callback,
                callback_capture.error_callback,
            )

            # Should add the valid file and report error for invalid path
            assert callback_capture.has_success_calls
            assert len(callback_capture.last_success_args[0]) == 1
            assert callback_capture.has_error_calls

    def test_get_combined_size_with_inaccessible_files(
        self, service, callback_capture, temp_audio_files
    ):
        """Test combined size calculation when some files become inaccessible."""
        # Add files first
        files = temp_audio_files.create_multiple_files(2, "size_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        # Delete one file from filesystem
        files[0].unlink()

        # Combined size should still work (skip inaccessible files)
        size = service.get_combined_size()
        assert size >= 0  # Should not crash

    def test_clear_files_exception_handling(self, service, callback_capture, temp_audio_files):
        """Test clear_files error handling."""
        # Add some files
        files = temp_audio_files.create_multiple_files(2, "clear_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        # Mock an exception during clear operation
        with patch.object(service, "_files") as mock_files:
            mock_files.clear.side_effect = RuntimeError("Mock clear error")

            callback_capture.reset()
            service.clear_files(callback_capture.success_callback, callback_capture.error_callback)

            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to clear files" in callback_capture.last_error_message

    def test_validate_files_exception_handling(self, service, callback_capture, temp_audio_files):
        """Test validate_files error handling."""
        # Add some files
        files = temp_audio_files.create_multiple_files(2, "validate_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        # Mock an exception during validation
        with patch("os.path.exists") as mock_exists:
            mock_exists.side_effect = RuntimeError("Mock validation error")

            callback_capture.reset()
            service.validate_files(
                callback_capture.success_callback, callback_capture.error_callback
            )

            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to validate files" in callback_capture.last_error_message

    def test_format_combined_size_gb_range(self, service, callback_capture, temp_audio_files):
        """Test formatted size display for GB range."""
        # Create a large file (mock the size)
        file1 = temp_audio_files.create_mp3_file("large.mp3", 2 * 1024 * 1024 * 1024)  # 2GB
        service.add_files([str(file1)], callback_capture.success_callback)

        formatted = service.format_combined_size()
        assert "GB" in formatted

    def test_is_valid_audio_file_exception_handling(self, service):
        """Test _is_valid_audio_file with path that causes exception."""
        # Test with None or invalid path that might cause exception
        result = service._is_valid_audio_file("")
        assert result is False  # Should handle gracefully

        # Test with path that has no suffix
        result = service._is_valid_audio_file("no_extension")
        assert result is False

    def test_add_files_unexpected_exception_handling(
        self, service, callback_capture, temp_audio_files
    ):
        """Test add_files handling of unexpected exceptions."""
        # Create a valid file
        valid_file = temp_audio_files.create_mp3_file("test.mp3")

        # Mock an unexpected exception during path normalization
        with patch.object(service, "_normalize_path") as mock_normalize:
            mock_normalize.side_effect = [str(valid_file), RuntimeError("Unexpected error")]

            service.add_files(
                [str(valid_file), "another_file.mp3"],
                callback_capture.success_callback,
                callback_capture.error_callback,
            )

            # Should handle the exception and continue processing
            assert callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Unexpected error processing" in callback_capture.last_error_message

    def test_validate_files_with_access_errors(self, service, callback_capture, temp_audio_files):
        """Test validate_files handling of file access errors."""
        # Add some files
        files = temp_audio_files.create_multiple_files(2, "access_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)

        # Mock os.access to raise an exception for one file
        def mock_access(path, mode):
            if "access_error_test_0" in path:
                raise OSError("Access denied")
            return True

        with patch("os.access", side_effect=mock_access):
            callback_capture.reset()
            service.validate_files(
                callback_capture.success_callback, callback_capture.error_callback
            )

            # Should handle the access error and remove the problematic file
            assert callback_capture.has_success_calls
            removed_files = callback_capture.last_success_args[0]
            assert len(removed_files) == 1
            assert "access_error_test_0" in removed_files[0]
