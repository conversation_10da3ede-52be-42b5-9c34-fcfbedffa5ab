import pytest
from src.abb.services.path_service import PathService


class TestPathService:
    def test_path_service_exists_and_has_required_methods(self):
        """Smoke test to ensure refactoring worked"""
        service = PathService()
        assert hasattr(service, 'calculate_output_path')
        assert hasattr(service, 'generate_output_filename')
        assert callable(service.calculate_output_path)
        assert callable(service.generate_output_filename)
    
    def test_sanitize_path_removes_invalid_chars(self):
        """Quick test of critical functionality"""
        service = PathService()
        result = service._sanitize_path('Test:File*Name?.txt')
        assert ':' not in result
        assert '*' not in result
        assert '?' not in result
        assert '_' in result  # Should replace with underscores
    
    def test_generate_output_filename_returns_valid_filename(self):
        """Ensure basic filename generation works"""
        service = PathService()
        metadata = {'title': 'Test Book', 'artist': 'Test Author'}
        
        # Test pattern 0 (Title)
        filename = service.generate_output_filename(metadata, 0)
        assert filename == 'Test Book.m4b'
        
        # Test pattern 1 (Author - Title)
        filename = service.generate_output_filename(metadata, 1)
        assert filename == 'Test Author - Test Book.m4b'