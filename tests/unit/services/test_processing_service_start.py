"""Tests for ProcessingService.start_processing method."""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from src.abb.services.processing_service import ProcessingService


class TestProcessingServiceStart:
    """Test ProcessingService start_processing functionality."""
    
    @pytest.fixture
    def service(self):
        """Create a ProcessingService instance."""
        with patch('src.abb.services.processing_service._get_executable_path'), \
             patch('src.abb.services.processing_service.ProcessingValidator'), \
             patch('src.abb.services.processing_service.PathService'), \
             patch('src.abb.services.processing_service.ProcessingWorker'), \
             patch('src.abb.services.processing_service.QThread'):
            service = ProcessingService()
            # Ensure the mocked objects have the methods we need
            service._validator = Mock()
            service._path_service = Mock()
            return service
    
    @pytest.fixture
    def mock_file_list(self):
        """Create mock file list."""
        return ["file1.mp3", "file2.mp3", "file3.mp3"]
    
    @pytest.fixture
    def mock_output_settings(self):
        """Create mock output settings."""
        return {
            'output_directory': '/output/base',
            'output_filename_pattern': 0,
            'use_subdirectory_pattern': True,
            'output_bitrate': 64,
            'output_channels': 1,
            'output_sample_rate': None
        }
    
    @pytest.fixture
    def mock_metadata(self):
        """Create mock metadata."""
        return {
            'title': 'Test Book',
            'artist': 'Test Author',
            'album': 'Test Album',
            'year': '2025'
        }
    
    def test_service_starts_worker_with_correct_params_and_connects_signals(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        """Test that service correctly starts worker and connects signals."""
        # Mock dependencies
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output/final'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full') as mock_process:
            
            # Call start_processing
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata,
                cover_art_path='/path/to/cover.jpg'
            )
            
            # Verify process_full was called with correct parameters
            mock_process.assert_called_once()
            call_args = mock_process.call_args[1]
            
            assert call_args['input_files'] == mock_file_list
            assert call_args['output_path'] == '/output/final'
            assert call_args['output_filename'] == 'output.m4b'
            assert call_args['metadata']['cover_art_temp_path'] == '/path/to/cover.jpg'
            assert call_args['settings']['bitrate'] == 64
            assert call_args['settings']['channels'] == 1
            assert 'sample_rate' not in call_args['settings']
    
    def test_validation_failure_emits_error(
        self, service, mock_file_list, mock_output_settings, mock_metadata, qtbot
    ):
        """Test that validation failure emits error signal."""
        # Mock validation to fail
        with patch.object(service._validator, 'validate_all', 
                         return_value=(False, "Insufficient disk space")), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output/test'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300):
            
            # Connect to error signal
            with qtbot.waitSignal(service.error, timeout=100) as blocker:
                service.start_processing(
                    file_list=mock_file_list,
                    output_settings=mock_output_settings,
                    metadata=mock_metadata
                )
            
            # Check error message
            assert len(blocker.args) == 1
            assert "Validation failed: Insufficient disk space" in blocker.args[0]
    
    def test_estimated_size_calculation(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        """Test that estimated size is calculated correctly."""
        # Mock duration to 1 hour per file (3 hours total)
        with patch('src.abb.services.processing_service.get_duration', return_value=3600), \
             patch.object(service._validator, 'validate_all') as mock_validate, \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch.object(service, 'process_full'):
            
            # Make validate_all return success so processing continues
            mock_validate.return_value = (True, None)
            
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            
            # Check estimated size calculation
            # 3 files * 3600 seconds * 64 kbps * 1000 / 8 / (1024 * 1024) = ~82.4 MB
            call_args = mock_validate.call_args[1]
            assert abs(call_args['estimated_size_mb'] - 82.4) < 0.1
    
    def test_sample_rate_included_when_specified(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        """Test that sample rate is included in settings when specified."""
        # Add sample rate to settings
        mock_output_settings['output_sample_rate'] = 44100
        
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full') as mock_process:
            
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            
            # Check that sample_rate was included
            call_args = mock_process.call_args[1]
            assert call_args['settings']['sample_rate'] == 44100
    
    def test_subdirectory_pattern_usage(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        """Test that subdirectory pattern is correctly passed to path service."""
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path') as mock_calc_path, \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full'):
            
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            
            # Verify calculate_output_path was called with correct subdirectory setting
            mock_calc_path.assert_called_once_with(
                base_directory='/output/base',
                metadata=mock_metadata,
                use_subdirectory=True
            )
    
    def test_filename_pattern_passed_correctly(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        """Test that filename pattern is correctly passed to path service."""
        mock_output_settings['output_filename_pattern'] = 1
        
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename') as mock_gen_filename, \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full'):
            
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            
            # Verify generate_output_filename was called with correct pattern
            mock_gen_filename.assert_called_once_with(
                metadata=mock_metadata,
                pattern=1
            )