import unittest
import os
import json
import tempfile
import shutil
import logging
from unittest.mock import patch, MagicMock

# Assuming SettingsManager is in src/abb/services/
# Adjust import path if necessary based on project structure
from src.abb.services.settings_manager import SettingsManager
from PySide6.QtCore import QObject, Signal

# Suppress logging during tests unless explicitly testing logging
logging.disable(logging.CRITICAL)

class TestSettingsManager(unittest.TestCase):

    def setUp(self):
        # Create a temporary directory for settings files
        self.test_dir = tempfile.mkdtemp()
        self.settings_file = os.path.join(self.test_dir, "test_settings.json")
        self.default_settings = {
            "output_bitrate": 128,
            "output_directory": "/tmp/output",
            "output_filename_pattern": 0,
            "some_other_setting": "value"
        }

    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.test_dir)
        logging.disable(logging.CRITICAL) # Re-disable logging after tests

    def _create_settings_file(self, content):
        with open(self.settings_file, 'w') as f:
            json.dump(content, f)

    def test_initialization_with_defaults_and_no_file(self):
        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128)
        self.assertEqual(manager.get_setting("output_directory"), "/tmp/output")
        self.assertEqual(manager.get_setting("some_other_setting"), "value")
        self.assertFalse(os.path.exists(self.settings_file)) # File should not be created on init if no changes

    def test_initialization_loads_from_file(self):
        initial_file_content = {
            "output_bitrate": 96,
            "output_directory": "/home/<USER>/music",
            "new_setting_from_file": "loaded"
        }
        self._create_settings_file(initial_file_content)

        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 96) # Overrides default
        self.assertEqual(manager.get_setting("output_directory"), "/home/<USER>/music") # Overrides default
        self.assertEqual(manager.get_setting("output_filename_pattern"), 0) # Retains default
        self.assertEqual(manager.get_setting("new_setting_from_file"), "loaded") # New setting from file

    @patch('src.abb.services.settings_manager.logger')
    def test_initialization_handles_corrupt_file(self, mock_logger):
        with open(self.settings_file, 'w') as f:
            f.write("this is not valid json")

        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128) # Should fall back to default
        mock_logger.warning.assert_called_with(f"Corrupt settings file: {self.settings_file}. Using default settings.")

    def test_initialization_handles_missing_file(self):
        # Ensure file does not exist
        if os.path.exists(self.settings_file):
            os.remove(self.settings_file)

        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128) # Should use default
        self.assertFalse(os.path.exists(self.settings_file))

    def test_get_setting_retrieves_value_and_default(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("test_key", "test_value")
        self.assertEqual(manager.get_setting("test_key"), "test_value")
        self.assertEqual(manager.get_setting("non_existent_key"), None)
        self.assertEqual(manager.get_setting("non_existent_key", "my_default"), "my_default")

    def test_set_setting_updates_memory_and_file(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("output_bitrate", 64)
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        
        # Verify file content
        with open(self.settings_file, 'r') as f:
            loaded_content = json.load(f)
            self.assertEqual(loaded_content.get("output_bitrate"), 64)

        manager.set_setting("new_key", "new_value")
        self.assertEqual(manager.get_setting("new_key"), "new_value")
        with open(self.settings_file, 'r') as f:
            loaded_content = json.load(f)
            self.assertEqual(loaded_content.get("new_key"), "new_value")

    def test_set_setting_emits_signal(self):
        manager = SettingsManager(self.settings_file)
        mock_slot = MagicMock()
        manager.settings_changed.connect(mock_slot)

        manager.set_setting("output_bitrate", 64)
        mock_slot.assert_called_once_with("output_bitrate", 64)
        mock_slot.reset_mock()

        manager.set_setting("output_directory", "/new/path")
        mock_slot.assert_called_once_with("output_directory", "/new/path")

    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_bitrate(self, mock_logger):
        manager = SettingsManager(self.settings_file)

        # Valid
        manager.set_setting("output_bitrate", 64)
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        mock_logger.warning.assert_not_called()

        # Invalid - not in list
        manager.set_setting("output_bitrate", 100)
        self.assertEqual(manager.get_setting("output_bitrate"), 64) # Should not change
        mock_logger.warning.assert_called_with("Invalid output_bitrate: 100. Must be one of [32, 48, 56, 64, 96, 128].")
        mock_logger.reset_mock()

        # Invalid - wrong type
        manager.set_setting("output_bitrate", "abc")
        self.assertEqual(manager.get_setting("output_bitrate"), 64) # Should not change
        mock_logger.warning.assert_called_with("Invalid output_bitrate: abc. Must be one of [32, 48, 56, 64, 96, 128].")

    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_directory(self, mock_logger):
        manager = SettingsManager(self.settings_file)

        # Valid
        manager.set_setting("output_directory", "/valid/path")
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path")
        mock_logger.warning.assert_not_called()

        # Invalid - empty string
        manager.set_setting("output_directory", "")
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path") # Should not change
        mock_logger.warning.assert_called_with("Invalid output_directory: . Must be a non-empty string.")
        mock_logger.reset_mock()

        # Invalid - wrong type
        manager.set_setting("output_directory", 123)
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path") # Should not change
        mock_logger.warning.assert_called_with("Invalid output_directory: 123. Must be a non-empty string.")

    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_filename_pattern(self, mock_logger):
        manager = SettingsManager(self.settings_file)

        # Valid
        manager.set_setting("output_filename_pattern", 1)
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1)
        mock_logger.warning.assert_not_called()

        # Invalid - not in list
        manager.set_setting("output_filename_pattern", 3)
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1) # Should not change
        mock_logger.warning.assert_called_with("Invalid output_filename_pattern: 3. Must be 0, 1, or 2.")
        mock_logger.reset_mock()

        # Invalid - wrong type
        manager.set_setting("output_filename_pattern", "a")
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1) # Should not change
        mock_logger.warning.assert_called_with("Invalid output_filename_pattern: a. Must be 0, 1, or 2.")

    def test_set_setting_unknown_key_no_validation_error(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("new_unvalidated_key", "some_value")
        self.assertEqual(manager.get_setting("new_unvalidated_key"), "some_value")
        # Ensure no warning/error for unvalidated key
        with patch('src.abb.services.settings_manager.logger') as mock_logger:
            manager.set_setting("another_unvalidated_key", 123)
            mock_logger.warning.assert_not_called()
            mock_logger.error.assert_not_called()
        self.assertEqual(manager.get_setting("another_unvalidated_key"), 123)

if __name__ == '__main__':
    unittest.main()