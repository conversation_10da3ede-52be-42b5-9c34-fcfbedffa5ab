"""
Unit tests for MetadataService.

Tests the extract_and_load_metadata method and signal emissions.
Includes comprehensive coverage for both ABB_NEW_META=True/False paths.
"""
import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QObject

from src.abb.services.metadata_service import MetadataService


class TestMetadataService:
    """Test cases for MetadataService."""
    
    def test_metadata_service_extract_and_load_metadata_populates_attributes_and_emits_signals(self, qtbot):
        """Test that extract_and_load_metadata populates internal attributes and emits signals."""
        # Mock the utility functions
        mock_tags = {
            "title": "Test Title",
            "artist": "Test Artist",
            "album": "Test Album",
            "genre": "Test Genre",
            "track_number": "1",
            "year": "2023"
        }
        mock_cover_data = b"fake_cover_image_data"
        
        with patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            
            # Create service instance
            service = MetadataService()
            
            # Set up signal spies
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            
            # Call the method
            test_filepath = "/path/to/test.mp3"
            service.extract_and_load_metadata(test_filepath)
            
            # Verify the utility functions were called with correct arguments
            mock_extract_tags.assert_called_once_with(test_filepath)
            mock_extract_cover.assert_called_once_with(test_filepath)
            
            # Verify internal attributes were populated
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data == mock_cover_data
            
            # Verify signals were emitted with correct data
            assert metadata_spy.signal_triggered
            
            # Check the emitted values - metadata now includes cover art data
            emitted_metadata = metadata_spy.args[0]
            
            # Verify metadata fields
            for key, value in mock_tags.items():
                assert emitted_metadata[key] == value
            
            # Verify cover art data is included
            assert emitted_metadata['cover_art_data'] == mock_cover_data
    
    def test_metadata_service_handles_none_cover_art(self, qtbot):
        """Test that the service correctly handles when no cover art is found."""
        mock_tags = {"title": "No Cover", "artist": "Test", "album": None, "genre": None, "track_number": None, "year": None}
        mock_cover_data = None  # No cover art
        
        with patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            
            service = MetadataService()
            
            # Set up signal spies
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            
            service.extract_and_load_metadata("/path/to/nocoverart.mp3")
            
            # Verify internal state
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data is None
            
            # Verify signals
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            
            # Verify metadata fields
            for key, value in mock_tags.items():
                assert emitted_metadata[key] == value
            
            # Cover art data should not be in metadata when None
            assert 'cover_art_data' not in emitted_metadata
    
    def test_metadata_service_update_current_metadata_modifies_internal_state_and_emits_signal(self, qtbot):
        """Verify that calling update_current_metadata correctly modifies the internal metadata state and emits the metadata_updated_signal with the correct data."""
        service = MetadataService()
        initial_metadata = {"title": "Old Title", "artist": "Old Artist"}
        service.current_metadata = initial_metadata.copy() # Set initial state

        field_name = "title"
        new_value = "New Title"

        # Spy on the signal
        metadata_updated_spy = qtbot.waitSignal(service.metadata_updated)

        service.update_current_metadata(field_name, new_value)

        # Verify internal state is updated
        assert service.current_metadata[field_name] == new_value
        assert service.current_metadata == {"title": "New Title", "artist": "Old Artist"}

        # Verify signal was emitted with the correct data
        assert metadata_updated_spy.signal_triggered
        emitted_metadata = metadata_updated_spy.args[0]
        assert emitted_metadata == service.current_metadata

    def test_metadata_service_set_cover_art_updates_internal_state_and_emits_signal(self, qtbot):
        """Verify that calling set_cover_art correctly updates the internal cover art path and emits the cover_art_updated_signal with the correct path."""
        service = MetadataService()
        initial_path = "/path/to/old_cover.jpg"
        service.current_cover_art_path = initial_path # Set initial state

        new_image_path = "/path/to/new_cover.png"

        # Spy on the signal
        cover_art_updated_spy = qtbot.waitSignal(service.metadata_updated)

        service.set_cover_art(new_image_path)

        # Verify internal state is updated
        assert service.current_cover_art_path == new_image_path

        # Verify signal was emitted with the correct data
        assert cover_art_updated_spy.signal_triggered
        emitted_metadata = cover_art_updated_spy.args[0]
        assert emitted_metadata['cover_art_path'] == new_image_path

    def test_metadata_service_signals_are_qt_signals(self):
        """Test that the signals are properly defined Qt signals."""
        service = MetadataService()
        
        # Check that signals exist and are the right type
        assert hasattr(service, 'metadata_loaded')
        assert hasattr(service, 'metadata_updated')
        assert hasattr(service, 'metadata_error')
        
        # Verify they can be connected (basic Qt signal functionality)
        def dummy_slot(data):
            pass
        
        # These should not raise exceptions
        service.metadata_loaded.connect(dummy_slot)
        service.metadata_updated.connect(dummy_slot)
        service.metadata_error.connect(dummy_slot)
    
    def test_metadata_service_inherits_from_qobject(self):
        """Test that MetadataService properly inherits from QObject."""
        service = MetadataService()
        assert isinstance(service, QObject)
    
    def test_metadata_service_initialization(self):
        """Test that MetadataService initializes with correct default values."""
        service = MetadataService()
        
        # Check initial state
        assert service.current_metadata is None
        assert service.current_cover_art_data is None
        assert service.current_cover_art_path is None
        assert hasattr(service, '_metadata')
        assert service._metadata == {}


class TestMetadataServiceDualPath:
    """Test cases for MetadataService with both ABB_NEW_META flag states.
    
    Ensures consistent behavior between unified handler and original functions.
    """
    
    def test_extract_metadata_flag_disabled(self, qtbot):
        """Test extract_metadata with ABB_NEW_META=False (original path)."""
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            
            mock_extract.return_value = mock_metadata
            service = MetadataService()
            
            # Set up signal spy
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            
            result = service.extract_metadata(test_filepath)
            
            # Verify original function was called
            mock_extract.assert_called_once_with(test_filepath)
            
            # Verify results
            assert result == mock_metadata
            assert service._metadata == mock_metadata
            
            # Verify signal emission
            assert metadata_spy.signal_triggered
            assert metadata_spy.args[0] == mock_metadata
    
    def test_extract_metadata_flag_enabled(self, qtbot):
        """Test extract_metadata with ABB_NEW_META=True (unified handler path)."""
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            
            service = MetadataService()
            
            # Mock the unified handler's load_from_file method
            with patch.object(service._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                
                result = service.extract_metadata(test_filepath)
                
                # Verify unified handler was used, not original function
                mock_load.assert_called_once_with(test_filepath)
                mock_extract.assert_not_called()
                
                # Verify results
                assert result == mock_metadata
                assert service._metadata == mock_metadata
                
                # Verify signal emission
                assert metadata_spy.signal_triggered
                assert metadata_spy.args[0] == mock_metadata
    
    def test_extract_metadata_both_paths_produce_identical_results(self, qtbot):
        """Test that both flag states produce identical results for same input."""
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist", "year": "2023"}
        
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_loaded)
            result_disabled = service_disabled.extract_metadata(test_filepath)
        
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_loaded)
                result_enabled = service_enabled.extract_metadata(test_filepath)
        
        # Verify identical results
        assert result_disabled == result_enabled
        assert service_disabled._metadata == service_enabled._metadata
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    
    def test_apply_defaults_flag_disabled(self, qtbot):
        """Test apply_defaults with ABB_NEW_META=False (original path)."""
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            
            mock_apply.return_value = expected_metadata
            service = MetadataService()
            
            # Set up signal spy
            metadata_spy = qtbot.waitSignal(service.metadata_updated)
            
            result = service.apply_defaults(input_metadata)
            
            # Verify original function was called
            mock_apply.assert_called_once_with(input_metadata)
            
            # Verify results
            assert result == expected_metadata
            assert service._metadata == expected_metadata
            
            # Verify signal emission
            assert metadata_spy.signal_triggered
            assert metadata_spy.args[0] == expected_metadata
    
    def test_apply_defaults_flag_enabled(self, qtbot):
        """Test apply_defaults with ABB_NEW_META=True (unified handler path)."""
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            
            service = MetadataService()
            
            # Mock the unified handler methods
            with patch.object(service._unified_handler, 'apply_defaults') as mock_handler_apply, \
                 patch.object(service._unified_handler, 'get_current_metadata') as mock_get_current:
                
                mock_get_current.return_value = expected_metadata
                
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_updated)
                
                result = service.apply_defaults(input_metadata)
                
                # Verify unified handler was used, not original function
                assert service._unified_handler._current_metadata == input_metadata
                mock_handler_apply.assert_called_once()
                mock_get_current.assert_called_once()
                mock_apply.assert_not_called()
                
                # Verify results
                assert result == expected_metadata
                assert service._metadata == expected_metadata
                
                # Verify signal emission
                assert metadata_spy.signal_triggered
                assert metadata_spy.args[0] == expected_metadata
    
    def test_apply_defaults_both_paths_produce_identical_results(self, qtbot):
        """Test that both flag states produce identical results for apply_defaults."""
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            
            mock_apply.return_value = expected_metadata
            service_disabled = MetadataService()
            
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_updated)
            result_disabled = service_disabled.apply_defaults(input_metadata)
        
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            
            with patch.object(service_enabled._unified_handler, 'apply_defaults'), \
                 patch.object(service_enabled._unified_handler, 'get_current_metadata') as mock_get_current:
                
                mock_get_current.return_value = expected_metadata
                
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_updated)
                result_enabled = service_enabled.apply_defaults(input_metadata)
        
        # Verify identical results
        assert result_disabled == result_enabled
        assert service_disabled._metadata == service_enabled._metadata
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    
    def test_extract_and_load_metadata_flag_disabled(self, qtbot):
        """Test extract_and_load_metadata with ABB_NEW_META=False (original path)."""
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            
            service = MetadataService()
            
            # Set up signal spy
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            
            service.extract_and_load_metadata(test_filepath)
            
            # Verify original functions were called
            mock_extract_tags.assert_called_once_with(test_filepath)
            mock_extract_cover.assert_called_once_with(test_filepath)
            
            # Verify internal state
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data == mock_cover_data
            
            # Verify signal emission
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            assert emitted_metadata['title'] == "Test Title"
            assert emitted_metadata['artist'] == "Test Artist"
            assert emitted_metadata['cover_art_data'] == mock_cover_data
    
    def test_extract_and_load_metadata_flag_enabled(self, qtbot):
        """Test extract_and_load_metadata with ABB_NEW_META=True (unified handler path)."""
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            service = MetadataService()
            
            # Mock unified handler methods
            with patch.object(service._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service._unified_handler, 'extract_cover_art') as mock_handler_cover:
                
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                
                service.extract_and_load_metadata(test_filepath)
                
                # Verify unified handler was used, not original functions
                mock_handler_tags.assert_called_once_with(test_filepath)
                mock_handler_cover.assert_called_once_with(test_filepath)
                mock_extract_tags.assert_not_called()
                mock_extract_cover.assert_not_called()
                
                # Verify internal state
                assert service.current_metadata == mock_tags
                assert service.current_cover_art_data == mock_cover_data
                
                # Verify signal emission
                assert metadata_spy.signal_triggered
                emitted_metadata = metadata_spy.args[0]
                assert emitted_metadata['title'] == "Test Title"
                assert emitted_metadata['artist'] == "Test Artist"
                assert emitted_metadata['cover_art_data'] == mock_cover_data
    
    def test_extract_and_load_metadata_both_paths_produce_identical_results(self, qtbot):
        """Test that both flag states produce identical results for extract_and_load_metadata."""
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            
            service_disabled = MetadataService()
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_loaded)
            service_disabled.extract_and_load_metadata(test_filepath)
        
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            
            with patch.object(service_enabled._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service_enabled._unified_handler, 'extract_cover_art') as mock_handler_cover:
                
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_loaded)
                service_enabled.extract_and_load_metadata(test_filepath)
        
        # Verify identical results
        assert service_disabled.current_metadata == service_enabled.current_metadata
        assert service_disabled.current_cover_art_data == service_enabled.current_cover_art_data
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    
    def test_extract_and_load_metadata_none_cover_art_flag_disabled(self, qtbot):
        """Test extract_and_load_metadata handles None cover art with flag disabled."""
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "No Cover"}
        mock_cover_data = None
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            
            service = MetadataService()
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            
            service.extract_and_load_metadata(test_filepath)
            
            # Verify internal state
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data is None
            
            # Verify signal emission excludes cover art data when None
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            assert emitted_metadata['title'] == "No Cover"
            assert 'cover_art_data' not in emitted_metadata
    
    def test_extract_and_load_metadata_none_cover_art_flag_enabled(self, qtbot):
        """Test extract_and_load_metadata handles None cover art with flag enabled."""
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "No Cover"}
        mock_cover_data = None
        
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service = MetadataService()
            
            with patch.object(service._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service._unified_handler, 'extract_cover_art') as mock_handler_cover:
                
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                
                service.extract_and_load_metadata(test_filepath)
                
                # Verify internal state
                assert service.current_metadata == mock_tags
                assert service.current_cover_art_data is None
                
                # Verify signal emission excludes cover art data when None
                assert metadata_spy.signal_triggered
                emitted_metadata = metadata_spy.args[0]
                assert emitted_metadata['title'] == "No Cover"
                assert 'cover_art_data' not in emitted_metadata
    
    def test_unified_handler_integration_is_enabled_detection(self):
        """Test that is_enabled() correctly detects flag state."""
        # Test various flag values
        test_cases = [
            ('true', True),
            ('True', True),
            ('TRUE', True),
            ('false', False),
            ('False', False),
            ('FALSE', False),
            ('', False),
            ('invalid', False),
        ]
        
        for flag_value, expected in test_cases:
            with patch.dict(os.environ, {'ABB_NEW_META': flag_value}):
                from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler
                assert UnifiedMetadataHandler.is_enabled() == expected
    
    def test_signal_emission_consistency_across_paths(self, qtbot):
        """Test that signals are emitted consistently regardless of flag state."""
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title"}
        
        # Track signal emissions for both paths
        signals_disabled = []
        signals_enabled = []
        
        # Test disabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            
            # Connect to all signals
            service_disabled.metadata_loaded.connect(lambda data: signals_disabled.append(('loaded', data)))
            service_disabled.metadata_updated.connect(lambda data: signals_disabled.append(('updated', data)))
            service_disabled.metadata_error.connect(lambda msg: signals_disabled.append(('error', msg)))
            
            service_disabled.extract_metadata(test_filepath)
        
        # Test enabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                
                # Connect to all signals
                service_enabled.metadata_loaded.connect(lambda data: signals_enabled.append(('loaded', data)))
                service_enabled.metadata_updated.connect(lambda data: signals_enabled.append(('updated', data)))
                service_enabled.metadata_error.connect(lambda msg: signals_enabled.append(('error', msg)))
                
                service_enabled.extract_metadata(test_filepath)
        
        # Verify signal patterns are identical
        assert len(signals_disabled) == len(signals_enabled)
        assert len(signals_disabled) == 1  # Should have one 'loaded' signal
        assert signals_disabled[0][0] == 'loaded'
        assert signals_enabled[0][0] == 'loaded'
        assert signals_disabled[0][1] == signals_enabled[0][1]
    
    def test_state_management_consistency_across_paths(self):
        """Test that internal state management is consistent between paths."""
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        
        # Test disabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            service_disabled.extract_metadata(test_filepath)
            
            disabled_state = {
                '_metadata': service_disabled._metadata,
                'get_metadata': service_disabled.get_metadata()
            }
        
        # Test enabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                service_enabled.extract_metadata(test_filepath)
                
                enabled_state = {
                    '_metadata': service_enabled._metadata,
                    'get_metadata': service_enabled.get_metadata()
                }
        
        # Verify state consistency
        assert disabled_state['_metadata'] == enabled_state['_metadata']
        assert disabled_state['get_metadata'] == enabled_state['get_metadata']