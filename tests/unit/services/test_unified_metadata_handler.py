"""Tests for UnifiedMetadataHandler.

Tests the facade pattern implementation that wraps existing metadata functions
while providing backward compatibility and new unified interface.
"""
import os
import pytest
from unittest.mock import patch, MagicMock
from pathlib import Path

from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler, create_metadata_handler


class TestUnifiedMetadataHandler:
    """Test the UnifiedMetadataHandler facade."""
    
    def setup_method(self):
        """Set up test instance."""
        self.handler = UnifiedMetadataHandler()
        
    def test_initialization(self):
        """Test handler initializes with empty state."""
        assert self.handler._current_metadata == {}
        assert self.handler._current_file_path is None
        assert not self.handler.has_metadata()
        
    def test_factory_function(self):
        """Test the convenience factory function."""
        handler = create_metadata_handler()
        assert isinstance(handler, UnifiedMetadataHandler)
        assert handler._current_metadata == {}
        
    @patch('src.abb.services.unified_metadata_handler.extract_metadata')
    def test_load_from_file_wraps_extract_metadata(self, mock_extract):
        """Test load_from_file properly wraps extract_metadata."""
        # Arrange
        test_file_path = "/test/path/audio.mp3"
        expected_metadata = {
            "title": "Test Title",
            "artist": "Test Artist",
            "album": "Test Album",
            "narrator": "Test Narrator",
            "year": "2023",
            "genre": "Test Genre",
            "series": "Test Series",
            "series_position": "1",
            "series_sort": "Test Series",
            "description": "Test Description",
            "cover_art": None
        }
        mock_extract.return_value = expected_metadata
        
        # Act
        result = self.handler.load_from_file(test_file_path)
        
        # Assert
        mock_extract.assert_called_once_with(test_file_path)
        assert result == expected_metadata
        assert self.handler._current_metadata == expected_metadata
        assert self.handler._current_file_path == test_file_path
        assert self.handler.has_metadata()
        
    @patch('src.abb.services.unified_metadata_handler.extract_tags')
    def test_extract_tags_only_wraps_extract_tags(self, mock_extract_tags):
        """Test extract_tags_only properly wraps extract_tags."""
        # Arrange
        test_file_path = "/test/path/audio.mp3"
        expected_tags = {
            "title": "Test Title",
            "artist": "Test Artist", 
            "album": "Test Album",
            "genre": "Test Genre",
            "track_number": "1",
            "year": "2023"
        }
        mock_extract_tags.return_value = expected_tags
        
        # Act
        result = self.handler.extract_tags_only(test_file_path)
        
        # Assert
        mock_extract_tags.assert_called_once_with(test_file_path)
        assert result == expected_tags
        
    @patch('src.abb.services.unified_metadata_handler.extract_cover')
    def test_extract_cover_art_wraps_extract_cover(self, mock_extract_cover):
        """Test extract_cover_art properly wraps extract_cover."""
        # Arrange
        test_file_path = "/test/path/audio.mp3"
        expected_cover_data = b"fake_image_data"
        mock_extract_cover.return_value = expected_cover_data
        
        # Act
        result = self.handler.extract_cover_art(test_file_path)
        
        # Assert
        mock_extract_cover.assert_called_once_with(test_file_path)
        assert result == expected_cover_data
        
    def test_update_field_modifies_current_metadata(self):
        """Test update_field modifies the current metadata state."""
        # Arrange
        self.handler._current_metadata = {"title": "Old Title", "artist": "Old Artist"}
        
        # Act
        self.handler.update_field("title", "New Title")
        self.handler.update_field("genre", "New Genre")
        
        # Assert
        assert self.handler._current_metadata["title"] == "New Title"
        assert self.handler._current_metadata["artist"] == "Old Artist"  # unchanged
        assert self.handler._current_metadata["genre"] == "New Genre"   # new field
        
    def test_get_current_metadata_returns_copy(self):
        """Test get_current_metadata returns a copy of the metadata."""
        # Arrange
        original_metadata = {"title": "Test Title", "artist": "Test Artist"}
        self.handler._current_metadata = original_metadata
        
        # Act
        result = self.handler.get_current_metadata()
        result["title"] = "Modified Title"  # Modify the returned copy
        
        # Assert
        assert result != self.handler._current_metadata  # They should be different
        assert self.handler._current_metadata["title"] == "Test Title"  # Original unchanged
        
    def test_get_for_ffmpeg_uses_command_builder_mapping(self):
        """Test get_for_ffmpeg uses ABB_TO_FFMPEG_METADATA_MAP_GENERAL."""
        # Arrange
        self.handler._current_metadata = {
            "title": "Test Title",
            "album": "Test Album", 
            "year": "2023",
            "genre": "Fiction",
            "narrator": "Test Narrator",
            "series": "Test Series",
            "description": "Test Description"
        }
        
        # Act
        result = self.handler.get_for_ffmpeg()
        
        # Assert - Check that ABB fields are mapped to FFmpeg fields
        assert result["title"] == "Test Title"        # title -> title
        assert result["album"] == "Test Album"        # album -> album
        assert result["date"] == "2023"               # year -> date
        assert result["genre"] == "Fiction"           # genre -> genre
        assert result["composer"] == "Test Narrator"  # narrator -> composer
        assert result["mood"] == "Test Series"        # series -> mood
        assert result["comment"] == "Test Description" # description -> comment (via special logic)
        
    def test_get_for_ffmpeg_handles_artist_special_case(self):
        """Test get_for_ffmpeg handles artist/album_artist special mapping."""
        # Arrange
        self.handler._current_metadata = {"artist": "Test Artist"}
        
        # Act
        result = self.handler.get_for_ffmpeg()
        
        # Assert
        assert result["artist"] == "Test Artist"
        assert result["album_artist"] == "Test Artist"  # Should be copied from artist
        
    def test_get_for_ffmpeg_comment_description_priority(self):
        """Test get_for_ffmpeg prioritizes comment over description."""
        # Arrange - both comment and description present
        self.handler._current_metadata = {
            "comment": "Test Comment",
            "description": "Test Description"
        }
        
        # Act
        result = self.handler.get_for_ffmpeg()
        
        # Assert - comment should take precedence
        assert result["comment"] == "Test Comment"
        
        # Test with only description
        self.handler._current_metadata = {"description": "Test Description"}
        result = self.handler.get_for_ffmpeg()
        assert result["comment"] == "Test Description"
        
    def test_get_for_ffmpeg_series_sort_priority(self):
        """Test get_for_ffmpeg prioritizes series_sort over sort_album."""
        # Arrange - both series_sort and sort_album present  
        self.handler._current_metadata = {
            "series_sort": "Test Series Sort",
            "sort_album": "Test Album Sort"
        }
        
        # Act
        result = self.handler.get_for_ffmpeg()
        
        # Assert - series_sort should take precedence for album_sort
        assert result["album_sort"] == "Test Series Sort"
        
        # Test with only sort_album
        self.handler._current_metadata = {"sort_album": "Test Album Sort"}
        result = self.handler.get_for_ffmpeg()
        assert result["album_sort"] == "Test Album Sort"
        
    def test_get_for_ffmpeg_filters_empty_values(self):
        """Test get_for_ffmpeg filters out empty and None values."""
        # Arrange
        self.handler._current_metadata = {
            "title": "Valid Title",
            "album": "",           # Empty string
            "year": None,          # None value
            "genre": "  ",         # Whitespace only
            "artist": "Valid Artist"
        }
        
        # Act
        result = self.handler.get_for_ffmpeg()
        
        # Assert
        assert "title" in result
        assert "artist" in result
        assert "album_artist" in result  # From artist
        assert "album" not in result     # Empty string filtered
        assert "date" not in result      # None value filtered
        assert "genre" not in result     # Whitespace filtered
        
    @patch('src.abb.services.unified_metadata_handler.apply_metadata_defaults')
    def test_apply_defaults_wraps_function(self, mock_apply_defaults):
        """Test apply_defaults wraps apply_metadata_defaults."""
        # Arrange
        original_metadata = {"title": "Test Title"}
        modified_metadata = {"title": "Test Title", "album": "Test Title"}  # With defaults
        self.handler._current_metadata = original_metadata
        mock_apply_defaults.return_value = modified_metadata
        
        # Act
        self.handler.apply_defaults()
        
        # Assert
        mock_apply_defaults.assert_called_once_with(original_metadata)
        assert self.handler._current_metadata == modified_metadata
        
    @patch('src.abb.services.unified_metadata_handler.clear_metadata_cache')
    def test_clear_cache_wraps_function(self, mock_clear_cache):
        """Test clear_cache wraps clear_metadata_cache."""
        # Act
        self.handler.clear_cache()
        
        # Assert
        mock_clear_cache.assert_called_once()
        
    def test_reset_state_clears_internal_state(self):
        """Test reset_state clears internal state."""
        # Arrange
        self.handler._current_metadata = {"title": "Test"}
        self.handler._current_file_path = "/test/path"
        
        # Act
        self.handler.reset_state()
        
        # Assert
        assert self.handler._current_metadata == {}
        assert self.handler._current_file_path is None
        assert not self.handler.has_metadata()
        
    def test_get_current_file_path(self):
        """Test get_current_file_path returns current file path."""
        # Test with no file loaded
        assert self.handler.get_current_file_path() is None
        
        # Test with file loaded
        test_path = "/test/path/audio.mp3"
        self.handler._current_file_path = test_path
        assert self.handler.get_current_file_path() == test_path
        
    def test_has_metadata(self):
        """Test has_metadata correctly reports metadata presence."""
        # Test with no metadata
        assert not self.handler.has_metadata()
        
        # Test with metadata
        self.handler._current_metadata = {"title": "Test"}
        assert self.handler.has_metadata()
        
    def test_get_field(self):
        """Test get_field retrieves specific field values."""
        # Arrange
        self.handler._current_metadata = {"title": "Test Title", "artist": "Test Artist"}
        
        # Act & Assert
        assert self.handler.get_field("title") == "Test Title"
        assert self.handler.get_field("artist") == "Test Artist"
        assert self.handler.get_field("nonexistent") is None
        assert self.handler.get_field("nonexistent", "default") == "default"
        
    def test_is_enabled_environment_variable(self):
        """Test is_enabled reads ABB_NEW_META environment variable."""
        # Test default (False)
        assert not UnifiedMetadataHandler.is_enabled()
        
        # Test True values
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            assert UnifiedMetadataHandler.is_enabled()
            
        with patch.dict(os.environ, {'ABB_NEW_META': 'True'}):
            assert UnifiedMetadataHandler.is_enabled()
            
        with patch.dict(os.environ, {'ABB_NEW_META': 'TRUE'}):
            assert UnifiedMetadataHandler.is_enabled()
            
        # Test False values
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}):
            assert not UnifiedMetadataHandler.is_enabled()
            
        with patch.dict(os.environ, {'ABB_NEW_META': 'False'}):
            assert not UnifiedMetadataHandler.is_enabled()
            
        with patch.dict(os.environ, {'ABB_NEW_META': 'other'}):
            assert not UnifiedMetadataHandler.is_enabled()


class TestUnifiedMetadataHandlerIntegration:
    """Integration tests using real files from golden data."""
    
    @pytest.fixture
    def golden_files_path(self):
        """Get path to golden test files."""
        return Path(__file__).parent.parent.parent / "data" / "golden"
        
    @pytest.fixture
    def handler(self):
        """Create a fresh handler for each test."""
        return UnifiedMetadataHandler()
        
    def test_load_real_mp3_file(self, handler, golden_files_path):
        """Test loading metadata from real golden MP3 file."""
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
            
        # Act
        metadata = handler.load_from_file(str(mp3_file))
        
        # Assert - Check that metadata is extracted (exact values depend on file)
        assert isinstance(metadata, dict)
        assert handler.has_metadata()
        assert handler.get_current_file_path() == str(mp3_file)
        
        # Check expected structure
        expected_keys = [
            "title", "artist", "album", "narrator", "year", "genre",
            "series", "series_position", "series_sort", "description", "cover_art"
        ]
        for key in expected_keys:
            assert key in metadata
            
    def test_load_real_m4b_file(self, handler, golden_files_path):
        """Test loading metadata from real golden M4B file."""
        m4b_file = golden_files_path / "golden.m4b"
        if not m4b_file.exists():
            pytest.skip("Golden M4B file not found")
            
        # Act
        metadata = handler.load_from_file(str(m4b_file))
        
        # Assert - Check that metadata is extracted
        assert isinstance(metadata, dict)
        assert handler.has_metadata()
        
        # Check expected structure
        expected_keys = [
            "title", "artist", "album", "narrator", "year", "genre",
            "series", "series_position", "series_sort", "description", "cover_art"
        ]
        for key in expected_keys:
            assert key in metadata
            
    def test_extract_tags_real_files(self, handler, golden_files_path):
        """Test extract_tags_only with real files."""
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
            
        # Act
        tags = handler.extract_tags_only(str(mp3_file))
        
        # Assert
        assert isinstance(tags, dict)
        expected_keys = ["title", "artist", "album", "genre", "track_number", "year"]
        for key in expected_keys:
            assert key in tags
            
    def test_extract_cover_real_files(self, handler, golden_files_path):
        """Test extract_cover_art with real files."""
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
            
        # Act
        cover_data = handler.extract_cover_art(str(mp3_file))
        
        # Assert - May or may not have cover art, but should not error
        assert cover_data is None or isinstance(cover_data, bytes)
        
    def test_compatibility_with_existing_extract_metadata(self, handler, golden_files_path):
        """Test that handler results match original extract_metadata function."""
        from src.abb.metadata_utils import extract_metadata
        
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
            
        # Act
        handler_result = handler.load_from_file(str(mp3_file))
        direct_result = extract_metadata(str(mp3_file))
        
        # Assert - Results should be identical
        assert handler_result == direct_result
        
    def test_ffmpeg_format_conversion(self, handler, golden_files_path):
        """Test that get_for_ffmpeg produces valid FFmpeg metadata."""
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
            
        # Act
        handler.load_from_file(str(mp3_file))
        ffmpeg_metadata = handler.get_for_ffmpeg()
        
        # Assert
        assert isinstance(ffmpeg_metadata, dict)
        
        # All values should be strings (required for FFmpeg)
        for key, value in ffmpeg_metadata.items():
            assert isinstance(key, str)
            assert isinstance(value, str)
            assert value.strip() != ""  # No empty values
            
        # Should contain basic FFmpeg fields if metadata exists
        if handler.get_field("title"):
            assert "title" in ffmpeg_metadata
        if handler.get_field("artist"):
            assert "artist" in ffmpeg_metadata
            assert "album_artist" in ffmpeg_metadata  # Should be copied