import pytest
from unittest.mock import patch
from src.abb.services.file_service import FileService

class TestFileServiceCombinedSize:
    def test_get_combined_size_sums_file_sizes(self, tmp_path):
        # Create files
        file1 = tmp_path / "a.mp3"
        file1.write_bytes(b"x" * 100)
        file2 = tmp_path / "b.mp3"
        file2.write_bytes(b"x" * 200)
        service = FileService()
        service.add_files([str(file1), str(file2)])
        with patch("os.path.getsize") as mock_getsize:
            mock_getsize.side_effect = [100, 200]
            total = service.get_combined_size()
        assert total == 300

    def test_combined_size_signal_emitted_on_add_remove(self, tmp_path, qtbot):
        file1 = tmp_path / "a.mp3"
        file1.write_bytes(b"x" * 100)
        file2 = tmp_path / "b.mp3"
        file2.write_bytes(b"x" * 200)
        service = FileService()
        with qtbot.waitSignal(service.combined_size_changed_signal, timeout=500):
            service.add_files([str(file1), str(file2)])
        # Remove a file and check signal
        with qtbot.waitSignal(service.combined_size_changed_signal, timeout=500):
            service.remove_file(0) 