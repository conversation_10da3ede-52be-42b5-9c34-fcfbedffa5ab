"""Tests for UI progress display integration (Task 1.7.5)."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtWidgets import QApplication
from src.abb.main_window import MainWindow
from src.abb.ui.widgets.right_panel_widget import RightPanelWidget


class TestProgressIntegration:
    """Test progress display integration between MainWindow and RightPanelWidget."""
    
    @pytest.fixture
    def qt_app(self, qapp):
        """Qt application fixture."""
        return qapp
    
    @pytest.fixture
    def mock_controller(self):
        """Create a mock controller with all required signals."""
        controller = Mock()
        
        # Create mock signals that behave like Qt signals
        controller.file_list_updated_signal = Mock()
        controller.metadata_updated_signal = Mock()
        controller.cover_art_updated_signal = Mock()
        controller.status_message_updated_signal = Mock()
        controller.processing_started_signal = Mock()
        controller.processing_progress_signal = Mock()
        controller.processing_finished_signal = Mock()
        controller.error_occurred_signal = Mock()
        controller.setting_changed = Mock()
        controller.selected_file_data_changed = Mock()
        controller.selected_file_properties_updated_signal = Mock()
        controller.combined_size_updated_signal = Mock()
        
        # Add connect method to all signals
        for attr_name in dir(controller):
            attr = getattr(controller, attr_name)
            if hasattr(attr, 'connect'):
                attr.connect = Mock()
        
        # Mock controller methods
        controller.get_files = Mock(return_value=[])
        controller.get_metadata = Mock(return_value={})
        controller.get_setting = Mock(return_value=None)
        controller.get_output_path = Mock(return_value="/test/output")
        controller.get_output_filename = Mock(return_value="test.m4b")
        controller.start_processing = Mock()
        controller.cancel_processing = Mock()
        
        return controller
    
    @pytest.fixture
    def main_window(self, qt_app, mock_controller):
        """Create MainWindow with mocked controller."""
        with patch('src.abb.main_window.MainController', return_value=mock_controller), \
             patch('src.abb.main_window.SettingsManager'):
            window = MainWindow()
            window._controller = mock_controller
            return window
    
    def test_progress_signal_connections_exist(self, main_window, mock_controller):
        """Test that progress signals are connected to UI update methods."""
        # Verify controller signal connections were made
        mock_controller.processing_started_signal.connect.assert_called()
        mock_controller.processing_progress_signal.connect.assert_called()
        mock_controller.processing_finished_signal.connect.assert_called()
        mock_controller.error_occurred_signal.connect.assert_called()
    
    def test_start_processing_calls_controller_method(self, main_window, mock_controller):
        """Test that start_processing calls the new controller method."""
        # Call start_processing
        main_window.start_processing()
        
        # Verify controller start_processing was called
        mock_controller.start_processing.assert_called_once()
    
    def test_progress_update_delegates_to_right_panel(self, main_window):
        """Test that _update_progress updates the right panel widget."""
        # Mock the right panel widget
        main_window.right_panel_widget = Mock()
        
        # Call _update_progress
        main_window._update_progress(75)
        
        # Verify right panel update_progress was called
        main_window.right_panel_widget.update_progress.assert_called_once_with(75)
    
    def test_processing_start_sets_ui_state(self, main_window):
        """Test that processing start signal sets correct UI state."""
        # Mock the right panel widget
        main_window.right_panel_widget = Mock()
        main_window.left_panel_widget = Mock()
        
        # Simulate processing start
        main_window._set_ui_for_processing_start(True)
        
        # Verify UI state changes
        main_window.right_panel_widget.set_processing_state.assert_called_once_with(True)
        main_window.left_panel_widget.setEnabled.assert_called_once_with(False)
    
    def test_processing_done_resets_progress(self, main_window):
        """Test that processing completion resets progress bar."""
        # Mock the right panel widget
        main_window.right_panel_widget = Mock()
        main_window.left_panel_widget = Mock()
        
        # Simulate processing completion
        main_window._processing_done("/test/output.m4b")
        
        # Verify progress reset and UI state changes
        main_window.right_panel_widget.reset_progress.assert_called_once()
        main_window.right_panel_widget.set_processing_state.assert_called_once_with(False)
    
    def test_right_panel_progress_methods_exist(self):
        """Test that RightPanelWidget has required progress methods."""
        # Mock controller for RightPanelWidget
        mock_controller = Mock()
        
        # Create RightPanelWidget
        widget = RightPanelWidget(mock_controller)
        
        # Verify required methods exist
        assert hasattr(widget, 'update_progress')
        assert hasattr(widget, 'reset_progress')
        assert hasattr(widget, 'set_processing_state')
        assert hasattr(widget, 'progress_bar')
    
    def test_right_panel_update_progress_shows_bar(self):
        """Test that update_progress makes progress bar visible."""
        # Mock controller
        mock_controller = Mock()
        
        # Create RightPanelWidget and show it for proper Qt widget hierarchy
        widget = RightPanelWidget(mock_controller)
        widget.show()
        
        # Initially progress bar should be hidden
        assert not widget.progress_bar.isVisible()
        
        # Update progress
        widget.update_progress(50)
        
        # Progress bar should now be visible with correct value
        assert widget.progress_bar.isVisible()
        assert widget.progress_bar.value() == 50
    
    def test_right_panel_reset_progress_hides_bar(self):
        """Test that reset_progress hides progress bar."""
        # Mock controller
        mock_controller = Mock()
        
        # Create RightPanelWidget and show it
        widget = RightPanelWidget(mock_controller)
        widget.show()
        
        # Set progress bar visible and with value
        widget.progress_bar.setVisible(True)
        widget.progress_bar.setValue(75)
        
        # Reset progress
        widget.reset_progress()
        
        # Progress bar should be hidden and reset
        assert not widget.progress_bar.isVisible()
        assert widget.progress_bar.value() == 0
    
    def test_right_panel_processing_state_toggles_buttons(self):
        """Test that set_processing_state toggles button states correctly."""
        # Mock controller
        mock_controller = Mock()
        
        # Create RightPanelWidget and show it
        widget = RightPanelWidget(mock_controller)
        widget.show()
        
        # Test processing state True
        widget.set_processing_state(True)
        assert not widget.process_button.isEnabled()
        assert widget.cancel_button.isEnabled()
        
        # Test processing state False
        widget.set_processing_state(False)
        assert widget.process_button.isEnabled()
        assert not widget.cancel_button.isEnabled()