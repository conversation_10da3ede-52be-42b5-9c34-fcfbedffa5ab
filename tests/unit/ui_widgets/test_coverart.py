from unittest.mock import <PERSON>Mock, patch

import pytest
from PySide6.QtCore import <PERSON><PERSON>uff<PERSON>, QByteArray, QIODevice, Qt  # Import necessary Qt classes
from PySide6.QtGui import QImage, QPixmap

from src.abb.ui.widgets.coverart import CoverArtWidget


class TestCoverArtWidget:
    """Test CoverArtWidget functionality"""

    @pytest.fixture
    def widget(self, qtbot):
        """Create a CoverArtWidget instance for testing"""
        widget = CoverArtWidget()
        qtbot.addWidget(widget)
        return widget

    def test_cover_art_widget_displays_image_on_slot_call(self, widget):
        """Test that update_cover_art_display displays image when given valid bytes data"""
        # Create a real QPixmap from a dummy image data
        # Create a 1x1 red QImage
        image = QImage(1, 1, QImage.Format.Format_ARGB32)
        image.fill(Qt.GlobalColor.red)
        
        # Convert QImage to bytes (PNG format)
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.OpenModeFlag.WriteOnly)
        image.save(buffer, "PNG")
        png_data = byte_array.data()
        
        # Call the slot with the generated PNG data
        widget.update_cover_art_display(png_data)

        # Verify that current_pixmap is set (not None)
        assert widget.current_pixmap is not None
        assert not widget.current_pixmap.isNull()

        # Verify that image_label has a pixmap
        label_pixmap = widget.image_label.pixmap()
        assert label_pixmap is not None
        assert not label_pixmap.isNull()

        # Verify the text is cleared
        assert widget.image_label.text() == ""

    def test_cover_art_widget_clears_on_none(self, widget):
        """Test that update_cover_art_display clears display when given None"""
        # Mock clear_cover_art
        widget.clear_cover_art = MagicMock()

        # Call the slot with None
        widget.update_cover_art_display(None)

        # Verify clear_cover_art was called
        widget.clear_cover_art.assert_called_once()

    def test_update_cover_art_display_image_too_large(self, widget):
        """Test that update_cover_art_display handles image data exceeding the size limit."""
        # Create a dummy image data that exceeds the 50MB limit
        # The actual limit is 50 * 1024 * 1024 bytes.
        # We'll create data slightly larger than that.
        large_image_data = b"a" * (50 * 1024 * 1024 + 1)

        # Mock clear_cover_art and set_cover_art_from_pixmap to ensure they are called/not called
        widget.clear_cover_art = MagicMock()
        widget.set_cover_art_from_pixmap = MagicMock()

        # Call the slot with the large image data
        widget.update_cover_art_display(large_image_data)

        # Verify clear_cover_art was called
        widget.clear_cover_art.assert_called_once()

        # Verify set_cover_art_from_pixmap was NOT called
        widget.set_cover_art_from_pixmap.assert_not_called()

        # Verify the error message was set
        assert widget.image_label.text() == "Image too large (max 50MB)"

    def test_cover_art_widget_handles_invalid_image_data(self, widget):
        """Test that update_cover_art_display handles invalid image data gracefully"""
        # Mock clear_cover_art
        widget.clear_cover_art = MagicMock()

        # Test with actual invalid image data that will fail to load
        test_image_data = b"invalid_image_data"

        # Call the slot - this should fail to load as it's not valid image data
        widget.update_cover_art_display(test_image_data)

        # Verify clear_cover_art was called due to failure
        widget.clear_cover_art.assert_called_once()

        # Verify error message was set
        assert widget.image_label.text() == "Invalid image data"

    def test_cover_art_widget_clear_functionality(self, widget):
        """Test the clear_cover_art method"""
        # Set some initial state
        widget.current_pixmap = QPixmap(100, 100)  # Non-null pixmap
        widget.image_label.setText("")  # No text

        # Call clear_cover_art
        widget.clear_cover_art()

        # Verify state is cleared
        assert widget.current_pixmap is None
        assert widget.image_label.text() == "Drag Image Here"
        assert widget.image_label.pixmap() is None or widget.image_label.pixmap().isNull()

    @pytest.fixture
    def dummy_image_path(self, tmp_path):
        """Create a dummy image file for testing."""
        dummy_file = tmp_path / "test_image.png"
        # Create a 1x1 red QImage
        image = QImage(1, 1, QImage.Format.Format_ARGB32)
        image.fill(Qt.GlobalColor.red)

        # Convert QImage to bytes (PNG format)
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.OpenModeFlag.WriteOnly)
        image.save(buffer, "PNG")
        png_data = byte_array.data()

        with open(dummy_file, "wb") as f:
            f.write(png_data)
        return str(dummy_file)

    def test_cover_art_drop_emits_cover_art_changed_signal(self, widget, qtbot, dummy_image_path):
        """Verify that dropping an image file emits the cover_art_path_changed signal
        with the correct image path.
        """
        from PySide6.QtCore import QMimeData, QUrl
        from PySide6.QtGui import QDropEvent

        mime_data = QMimeData()
        mime_data.setUrls([QUrl.fromLocalFile(dummy_image_path)])

        # Create a mock QDropEvent
        # The position and dropAction are not critical for this test, but required by constructor
        drop_event = QDropEvent(
            widget.pos(),
            Qt.DropAction.CopyAction,
            mime_data,
            Qt.MouseButton.NoButton,
            Qt.KeyboardModifier.NoModifier,
        )

        with qtbot.waitSignal(widget.cover_art_path_changed) as blocker:
            widget.dropEvent(drop_event)

        assert blocker.args == [dummy_image_path]

    def test_cover_art_button_select_emits_cover_art_changed_signal(self, widget, qtbot, dummy_image_path):
        """Verify that selecting an image via the file dialog (mocked) emits the
        cover_art_path_changed signal with the correct image path.
        """
        with patch("src.abb.ui.widgets.coverart.QFileDialog.getOpenFileName",
                   return_value=(dummy_image_path, "PNG Files (*.png)")):
            with qtbot.waitSignal(widget.cover_art_path_changed) as blocker:
                widget.load_button.click()

            assert blocker.args == [dummy_image_path]

    # The previous test_cover_art_widget_integration_with_real_pixmap is now covered
    # by test_cover_art_widget_displays_image_on_slot_call, which uses real QPixmap behavior.
    # No need for a separate integration test with the same purpose.

