from unittest.mock import Mock

import pytest
from PySide6.QtCore import Q<PERSON>ime<PERSON><PERSON>, QPoint, Qt  # QMimeData is in QtCore
from PySide6.QtGui import QDropEvent  # QDropEvent is in QtGui
from PySide6.QtWidgets import QListWidgetItem

from src.abb.ui.widgets.left_panel_widget import (  # Import DraggableListWidget
    LeftPanelWidget,
)


@pytest.fixture
def left_panel_widget(qtbot):
    widget = LeftPanelWidget()
    qtbot.addWidget(widget)
    return widget


def test_list_widget_allows_internal_drag_drop_and_emits_reorder_signal(left_panel_widget, qtbot):
    # Verify that the DraggableListWidget is configured to allow internal drag-and-drop movements
    list_widget = left_panel_widget.file_list_widget
    
    # Check drag-and-drop configuration
    from PySide6.QtWidgets import QListWidget
    assert list_widget.dragDropMode() == QListWidget.InternalMove
    assert list_widget.defaultDropAction() == Qt.MoveAction
    assert list_widget.dragEnabled() is True
    assert list_widget.acceptDrops() is True
    
    # Add items to the list widget
    item1 = QListWidgetItem("file1.mp3")
    item2 = QListWidgetItem("file2.mp3")
    item3 = QListWidgetItem("file3.mp3")
    list_widget.addItem(item1)
    list_widget.addItem(item2)
    list_widget.addItem(item3)

    assert list_widget.count() == 3
    assert list_widget.item(0).text() == "file1.mp3"
    assert list_widget.item(1).text() == "file2.mp3"
    assert list_widget.item(2).text() == "file3.mp3"

    # Mock the signal
    mock_signal = Mock()
    left_panel_widget.files_reordered_signal.connect(mock_signal)

    # Simulate drag and drop: move item1 (index 0) to position of item3 (index 2)
    # This should result in order: file2, file3, file1
    # Manually reorder items to simulate the result of a drag-and-drop
    # Move item1 (index 0) to the end (index 2)
    item_to_move = list_widget.takeItem(0)
    list_widget.insertItem(2, item_to_move)

    # Call the method that emits the signal after reordering
    left_panel_widget._handle_files_reordered()

    # Verify the order of items in the list widget (already reordered manually)
    assert list_widget.count() == 3
    assert list_widget.item(0).text() == "file2.mp3"
    assert list_widget.item(1).text() == "file3.mp3"
    assert list_widget.item(2).text() == "file1.mp3"

    # Verify the signal was emitted with the correct new order
    mock_signal.assert_called_once_with(["file2.mp3", "file3.mp3", "file1.mp3"])
