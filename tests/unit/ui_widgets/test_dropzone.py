import pytest
from PySide6.QtCore import Qt, QUrl, QMimeData, QPoint
from PySide6.QtGui import QDragEnterEvent, QDropEvent
from PySide6.QtTest import QSignalSpy

from src.abb.ui.widgets.dropzone import DropZone


class TestDropZone:
    """Unit tests for DropZone widget"""
    
    def test_accepts_supported_files(self, qtbot):
        """DropZone accepts drag events with supported files"""
        widget = DropZone()
        qtbot.addWidget(widget)
        
        # Create drag event with mp3 file
        mime = QMimeData()
        mime.setUrls([QUrl.fromLocalFile("/test/file.mp3")])
        
        event = QDragEnterEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        
        widget.dragEnterEvent(event)
        assert event.isAccepted()
    
    def test_rejects_unsupported_files(self, qtbot):
        """DropZone rejects non-audio files"""
        widget = DropZone()
        qtbot.addWidget(widget)
        
        # Create drag event with image file
        mime = QMimeData()
        mime.setUrls([QUrl.fromLocalFile("/test/image.jpg")])
        
        event = QDragEnterEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        
        widget.dragEnterEvent(event)
        assert not event.isAccepted()
    
    def test_emits_files_dropped_signal(self, qtbot):
        """DropZone emits signal with file paths on drop"""
        widget = DropZone()
        qtbot.addWidget(widget)
        
        # Set up signal spy
        signal_spy = QSignalSpy(widget.filesDropped)
        
        # Create drop event
        mime = QMimeData()
        mime.setUrls([
            QUrl.fromLocalFile("/test/file1.mp3"),
            QUrl.fromLocalFile("/test/file2.m4a")
        ])
        
        event = QDropEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        
        widget.dropEvent(event)
        
        # Check signal was emitted with correct paths
        assert signal_spy.count() == 1
        emitted_files = signal_spy.at(0)[0]
        assert len(emitted_files) == 2
