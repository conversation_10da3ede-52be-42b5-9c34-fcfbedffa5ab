import pytest
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.abb.ui.widgets.metadata_form_widget import MetadataFormWidget


class TestMetadataFormWidget:
    """Test MetadataFormWidget functionality"""
    
    @pytest.fixture
    def widget(self, qtbot):
        """Create a MetadataFormWidget instance for testing"""
        widget = MetadataFormWidget()
        qtbot.addWidget(widget)
        return widget
    
    def test_metadata_form_populates_fields_on_slot_call(self, widget):
        """Test that update_metadata_display populates all fields correctly"""
        # Test with all tags present
        metadata_dict = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'album': 'Test Album',
            'genre': 'Test Genre',
            'track': '5',
            'date': '2023'
        }
        
        # Call the slot
        widget.update_metadata_display(metadata_dict)
        
        # Assert all fields are populated correctly
        assert widget.title_edit.text() == 'Test Title'
        assert widget.artist_edit.text() == 'Test Artist'
        assert widget.album_edit.text() == 'Test Album'
        assert widget.genre_edit.text() == 'Test Genre'
        assert widget.track_number_edit.text() == '5'
        assert widget.year_edit.text() == '2023'
        
        # Also check that author field is populated with artist value
        assert widget.author_edit.text() == 'Test Artist'
    
    def test_metadata_form_handles_missing_tags_gracefully(self, widget):
        """Test that update_metadata_display handles missing tags by setting empty strings"""
        # Test with some tags missing
        metadata_dict = {
            'title': 'Only Title',
            'genre': 'Only Genre'
        }
        
        # Call the slot
        widget.update_metadata_display(metadata_dict)
        
        # Assert populated fields have correct values
        assert widget.title_edit.text() == 'Only Title'
        assert widget.genre_edit.text() == 'Only Genre'
        
        # Assert missing fields are empty strings
        assert widget.artist_edit.text() == ''
        assert widget.album_edit.text() == ''
        assert widget.track_number_edit.text() == ''
        assert widget.year_edit.text() == ''
        assert widget.author_edit.text() == ''
    
    def test_metadata_form_handles_empty_dict(self, widget):
        """Test that update_metadata_display handles empty dictionary"""
        # First populate with some data
        widget.title_edit.setText('Previous Title')
        widget.artist_edit.setText('Previous Artist')
        
        # Call the slot with empty dict
        widget.update_metadata_display({})
        
        # Assert all fields are cleared
        assert widget.title_edit.text() == ''
        assert widget.artist_edit.text() == ''
        assert widget.album_edit.text() == ''
        assert widget.genre_edit.text() == ''
        assert widget.track_number_edit.text() == ''
        assert widget.year_edit.text() == ''
        assert widget.author_edit.text() == ''
    
    def test_metadata_form_preserves_other_fields(self, widget):
        """Test that fields not in metadata_dict are preserved"""
        # Set some initial values in fields that won't be updated
        widget.narrator_edit.setText('Existing Narrator')
        widget.series_edit.setText('Existing Series')
        widget.description_edit.setText('Existing Description')
        
        # Update with metadata that doesn't include these fields
        metadata_dict = {
            'title': 'New Title',
            'artist': 'New Artist'
        }
        
        widget.update_metadata_display(metadata_dict)
        
        # Assert updated fields changed
        assert widget.title_edit.text() == 'New Title'
        assert widget.artist_edit.text() == 'New Artist'
        
        # Assert other fields remain unchanged
        assert widget.narrator_edit.text() == 'Existing Narrator'
        assert widget.series_edit.text() == 'Existing Series'
        assert widget.description_edit.text() == 'Existing Description'

    def test_field_edit_emits_metadata_field_changed_signal(self, widget, qtbot):
        """
        Test that editing a field in the form and triggering editingFinished
        causes the metadata_field_changed signal to be emitted with the correct
        field name and new value.
        """
        field_name = "title"
        new_value = "New Title Value"

        with qtbot.waitSignal(widget.metadata_field_changed) as blocker:
            widget.title_edit.setText(new_value)
            widget.title_edit.editingFinished.emit()

        assert blocker.args == [field_name, new_value]