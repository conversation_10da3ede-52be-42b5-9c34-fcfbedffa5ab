import pytest
from PySide6.QtWidgets import QApplication, QFileDialog
from PySide6.QtCore import Qt
from unittest.mock import MagicMock, patch

from src.abb.ui.widgets.output_settings_widget import OutputSettingsWidget

# Ensure QApplication is initialized for tests
@pytest.fixture
def widget(qtbot):
    initial_settings = {
        "output_directory": "/test/output",
        "output_bitrate": 64,
        "output_sample_rate": 48000,
        "output_channels": 1, # Mono
        "output_filename_pattern": 1,
        "output_create_subdirectory": True,
    }
    return OutputSettingsWidget(initial_settings=initial_settings)

def test_output_settings_widget_loads_initial_values(widget):
    assert widget.output_dir_edit.text() == "/test/output"
    assert widget.bitrate_combo.currentText() == "64"
    assert widget.samplerate_combo.currentText() == "48000"
    assert widget.channels_combo.currentText() == "Mono"
    assert widget.filename_pattern_edit.text() == "1"
    assert widget.subdir_checkbox.isChecked() is True

def test_output_settings_widget_emits_setting_changed_on_interaction(widget):
    mock_slot = MagicMock()
    widget.setting_changed_signal.connect(mock_slot)

    # Test bitrate change
    widget.bitrate_combo.setCurrentText("96")
    mock_slot.assert_called_with("output_bitrate", 96)
    mock_slot.reset_mock()

    # Test sample rate change
    widget.samplerate_combo.setCurrentText("22050")
    mock_slot.assert_called_with("output_sample_rate", 22050)
    mock_slot.reset_mock()

    # Test channels change - Stereo
    widget.channels_combo.setCurrentText("Stereo")
    mock_slot.assert_called_with("output_channels", 2)
    mock_slot.reset_mock()

    # Test channels change - Mono
    widget.channels_combo.setCurrentText("Mono")
    mock_slot.assert_called_with("output_channels", 1)
    mock_slot.reset_mock()

    # Test output directory edit
    widget.output_dir_edit.setText("/new/path")
    mock_slot.assert_called_with("output_directory", "/new/path")
    mock_slot.reset_mock()

    # Test filename pattern edit
    widget.filename_pattern_edit.setText("0")
    mock_slot.assert_called_with("output_filename_pattern", 0) # Should convert to int
    mock_slot.reset_mock()

    widget.filename_pattern_edit.setText("invalid")
    mock_slot.assert_called_with("output_filename_pattern", "invalid") # Should emit raw string if conversion fails
    mock_slot.reset_mock()

    # Test create subdirectory checkbox
    widget.subdir_checkbox.setChecked(False)
    mock_slot.assert_called_with("output_create_subdirectory", False)
    mock_slot.reset_mock()

    widget.subdir_checkbox.setChecked(True)
    mock_slot.assert_called_with("output_create_subdirectory", True)
    mock_slot.reset_mock()

def test_browse_output_directory_updates_lineedit_and_emits_signal(widget):
    mock_slot = MagicMock()
    widget.setting_changed_signal.connect(mock_slot)

    mock_dir = "/mock/selected/directory"
    with patch.object(QFileDialog, 'getExistingDirectory', return_value=mock_dir) as mock_get_dir:
        widget.output_dir_button.click()
        mock_get_dir.assert_called_once()
        assert widget.output_dir_edit.text() == mock_dir
        mock_slot.assert_called_with("output_directory", mock_dir)