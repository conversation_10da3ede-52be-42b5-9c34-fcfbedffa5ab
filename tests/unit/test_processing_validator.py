"""Tests for ProcessingValidator class."""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from src.abb.processing_validator import ProcessingValidator


class TestProcessingValidator:
    """Test ProcessingValidator functionality."""
    
    @pytest.fixture
    def validator(self):
        """Create a ProcessingValidator instance."""
        return ProcessingValidator()
    
    @pytest.fixture
    def mock_input_files(self, tmp_path):
        """Create mock input files."""
        files = []
        for i in range(3):
            file = tmp_path / f"input{i}.mp3"
            file.write_text("mock audio data")
            files.append(file)
        return files
    
    def test_validate_all_success(self, validator, mock_input_files, tmp_path):
        """Test successful validation of all requirements."""
        output_dir = tmp_path / "output"
        
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)  # 1GB free
            
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100  # 100MB estimated
            )
            
            assert success is True
            assert error is None
    
    def test_validate_no_input_files(self, validator, tmp_path):
        """Test validation fails with no input files."""
        output_dir = tmp_path / "output"
        
        success, error = validator.validate_all([], output_dir, 100)
        
        assert success is False
        assert error == "No input files provided"
    
    def test_validate_input_file_not_found(self, validator, tmp_path):
        """Test validation fails when input file doesn't exist."""
        missing_file = tmp_path / "missing.mp3"
        output_dir = tmp_path / "output"
        
        success, error = validator.validate_all([missing_file], output_dir, 100)
        
        assert success is False
        assert "Input file not found" in error
    
    def test_validate_input_file_not_readable(self, validator, tmp_path):
        """Test validation fails when input file is not readable."""
        file = tmp_path / "unreadable.mp3"
        file.write_text("data")
        output_dir = tmp_path / "output"
        
        with patch('os.access', return_value=False):
            success, error = validator.validate_all([file], output_dir, 100)
        
        assert success is False
        assert "File not readable" in error
    
    def test_validate_output_directory_not_writable(self, validator, mock_input_files, tmp_path):
        """Test validation fails when output directory is not writable."""
        output_dir = tmp_path / "output"
        output_dir.mkdir()
        
        with patch('pathlib.Path.touch', side_effect=PermissionError):
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        
        assert success is False
        assert "Output directory not writable" in error
    
    def test_validate_insufficient_disk_space(self, validator, mock_input_files, tmp_path):
        """Test validation fails with insufficient disk space."""
        output_dir = tmp_path / "output"
        
        with patch('shutil.disk_usage') as mock_disk:
            # Only 100MB free, need 120MB (100 * 1.2)
            mock_disk.return_value = MagicMock(free=100*1024*1024)
            
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        
        assert success is False
        assert "Insufficient disk space" in error
        assert "Required: 120.0 MB" in error
        assert "Available: 100.0 MB" in error
    
    def test_validate_disk_space_check_fails_gracefully(self, validator, mock_input_files, tmp_path):
        """Test validation continues if disk space check fails."""
        output_dir = tmp_path / "output"
        
        with patch('shutil.disk_usage', side_effect=Exception("Disk error")):
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        
        # Should succeed despite disk check failure
        assert success is True
        assert error is None
    
    def test_validate_creates_output_directory(self, validator, mock_input_files, tmp_path):
        """Test validation creates output directory if it doesn't exist."""
        output_dir = tmp_path / "new_output"
        assert not output_dir.exists()
        
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)
            
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        
        assert success is True
        assert error is None
        assert output_dir.exists()
    
    def test_processing_validator_checks_all_requirements(self, validator, mock_input_files, tmp_path):
        """Test the high-level behavioral test from implementation plan."""
        output_dir = tmp_path / "output"
        
        # This test ensures all validations are performed
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)
            
            # Spy on internal methods to ensure they're all called
            with patch.object(validator, '_validate_input_files', wraps=validator._validate_input_files) as spy_input, \
                 patch.object(validator, '_validate_output_directory', wraps=validator._validate_output_directory) as spy_output, \
                 patch.object(validator, '_validate_disk_space', wraps=validator._validate_disk_space) as spy_disk:
                
                success, error = validator.validate_all(
                    mock_input_files, output_dir, 100
                )
                
                # Verify all validation methods were called
                spy_input.assert_called_once()
                spy_output.assert_called_once()
                spy_disk.assert_called_once()
        
        assert success is True
        assert error is None