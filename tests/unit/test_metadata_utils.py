"""
Unit tests for metadata_utils module.

Tests the extract_tags and extract_cover functions.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from src.abb.metadata_utils import extract_tags, extract_cover


class TestMetadataUtils:
    """Test cases for metadata utility functions."""
    
    def test_extract_tags_returns_dict_with_relevant_tags(self):
        """Test that extract_tags returns a dictionary with all expected tag keys."""
        # Test with MP3 file
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            # Mock MP3 file with ID3 tags
            mock_audio = Mock()
            mock_audio.tags = {
                "TIT2": Mock(__str__=lambda self: "Test Title"),
                "TPE1": <PERSON><PERSON>(__str__=lambda self: "Test Artist"),
                "TALB": Mock(__str__=lambda self: "Test Album"),
                "TCON": Mock(__str__=lambda self: "Test Genre"),
                "TRCK": Mock(__str__=lambda self: "1"),
                "TDRC": <PERSON><PERSON>(__str__=lambda self: "2023")
            }
            mock_file.return_value = mock_audio
            
            result = extract_tags("/path/to/test.mp3")
            
            assert isinstance(result, dict)
            assert result["title"] == "Test Title"
            assert result["artist"] == "Test Artist"
            assert result["album"] == "Test Album"
            assert result["genre"] == "Test Genre"
            assert result["track_number"] == "1"
            assert result["year"] == "2023"
    
    def test_extract_tags_handles_missing_tags(self):
        """Test that extract_tags returns None for missing tags."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            # Mock MP3 file with only some tags
            mock_audio = Mock()
            mock_audio.tags = {
                "TIT2": Mock(__str__=lambda self: "Only Title"),
                "TPE1": Mock(__str__=lambda self: "Only Artist")
            }
            mock_file.return_value = mock_audio
            
            result = extract_tags("/path/to/test.mp3")
            
            assert result["title"] == "Only Title"
            assert result["artist"] == "Only Artist"
            assert result["album"] is None
            assert result["genre"] is None
            assert result["track_number"] is None
            assert result["year"] is None
    
    def test_extract_tags_handles_m4a_format(self):
        """Test that extract_tags correctly handles M4A/MP4 format."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            # Mock M4A file with MP4 tags
            mock_audio = Mock()
            mock_audio.tags = {
                "©nam": ["M4A Title"],
                "©ART": ["M4A Artist"],
                "©alb": ["M4A Album"],
                "©gen": ["M4A Genre"],
                "trkn": [(5, 10)],  # Track 5 of 10
                "©day": ["2024"]
            }
            mock_file.return_value = mock_audio
            
            result = extract_tags("/path/to/test.m4a")
            
            assert result["title"] == "M4A Title"
            assert result["artist"] == "M4A Artist"
            assert result["album"] == "M4A Album"
            assert result["genre"] == "M4A Genre"
            assert result["track_number"] == "5"
            assert result["year"] == "2024"
    
    def test_extract_tags_handles_file_not_found(self):
        """Test that extract_tags returns empty dict when file cannot be read."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.return_value = None
            
            result = extract_tags("/path/to/nonexistent.mp3")
            
            assert isinstance(result, dict)
            assert all(value is None for value in result.values())
    
    def test_extract_tags_handles_exceptions(self):
        """Test that extract_tags handles exceptions gracefully."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.side_effect = Exception("Test error")
            
            result = extract_tags("/path/to/error.mp3")
            
            assert isinstance(result, dict)
            assert all(value is None for value in result.values())
    
    def test_extract_cover_art_returns_image_data_or_none(self):
        """Test that extract_cover returns bytes for cover art or None."""
        # Test MP3 with APIC frame
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_apic = Mock()
            mock_apic.data = b"fake_image_data"
            mock_audio.tags = {"APIC:": mock_apic}
            mock_file.return_value = mock_audio
            
            result = extract_cover("/path/to/test.mp3")
            
            assert result == b"fake_image_data"
            assert isinstance(result, bytes)
    
    def test_extract_cover_returns_none_when_no_cover_art(self):
        """Test that extract_cover returns None when no cover art is present."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_audio.tags = {"TIT2": "Title only, no cover"}
            mock_file.return_value = mock_audio
            
            result = extract_cover("/path/to/test.mp3")
            
            assert result is None
    
    def test_extract_cover_handles_m4a_format(self):
        """Test that extract_cover correctly extracts cover from M4A files."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            # Mock MP4Cover object - when bytes() is called on it, it returns the data
            mock_cover = b"m4a_cover_data"  # MP4Cover objects are bytes-like
            mock_audio.tags = {"covr": [mock_cover]}
            mock_file.return_value = mock_audio
            
            result = extract_cover("/path/to/test.m4a")
            
            assert result == b"m4a_cover_data"
            assert isinstance(result, bytes)
    
    def test_extract_cover_handles_file_not_found(self):
        """Test that extract_cover returns None when file cannot be read."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.return_value = None
            
            result = extract_cover("/path/to/nonexistent.mp3")
            
            assert result is None
    
    def test_extract_cover_handles_exceptions(self):
        """Test that extract_cover handles exceptions gracefully."""
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.side_effect = Exception("Test error")
            
            result = extract_cover("/path/to/error.mp3")
            
            assert result is None