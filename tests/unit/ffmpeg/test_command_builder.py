import os
import pytest
from unittest.mock import Mock, patch
from src.abb.ffmpeg.command_builder import FFmpegCommandBuilder
from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler


class TestFFmpegCommandBuilder:
    def test_command_builder_exists_and_has_required_methods(self):
        """Smoke test to ensure refactoring worked"""
        mock_codec_check = Mock(return_value=True)
        builder = FFmpegCommandBuilder(mock_codec_check)
        assert hasattr(builder, 'build_ffmpeg_command')
        assert hasattr(builder, 'build_ffmpeg_preview_command')
        assert callable(builder.build_ffmpeg_command)
        assert callable(builder.build_ffmpeg_preview_command)
    
    def test_build_command_includes_basic_elements(self):
        """Ensure command has essential components"""
        mock_codec_check = Mock(return_value=False)  # Force to use 'aac'
        builder = FFmpegCommandBuilder(mock_codec_check)
        
        cmd = builder.build_ffmpeg_command(
            input_files=['/path/to/input.mp3'],
            output_file_full_path='/path/to/output.m4b',
            metadata={'title': 'Test'},
            settings={'bitrate': 64},
            ffmpeg_exe_path='ffmpeg'
        )
        
        assert 'ffmpeg' in cmd
        assert '-i' in cmd
        assert '/path/to/input.mp3' in cmd
        assert '/path/to/output.m4b' in cmd
        assert '-metadata' in cmd
        assert 'title=Test' in cmd
    
    def test_legacy_path_uses_unified_handler_mapping(self):
        """Test that even legacy path uses UnifiedMetadataHandler as single source of truth"""
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'year': '2023'
        }
        
        # Test legacy path
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}):
            result = builder._get_ffmpeg_tags_legacy(metadata)
            
        # Verify that the mapping comes from UnifiedMetadataHandler
        expected_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        assert 'title' in result
        assert 'artist' in result
        assert 'date' in result  # year -> date mapping from UnifiedMetadataHandler
        
    def test_unified_path_uses_handler_directly(self):
        """Test that unified path uses UnifiedMetadataHandler directly"""
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'year': '2023'
        }
        
        # Test unified path
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            result = builder._get_ffmpeg_tags_via_handler(metadata)
            
        assert 'title' in result
        assert 'artist' in result
        assert 'album_artist' in result  # Should be copied from artist
        assert 'date' in result  # year -> date mapping
        
    def test_both_paths_produce_identical_results(self):
        """Test that both ABB_NEW_META paths produce identical results"""
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'album': 'Test Album',
            'year': '2023',
            'genre': 'Fiction'
        }
        
        # Test both paths
        legacy_result = builder._get_ffmpeg_tags_legacy(metadata)
        unified_result = builder._get_ffmpeg_tags_via_handler(metadata)
        
        # Results should be identical
        assert legacy_result == unified_result
        
    def test_deprecated_constant_still_exists_for_backward_compatibility(self):
        """Test that the deprecated constant still exists for backward compatibility"""
        # The constant should still exist but be marked as deprecated
        assert hasattr(FFmpegCommandBuilder, 'ABB_TO_FFMPEG_METADATA_MAP_GENERAL')
        
        # But it should match the UnifiedMetadataHandler mapping
        builder_mapping = FFmpegCommandBuilder.ABB_TO_FFMPEG_METADATA_MAP_GENERAL
        handler_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        
        assert builder_mapping == handler_mapping
        
    def test_environment_variable_controls_path_selection(self):
        """Test that ABB_NEW_META environment variable controls which path is used"""
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        
        # Test False/default case
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}, clear=False):
            assert not builder._should_use_unified_handler()
            
        # Test True case
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}, clear=False):
            assert builder._should_use_unified_handler()
            
        # Test default case (no env var)
        with patch.dict(os.environ, {}, clear=True):
            assert not builder._should_use_unified_handler()