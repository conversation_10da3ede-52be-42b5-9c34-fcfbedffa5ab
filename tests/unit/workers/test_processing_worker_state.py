"""Tests for ProcessingWorker state management."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QProcess
from src.abb.processing_worker import ProcessingWorker
from src.abb.ffmpeg_utils import _get_executable_path


class TestProcessingWorkerStateManagement:
    """Test ProcessingWorker state management functionality."""
    
    @pytest.fixture
    def worker(self):
        """Create a ProcessingWorker instance."""
        return ProcessingWorker("/usr/bin/ffmpeg")
    
    @pytest.fixture
    def mock_process_params(self):
        """Create mock parameters for process method."""
        return {
            "file_list": ["file1.mp3", "file2.mp3"],
            "output_path": "/output",
            "output_filename": "book.m4b",
            "metadata": {"title": "Test Book"},
            "settings": {"bitrate": 64},
            "total_duration_seconds": 3600
        }
    
    def test_worker_tracks_processing_state_correctly(self, worker, mock_process_params):
        """Test that worker correctly tracks processing state."""
        # Initially not processing
        assert worker._is_processing is False
        
        # Mock the command building to fail early
        with patch('src.abb.processing_worker.build_ffmpeg_command', return_value=None):
            worker.process(**mock_process_params)
        
        # Should reset to not processing after error
        assert worker._is_processing is False
    
    def test_worker_prevents_concurrent_processing(self, worker, mock_process_params, qtbot):
        """Test that worker prevents concurrent processing attempts."""
        # Set processing state manually
        worker._is_processing = True
        
        # Attempt to start another process
        with qtbot.waitSignal(worker.error, timeout=100) as blocker:
            worker.process(**mock_process_params)
        
        # Should emit error
        assert len(blocker.args) == 1
        assert "Processing already in progress" in blocker.args[0]
    
    def test_worker_cleanup_always_executes_on_error(self, worker, mock_process_params):
        """Test that cleanup and state reset happen on error."""
        # Set up worker to have a valid ffmpeg path
        worker.ffmpeg_path = "/usr/bin/ffmpeg"
        
        # Mock to raise an exception after _is_processing is set
        # Mock build_ffmpeg_command to raise error (this happens after _is_processing=True)
        with patch('src.abb.processing_worker.build_ffmpeg_command', side_effect=Exception("Test error")):
            with patch.object(worker, '_cleanup_temp_files') as mock_cleanup:
                with patch('os.path.isfile', return_value=True), \
                     patch('os.access', return_value=True), \
                     patch('src.abb.processing_worker._get_executable_path', return_value='/usr/bin/ffprobe'):
                    worker.process(**mock_process_params)
                
                # Cleanup should be called
                mock_cleanup.assert_called_once()
                
        # State should be reset by finally block
        assert worker._is_processing is False
    
    def test_cancel_checks_processing_state(self, worker):
        """Test that cancel only works when processing."""
        # Not processing, cancel should return early
        worker._is_processing = False
        worker.cancel()
        
        # No process should be created
        assert worker._process is None
    
    def test_state_reset_on_process_finished(self, worker):
        """Test that state is reset when process finishes."""
        # Set up state
        worker._is_processing = True
        worker._is_cancelled = False
        
        # Mock process
        mock_process = Mock(spec=QProcess)
        mock_process.readAllStandardError.return_value = b""
        worker._process = mock_process
        
        # Successful completion
        worker._process_finished(0, QProcess.NormalExit)
        
        # State should be reset
        assert worker._is_processing is False
        assert worker._is_cancelled is False
    
    def test_state_reset_on_cancelled_process(self, worker):
        """Test that state is reset when process is cancelled."""
        # Set up state
        worker._is_processing = True
        worker._is_cancelled = True
        
        # Mock cleanup method
        with patch.object(worker, '_cleanup_after_cancel_or_error'):
            worker._process_finished(1, QProcess.NormalExit)
        
        # State should be reset
        assert worker._is_processing is False
        assert worker._is_cancelled is False
    
    def test_preview_process_respects_state(self, worker, qtbot):
        """Test that preview process also respects processing state."""
        # Set processing state
        worker._is_processing = True
        
        # Attempt preview
        with qtbot.waitSignal(worker.error, timeout=100) as blocker:
            worker.start_preview_process(
                input_file_path="test.mp3",
                metadata={},
                settings={}
            )
        
        # Should emit error
        assert len(blocker.args) == 1
        assert "Processing already in progress" in blocker.args[0]
    
    def test_multiple_early_returns_reset_state(self, worker, mock_process_params):
        """Test that all early return paths reset processing state."""
        # Test FFmpeg not found path
        worker.ffmpeg_path = "/nonexistent/ffmpeg"
        worker.process(**mock_process_params)
        assert worker._is_processing is False
        
        # Test command build failure path
        with patch('src.abb.processing_worker.build_ffmpeg_command', return_value=None):
            worker.process(**mock_process_params)
        assert worker._is_processing is False