import pytest
import logging
from unittest.mock import <PERSON><PERSON>ock, patch
from PySide6.QtTest import QSignalSpy

from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager


class TestMainControllerProperties:
    """Test MainController property-related functionality"""
    
    @pytest.fixture
    def controller_with_settings(self, tmp_path):
        """Create a MainController with a SettingsManager for testing"""
        # Create a temporary settings file
        settings_file = tmp_path / "settings.json"
        
        # Create a SettingsManager with default settings
        default_settings = {
            "output_bitrate": 64,
            "output_channels": 2,
            "output_sample_rate": 44100,
            "output_directory": str(tmp_path),
            "output_filename_pattern": 0,
            "output_create_subdirectory": True
        }
        settings_manager = SettingsManager(str(settings_file), default_settings)
        
        # Create controller with settings manager
        controller = MainController(settings_manager=settings_manager)
        
        return controller
    
    def test_handle_file_selection_changed_with_metadata(self, qtbot, tmp_path, controller_with_settings):
        """Controller extracts metadata when file is selected"""
        controller = controller_with_settings
        
        # Create a real file
        test_file = tmp_path / "test.mp3"
        test_file.write_bytes(b"x" * 100)
        
        # Add the file to the controller
        controller.add_files([str(test_file)])
        
        # Mock the metadata extraction
        with patch.object(controller._metadata_service, 'extract_metadata') as mock_extract, \
             patch('src.abb.ffmpeg_utils.get_audio_properties') as mock_get_props, \
             patch('src.abb.metadata_utils.mutagen.File') as mock_mutagen_file, \
             patch('src.abb.metadata_utils.ID3') as mock_id3:
            
            # Create expected metadata
            expected_metadata = {
                'title': 'Test Title',
                'artist': 'Test Artist',
                'bitrate': 128
            }
            
            # Mock extract_metadata to return expected data AND update internal state
            def mock_extract_side_effect(file_path):
                # Update the service's internal metadata state
                controller._metadata_service._metadata = expected_metadata.copy()
                # Emit the signal as the real method would (Phase 1B: use consolidated signal)
                controller._metadata_service.metadata_loaded.emit(expected_metadata.copy())
                return expected_metadata
            
            mock_extract.side_effect = mock_extract_side_effect
            mock_get_props.return_value = ({'bitrate': 128, 'sample_rate': 44100, 'channels': 2, 'duration': 60, 'file_size': 1000}, None)
            mock_mutagen_file.return_value = MagicMock(info=MagicMock(length=60))
            mock_id3_instance = MagicMock()
            mock_id3_instance.__contains__.side_effect = lambda k: k in ["TIT2", "TPE1", "TALB"]
            mock_id3_instance.__getitem__.side_effect = lambda k: {
                "TIT2": "Test Title",
                "TPE1": "Test Artist",
                "TALB": "Test Album"
            }[k]
            mock_id3.return_value = mock_id3_instance
            
            # Spy on the signal
            spy = QSignalSpy(controller.selected_file_data_changed)
            
            # Handle selection
            controller.handle_file_selection_changed([str(test_file)])
            
            # Should have called extract_metadata
            mock_extract.assert_called_once_with(str(test_file))
            
            # Should have emitted signal with metadata
            qtbot.wait(100)
            assert spy.count() == 1
            emitted_data = spy.at(0)[0]
            assert 'title' in emitted_data
    
    def test_handle_file_selection_changed_no_files(self, qtbot, controller_with_settings):
        """Controller emits empty dict when no files selected"""
        controller = controller_with_settings
        
        spy = QSignalSpy(controller.selected_file_data_changed)
        
        # Handle empty selection
        controller.handle_file_selection_changed([])
        
        # Should emit empty dict
        assert spy.count() == 1
        assert spy.at(0)[0] == {}
    
    def test_files_changed_updates_ui(self, qtbot, controller_with_settings):
        """File changes trigger UI updates through signals"""
        controller = controller_with_settings
        
        # Mock file service
        controller._file_service = MagicMock()
        
        # Spy on file list signal
        spy = QSignalSpy(controller.file_list_updated_signal)
        
        # Trigger files changed
        controller._on_files_changed(['file1.mp3', 'file2.mp3'])
        
        # Should emit signal
        assert spy.count() == 1
        assert spy.at(0)[0] == ['file1.mp3', 'file2.mp3'] 