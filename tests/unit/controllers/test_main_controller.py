import pytest
from unittest.mock import <PERSON><PERSON><PERSON>, patch, Mock
from PySide6.QtTest import QSignalSpy
from PySide6.QtCore import QObject, Signal
from pathlib import Path

from src.abb.controllers.main_controller import MainController


@pytest.fixture
def main_controller_with_mocked_services():
    """
    Fixture to create a MainController with mocked services for testing.
    This fixture ensures that MainController is instantiated with a mocked
    SettingsManager, as its constructor now requires it.
    """
    with patch('src.abb.controllers.main_controller.FileService') as MockFileService, \
         patch('src.abb.controllers.main_controller.MetadataService') as MockMetadataService, \
         patch('src.abb.controllers.main_controller.ProcessingService') as MockProcessingService, \
         patch('src.abb.controllers.main_controller.SettingsManager') as MockSettingsManager, \
         patch('src.abb.controllers.main_controller.PathService') as MockPathService: # Patch PathService too

        # Instantiate mocks
        mock_file_service = MockFileService.return_value
        mock_metadata_service = MockMetadataService.return_value
        mock_processing_service = MockProcessingService.return_value
        mock_settings_manager = MockSettingsManager.return_value
        mock_path_service = MockPathService.return_value # Get mock for PathService

        # Pass the mocked settings_manager to the MainController constructor
        controller = MainController(settings_manager=mock_settings_manager)
        
        # Ensure the internal service attributes point to our mocks
        controller._file_service = mock_file_service
        controller._metadata_service = mock_metadata_service
        controller._processing_service = mock_processing_service
        # _settings_manager is already set via constructor, no need to re-assign
        controller._path_service = mock_path_service # Assign mocked PathService

        yield controller, {
            'file_service': mock_file_service,
            'metadata_service': mock_metadata_service,
            'processing_service': mock_processing_service,
            'settings_manager': mock_settings_manager,
            'path_service': mock_path_service # Include PathService in mocks dict
        }


class TestMainControllerFileOperations:
    """Test MainController file-related operations"""
    
    def test_add_files_delegates_to_service(self, main_controller_with_mocked_services):
        """MainController delegates add_files to FileService"""
        controller, mocks = main_controller_with_mocked_services
        mock_file_service = mocks['file_service']
        mock_settings_manager = mocks['settings_manager'] # Get settings_manager mock

        mock_file_service.add_files.return_value = ["/path/to/file1.mp3"]
        
        # Add files
        result = controller.add_files(["/path/to/file1.mp3", "/path/to/file2.txt"])
        
        # Verify delegation
        mock_file_service.add_files.assert_called_once_with(["/path/to/file1.mp3", "/path/to/file2.txt"])
        assert result == ["/path/to/file1.mp3"]
        # Verify setting was updated
        mock_settings_manager.set_setting.assert_called_once_with( # Changed to assert_called_once_with
            "last_input_dir", "/path/to"
        )
    
    def test_updates_last_input_dir_setting(self, main_controller_with_mocked_services):
        """MainController updates last input directory when adding files"""
        controller, mocks = main_controller_with_mocked_services
        mock_settings_manager = mocks['settings_manager']
        mock_file_service = mocks['file_service']

        mock_file_service.add_files.return_value = ["/path/to/file.mp3"]
        
        # Add files
        controller.add_files(["/path/to/file.mp3"])
        
        # Verify setting was updated
        mock_settings_manager.set_setting.assert_called_with(
            "last_input_dir", "/path/to"
        )
    
    def test_controller_reorder_files_delegates_to_file_service(self, main_controller_with_mocked_services):
        """MainController delegates reorder_files to FileService"""
        controller, mocks = main_controller_with_mocked_services
        mock_file_service = mocks['file_service']
        
        # Test data
        new_order_paths = ["file2.mp3", "file1.mp3", "file3.mp3"]
        
        # Call reorder_files
        controller.reorder_files(new_order_paths)
        
        # Verify delegation
        mock_file_service.reorder_files.assert_called_once_with(new_order_paths)


class TestMainControllerMetadataIntegration:
    """Test MainController metadata integration functionality"""
    
    def test_metadata_service_extract_called_on_files_changed_with_no_metadata(self, qtbot, main_controller_with_mocked_services):
        """Test that metadata is extracted from first file when files are added and no metadata exists"""
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        
        mock_metadata_service.current_metadata = None
        
        # Simulate files changed signal
        files = ["/path/to/file1.mp3", "/path/to/file2.mp3"]
        controller._on_files_changed(files)
        
        # Verify extract_and_load_metadata was called with first file
        mock_metadata_service.extract_and_load_metadata.assert_called_once_with("/path/to/file1.mp3")
    
    def test_metadata_service_not_called_when_metadata_exists(self, qtbot, main_controller_with_mocked_services):
        """Test that metadata is not extracted when metadata already exists"""
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        
        # Mock the metadata service with existing metadata
        mock_metadata_service.current_metadata = {"title": "Existing"}
        
        # Simulate files changed signal
        files = ["/path/to/file1.mp3"]
        controller._on_files_changed(files)
        
        # Verify extract_and_load_metadata was NOT called
        mock_metadata_service.extract_and_load_metadata.assert_not_called()
    
    def test_metadata_service_not_called_when_no_files(self, qtbot, main_controller_with_mocked_services):
        """Test that metadata is not extracted when file list is empty"""
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        
        mock_metadata_service.current_metadata = None
        
        # Simulate files changed signal with empty list
        controller._on_files_changed([])
        
        # Verify extract_and_load_metadata was NOT called
        mock_metadata_service.extract_and_load_metadata.assert_not_called()
    
    def test_metadata_updated_signal_emitted_on_metadata_loaded(self, qtbot, main_controller_with_mocked_services):
        """Test that metadata_updated_signal is emitted when metadata_loaded signal fires"""
        controller, mocks = main_controller_with_mocked_services
        
        # Create a real MetadataService to get real signals
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service # Temporarily replace mock with real for signal test
        
        # Re-connect signals after replacing the service
        controller._connect_service_signals()
        
        # Set up signal spy
        spy = QSignalSpy(controller.metadata_updated_signal)
        
        # Emit metadata_loaded signal
        test_metadata = {"title": "Test Title", "artist": "Test Artist"}
        metadata_service.metadata_loaded.emit(test_metadata)
        
        # Verify signal was emitted
        assert spy.count() == 1
        assert spy.at(0)[0] == test_metadata
    
    def test_cover_art_updated_signal_emitted_on_cover_art_loaded(self, qtbot, main_controller_with_mocked_services):
        """Test that cover_art_updated_signal is emitted when metadata_loaded signal contains cover art"""
        controller, mocks = main_controller_with_mocked_services
        
        # Create a real MetadataService to get real signals
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service # Temporarily replace mock with real for signal test
        
        # Re-connect signals after replacing the service
        controller._connect_service_signals()
        
        # Set up signal spy
        spy = QSignalSpy(controller.cover_art_updated_signal)
        
        # Emit metadata_loaded signal with cover art data
        test_metadata_with_cover = {"cover_art_data": b"fake_image_data"}
        metadata_service.metadata_loaded.emit(test_metadata_with_cover)
        
        # Verify signal was emitted with cover art data
        assert spy.count() == 1
        assert spy.at(0)[0] == b"fake_image_data"
    
    def test_cover_art_updated_signal_emitted_with_none(self, qtbot, main_controller_with_mocked_services):
        """Test that cover_art_updated_signal is not emitted when metadata_loaded has no cover art"""
        controller, mocks = main_controller_with_mocked_services
        
        # Create a real MetadataService to get real signals
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service # Temporarily replace mock with real for signal test
        
        # Re-connect signals after replacing the service
        controller._connect_service_signals()
        
        # Set up signal spy
        spy = QSignalSpy(controller.cover_art_updated_signal)
        
        # Emit metadata_loaded signal without cover art data
        test_metadata = {"title": "Test"}
        metadata_service.metadata_loaded.emit(test_metadata)
        
        # Verify signal was not emitted since no cover art data
        assert spy.count() == 0


class TestMainControllerSignalDisconnection:
    """Test MainController signal disconnection functionality."""

    @pytest.fixture
    def controller_with_mocks(self, main_controller_with_mocked_services):
        """
        Fixture to create a MainController with mocked services
        and ensure signals are connected.
        This fixture now uses the shared main_controller_with_mocked_services.
        """
        controller, mocks = main_controller_with_mocked_services

        # Mock the signals on the service mocks with connect/disconnect methods
        def create_signal_mock():
            signal_mock = MagicMock()
            signal_mock.connect = MagicMock()
            signal_mock.disconnect = MagicMock()
            return signal_mock
        
        # Re-assign mocks to ensure they have connect/disconnect methods for this test's assertions
        mocks['file_service'].files_changed = create_signal_mock()
        mocks['file_service'].combined_size_changed_signal = create_signal_mock()
        # Phase 1B: Use consolidated MetadataService signals
        mocks['metadata_service'].metadata_loaded = create_signal_mock()
        mocks['metadata_service'].metadata_updated = create_signal_mock()
        mocks['metadata_service'].metadata_error = create_signal_mock()
        mocks['processing_service'].progress = create_signal_mock()
        mocks['processing_service'].finished = create_signal_mock()
        mocks['processing_service'].error = create_signal_mock()
        mocks['processing_service'].status = create_signal_mock()
        mocks['settings_manager'].settings_changed = create_signal_mock()

        # Re-connect signals to the mocked services if __init__ didn't already
        # (MainController's __init__ calls _connect_service_signals)
        controller._connect_service_signals()
        
        yield controller, mocks
