"""Tests for build_ffmpeg_processing_command function."""

import pytest
from unittest.mock import patch, Mock
from src.abb.ffmpeg_utils import build_ffmpeg_processing_command


class TestBuildFFmpegProcessingCommand:
    """Test build_ffmpeg_processing_command functionality."""
    
    def test_build_ffmpeg_command_concatenates_inputs(self):
        """Test that command handles multiple input files."""
        file_list = ["file1.mp3", "file2.mp3", "file3.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test Book"}
        audio_settings = {"bitrate": 64, "channels": 1}
        ffmpeg_path = "/usr/bin/ffmpeg"
        
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            mock_build.return_value = ["ffmpeg", "-i", "file1.mp3"]
            
            result = build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            
            # Verify build_ffmpeg_command was called with correct params
            mock_build.assert_called_once_with(
                input_files=file_list,
                output_file_full_path=output_path,
                metadata=metadata_dict,
                settings=audio_settings,
                ffmpeg_exe_path=ffmpeg_path
            )
    
    def test_build_ffmpeg_command_embeds_metadata_tags(self):
        """Test that command includes metadata tags."""
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {
            "title": "My Audiobook",
            "artist": "John Doe",
            "album": "Great Series",
            "year": "2025",
            "comment": "A great book"
        }
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            
            # Check metadata was passed through
            call_args = mock_build.call_args[1]
            assert call_args['metadata'] == metadata_dict
    
    def test_build_ffmpeg_command_embeds_cover_art(self):
        """Test that command includes cover art when provided."""
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test"}
        cover_art_path = "/path/to/cover.jpg"
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=cover_art_path,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            
            # Check cover art was added to metadata
            call_args = mock_build.call_args[1]
            assert call_args['metadata']['cover_art_temp_path'] == cover_art_path
    
    def test_build_ffmpeg_command_applies_audio_settings(self):
        """Test that command includes audio settings."""
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test"}
        audio_settings = {
            "bitrate": 96,
            "channels": 2,
            "sample_rate": 44100
        }
        ffmpeg_path = "/usr/bin/ffmpeg"
        
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            
            # Check audio settings were passed through
            call_args = mock_build.call_args[1]
            assert call_args['settings'] == audio_settings
    
    def test_build_ffmpeg_command_preserves_original_metadata(self):
        """Test that original metadata dict is not modified."""
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Original Title"}
        cover_art_path = "/path/to/cover.jpg"
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        
        original_metadata = metadata_dict.copy()
        
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command'):
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=cover_art_path,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
        
        # Original metadata should not be modified
        assert metadata_dict == original_metadata
        assert 'cover_art_temp_path' not in metadata_dict