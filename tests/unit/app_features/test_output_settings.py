import os
import tempfile
import json
import pytest
from unittest.mock import Mock, patch
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.ui.widgets.output_settings_widget import OutputSettingsWidget


class TestOutputSettings:
    def test_changing_output_settings_updates_controller_and_settings_manager(self, qtbot, tmp_path):
        """Test that changing any output setting updates both controller and settings manager"""
        # Create a temporary settings file for testing
        settings_file = tmp_path / "settings.json"
        
        # Create a SettingsManager with the temporary file
        from src.abb.services.settings_manager import SettingsManager
        settings_manager = SettingsManager(str(settings_file), {})
        
        # Create controller with the SettingsManager
        controller = MainController(settings_manager=settings_manager)
        
        # Create widget with initial settings
        initial_settings = {
            "output_bitrate": 64,
            "output_channels": 2,
            "output_create_subdirectory": True,
            "output_filename_pattern": 0
        }
        widget = OutputSettingsWidget(initial_settings=initial_settings)
        qtbot.addWidget(widget)
        
        # Connect widget to controller
        widget.setting_changed_signal.connect(controller.update_output_setting)
        
        # Test bitrate change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.bitrate_combo.setCurrentText("96")  # Select 96 kbps
            qtbot.wait(50)  # Wait for signal to be processed
            mock_set.assert_called_with('output_bitrate', 96)
        
        # Test sample rate change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.samplerate_combo.setCurrentText("44100")  # Select 44.1 kHz
            qtbot.wait(50)  # Wait for signal to be processed
            mock_set.assert_called_with('output_sample_rate', 44100)
        
        # Test channels change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.channels_combo.setCurrentText("Mono")  # Select Mono
            qtbot.wait(50)  # Wait for signal to be processed
            mock_set.assert_called_with('output_channels', 1)
        
        # Test output directory change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            # Simulate setting the output directory via the line edit
            widget.output_dir_edit.setText("/custom/output/dir")
            qtbot.wait(50)  # Wait for signal to be processed
            # Trigger the textChanged signal manually since setText doesn't always trigger it in tests
            widget._on_setting_changed("output_directory", "/custom/output/dir")
            mock_set.assert_called_with('output_directory', "/custom/output/dir")
        
        # Test filename pattern change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            # Simulate changing the filename pattern
            widget.filename_pattern_edit.setText("1")
            qtbot.wait(50)  # Wait for signal to be processed
            # Trigger the textChanged signal manually
            widget._on_setting_changed("output_filename_pattern", 1)
            mock_set.assert_called_with('output_filename_pattern', 1)
        
        # Test subdirectory checkbox change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.subdir_checkbox.setChecked(False)
            qtbot.wait(50)  # Wait for signal to be processed
            mock_set.assert_called_with('output_create_subdirectory', False)
    
    def test_output_settings_persist_on_app_restart(self, qtbot, tmp_path):
        """Test that settings are saved and loaded correctly"""
        settings_file = tmp_path / "settings.json"
        
        # Create a real SettingsManager with initial settings
        from src.abb.services.settings_manager import SettingsManager
        initial_settings = {
            'output_bitrate': 96,
            'output_channels': 1,
            'output_create_subdirectory': False,
            'output_filename_pattern': 2,
            'output_directory': '/custom/output',
            'output_sample_rate': 44100
        }
        
        # First controller instance - set up settings
        settings_manager1 = SettingsManager(str(settings_file), initial_settings)
        controller1 = MainController(settings_manager=settings_manager1)
        
        # Verify settings are correctly set
        assert controller1._settings_manager.get_setting('output_bitrate') == 96
        assert controller1._settings_manager.get_setting('output_channels') == 1
        assert controller1._settings_manager.get_setting('output_create_subdirectory') == False
        assert controller1._settings_manager.get_setting('output_filename_pattern') == 2
        
        # Change a setting
        controller1._settings_manager.set_setting('output_bitrate', 128)
        
        # Create a second controller instance - should load from the same file
        settings_manager2 = SettingsManager(str(settings_file))
        controller2 = MainController(settings_manager=settings_manager2)
        
        # Verify the changed setting was persisted
        assert controller2._settings_manager.get_setting('output_bitrate') == 128
        assert controller2._settings_manager.get_setting('output_channels') == 1  # Unchanged
        assert controller2._settings_manager.get_setting('output_create_subdirectory') == False  # Unchanged
        assert controller2._settings_manager.get_setting('output_filename_pattern') == 2  # Unchanged

def test_settings_manager_persistence():
    """Ensure settings actually persist to disk"""
    # Use a temporary file for testing
    fd, temp_path = tempfile.mkstemp(suffix=".json")
    os.close(fd) # Close the file descriptor

    try:
        from src.abb.services.settings_manager import SettingsManager # Adjust import if necessary
        manager1 = SettingsManager(temp_path)
        manager1.set_setting("test_key", "test_value")
        
        # Create new instance, which should load from the same temp_path
        manager2 = SettingsManager(temp_path)
        assert manager2.get_setting("test_key") == "test_value"
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)