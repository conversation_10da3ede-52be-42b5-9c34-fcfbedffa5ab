import pytest
from pathlib import Path
from unittest.mock import patch
from PySide6.QtCore import Qt, QUrl, QMimeData, QPoint
from PySide6.QtGui import QDropEvent
from PySide6.QtTest import QSignalSpy

from src.abb.main_window import MainWindow


class TestFileImportFeature:
    """
    Feature: Audio File Import
    As an audiobook creator
    I want to import audio files
    So that I can process them into an audiobook
    """
    
    @pytest.fixture
    def main_window(self, qtbot, monkeypatch):
        monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
        window = MainWindow()
        qtbot.addWidget(window)
        return window
    
    def test_import_files_via_drag_and_drop(self, main_window, tmp_path, qtbot):
        """User can drag and drop audio files to import them"""
        # Create test files
        mp3_file = tmp_path / "chapter1.mp3"
        mp3_file.write_text("dummy")
        
        # Simulate drag and drop
        mime_data = QMimeData()
        mime_data.setUrls([QUrl.fromLocalFile(str(mp3_file))])
        
        drop_event = QDropEvent(
            QPoint(50, 50), Qt.CopyAction, mime_data,
            Qt.LeftButton, Qt.NoModifier
        )
        
        # Show the main window to ensure all children are visible
        main_window.show()
        qtbot.waitExposed(main_window)
        drop_zone = main_window.left_panel_widget.drop_zone
        assert drop_zone.isVisible()
        assert drop_zone.acceptDrops()
        
        # Patch QFileDialog to prevent dialog from opening if triggered
        with patch('PySide6.QtWidgets.QFileDialog.getOpenFileNames') as mock_dialog:
            with qtbot.waitSignal(main_window._controller.file_list_updated_signal):
                drop_zone.dropEvent(drop_event)
            # Optionally, assert the dialog was not called (drop path worked)
            assert not mock_dialog.called
        
        # Verify file appears in list
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 1
        assert file_list.item(0).text() == "chapter1.mp3"

    def test_import_files_via_add_button_triggers_dialog(self, main_window, qtbot):
        """User can click Add Files button to trigger import dialog handler"""
        with patch.object(main_window, '_on_add_files_requested') as mock_handler:
            main_window.left_panel_widget.add_files_button.click()
            mock_handler.assert_called_once()

    def test_import_files_via_controller(self, main_window, tmp_path, qtbot):
        """Files added via controller appear in UI"""
        # Create test files
        files = []
        for i in range(3):
            f = tmp_path / f"chapter{i}.mp3"
            f.write_text("dummy")
            files.append(str(f))
        
        # Add files directly via controller (simulating post-dialog selection)
        with qtbot.waitSignal(main_window._controller.file_list_updated_signal):
            main_window._controller.add_files(files)
        
        # Verify files appear
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 3

    def test_file_filtering_during_import(self, main_window, tmp_path, qtbot):
        """Only valid audio files are imported"""
        # Create mixed files
        valid = tmp_path / "audio.mp3"
        invalid = tmp_path / "image.jpg"
        valid.write_text("dummy")
        invalid.write_text("dummy")
        
        # Try to import both
        main_window._controller.add_files([str(valid), str(invalid)])
        qtbot.wait(100)
        
        # Only valid file should be imported
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 1
        assert file_list.item(0).text() == "audio.mp3"
