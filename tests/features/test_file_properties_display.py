import pytest
from unittest.mock import patch, MagicMock
from PySide6.QtTest import QSignalSpy

from src.abb.main_window import MainWindow


class TestFilePropertiesDisplay:
    """
    Feature: Display Audio File Properties
    As an audiobook creator
    I want to see properties of selected files
    So that I can verify audio quality before processing
    """
    
    @pytest.fixture
    def main_window(self, qtbot, monkeypatch):
        monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
        window = MainWindow()
        qtbot.addWidget(window)
        return window
    
    @pytest.fixture
    def sample_files_with_properties(self, tmp_path):
        """Create files and their expected properties"""
        files = []
        
        # File 1: High quality
        file1 = tmp_path / "chapter01.mp3"
        file1.write_bytes(b"dummy" * 1000)  # ~5KB
        files.append({
            'path': str(file1),
            'properties': {
                'bitrate': 320,
                'sample_rate': 48000,
                'channels': 2,
                'duration': 180.5,
                'file_size': file1.stat().st_size
            }
        })
        
        # File 2: Lower quality
        file2 = tmp_path / "chapter02.m4a"
        file2.write_bytes(b"dummy" * 500)   # ~2.5KB
        files.append({
            'path': str(file2),
            'properties': {
                'bitrate': 128,
                'sample_rate': 44100,
                'channels': 1,
                'duration': 240.0,
                'file_size': file2.stat().st_size
            }
        })
        
        return files
    
    def test_selecting_file_displays_properties(self, main_window, sample_files_with_properties, qtbot):
        """When user selects a file, its properties are displayed"""
        file_data = sample_files_with_properties[0]
        # Patch BEFORE adding files and selection
        with patch('src.abb.controllers.main_controller.get_audio_properties') as mock_get_props, \
             patch('src.abb.metadata_utils.mutagen.File') as mock_mutagen_file, \
             patch('src.abb.metadata_utils.ID3') as mock_id3:
            mock_get_props.return_value = (file_data['properties'], None)
            mock_mutagen_file.return_value = MagicMock(info=MagicMock(length=180.5))
            # Simulate ID3 tags for MP3
            mock_id3_instance = MagicMock()
            mock_id3_instance.__contains__.side_effect = lambda k: k in ["TIT2", "TPE1", "TALB"]
            mock_id3_instance.__getitem__.side_effect = lambda k: {
                "TIT2": "Test Title",
                "TPE1": "Test Artist",
                "TALB": "Test Album"
            }[k]
            mock_id3.return_value = mock_id3_instance
            # Add file
            main_window._controller.add_files([file_data['path']])
            qtbot.wait(100)
            # Simulate selection
            file_list = main_window.left_panel_widget.file_list_widget
            file_list.setCurrentRow(0)
            # Wait for the signal to propagate
            with qtbot.waitSignal(main_window._controller.selected_file_data_changed, timeout=1000):
                main_window.left_panel_widget._on_selection_changed()
            # Check properties
            left_panel = main_window.left_panel_widget
            assert str(file_data['properties']['bitrate']) in left_panel.bitrate_value_label.text()
    
    def test_no_file_selected_shows_empty_properties(self, main_window, qtbot):
        """When no file is selected, properties are cleared"""
        # Given - no files loaded
        # When - checking initial state
        left_panel = main_window.left_panel_widget
        
        # Then - properties are empty/default
        assert left_panel.duration_value_label.text() == ""
        assert left_panel.bitrate_value_label.text() == ""
        assert left_panel.sample_rate_value_label.text() == ""
        assert left_panel.channels_value_label.text() == ""
    
    def test_properties_display_through_controller_flow(self, main_window, sample_files_with_properties, qtbot):
        """Test the complete flow through controller"""
        file_data = sample_files_with_properties[0]
        
        # Mock at the metadata extraction level
        with patch('src.abb.metadata_utils.extract_metadata') as mock_extract:
            mock_extract.return_value = {
                'title': 'Test Title',
                'artist': 'Test Artist'
            }
            
            with patch('src.abb.ffmpeg_utils.get_audio_properties') as mock_get_props:
                mock_get_props.return_value = (file_data['properties'], None)
                
                # Add file
                main_window._controller.add_files([file_data['path']])
                qtbot.wait(100)
                
                # Select file - this should trigger the complete flow
                spy = QSignalSpy(main_window._controller.selected_file_data_changed)
                
                file_list = main_window.left_panel_widget.file_list_widget
                file_list.setCurrentRow(0)
                main_window.left_panel_widget._on_selection_changed()
                
                qtbot.wait(200)  # Give more time for async operations
                
                # The signal should have been emitted
                assert spy.count() > 0 