import pytest
from pathlib import Path

@pytest.mark.usefixtures("main_window_with_monkeypatch")
class TestCombinedSizeDisplay:
    """
    Feature: Display Combined File Size
    As an audiobook creator
    I want to see the total size of all files
    So that I can estimate the output file size
    """
    def test_combined_size_shows_on_file_add(self, main_window_with_monkeypatch, tmp_path, qtbot):
        file1 = tmp_path / "file1.mp3"
        file1.write_bytes(b"x" * 1024 * 1024)  # 1MB
        file2 = tmp_path / "file2.mp3"
        file2.write_bytes(b"x" * 1024 * 512)   # 512KB
        main_window_with_monkeypatch._controller.add_files([str(file1)])
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "1.0 MB" in size_text or "1024" in size_text
        main_window_with_monkeypatch._controller.add_files([str(file2)])
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "1.5 MB" in size_text or "1536" in size_text

    def test_combined_size_updates_on_file_removal(self, main_window_with_monkeypatch, tmp_path, qtbot):
        files = []
        for i in range(3):
            f = tmp_path / f"file{i}.mp3"
            f.write_bytes(b"x" * 1024 * 100 * (i + 1))
            files.append(str(f))
        main_window_with_monkeypatch._controller.add_files(files)
        qtbot.wait(100)
        main_window_with_monkeypatch._controller.remove_file(1)
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "400" in size_text or "0.4" in size_text

    def test_combined_size_formats_correctly(self, main_window_with_monkeypatch, tmp_path, qtbot):
        test_cases = [
            (500, "500 B"),
            (1024 * 2, "2.0 KB"),
            (1024 * 1024 * 5, "5.0 MB"),
            (1024 * 1024 * 1024 * 2, "2.00 GB")
        ]
        for size_bytes, expected_format in test_cases:
            main_window_with_monkeypatch._controller.clear_file_list()
            test_file = tmp_path / f"test_{size_bytes}.mp3"
            test_file.write_bytes(b"x" * size_bytes)
            main_window_with_monkeypatch._controller.add_files([str(test_file)])
            qtbot.wait(100)
            size_text = main_window_with_monkeypatch.combined_size_label.text()
            assert any(unit in size_text for unit in expected_format.split()) 