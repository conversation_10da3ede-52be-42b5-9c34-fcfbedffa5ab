[tool.pytest.ini_options]
pythonpath = ["src"]
testpaths = ["tests"]
addopts = "--cov"
qt_api = "pyside6"
markers = [
    "qt: marks tests that require Qt application (deselect with '-m \"not qt\"')",
    "fast: marks tests that run quickly (<100ms, no external dependencies)",
    "slow: marks tests that take longer (may require Qt, FFmpeg, or network)",
    "integration: marks tests that test multiple components together",
]

[tool.coverage.run]
source = ["src"]

[tool.coverage.report]
show_missing = true

[project]
name = "audiobook-boss"
description = "Audiobook management and processing application"
version = "0.1.0"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
requires-python = ">=3.8"
dependencies = [
    "Pillow"
]

[tool.ruff]
# Same as Black
line-length = 100
indent-width = 4

# Assume Python 3.8+
target-version = "py38"

[tool.ruff.lint]
# Enable pycodestyle (E), Pyflakes (F), isort (I), and other rules
select = ["E", "F", "I", "B", "D"]
ignore = []

# Allow unused variables when underscore-prefixed
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Use double quotes for strings
quote-style = "double"

# Indent with spaces
indent-style = "space"

# Enable auto-formatting
docstring-code-format = true
line-ending = "auto"

[tool.ruff.lint.isort]
known-first-party = ["abb"]

[tool.ruff.lint.pydocstyle]
convention = "google"
