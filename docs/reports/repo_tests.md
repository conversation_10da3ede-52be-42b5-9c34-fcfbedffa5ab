# Generated on: 2025-06-07 18:10:21

This file is a merged representation of a subset of the codebase, containing specifically included files and files not matching ignore patterns, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: tests/**/*.py
- Files matching these patterns are excluded: .html
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
tests/
  features/
    test_combined_size_display.py
    test_file_import.py
    test_file_properties_display.py
  integration/
    test_file_import_integration.py
    test_file_properties_integration.py
    test_golden.py
  unit/
    app_features/
      test_file_reordering.py
      test_output_settings.py
    controllers/
      test_main_controller_properties.py
      test_main_controller.py
    ffmpeg/
      test_command_builder.py
    services/
      core/
        __init__.py
        test_file_service_core.py
        test_metadata_handler_core.py
        test_processing_service_core.py
      test_file_service_combined_size.py
      test_file_service.py
      test_interfaces.py
      test_metadata_service.py
      test_path_service.py
      test_processing_service_start.py
      test_service_factory.py
      test_settings_manager.py
      test_unified_metadata_handler.py
    ui_widgets/
      test_coverart.py
      test_dropzone.py
      test_left_panel_widget.py
      test_metadata_form_widget.py
      test_output_settings_widget.py
      test_progress_integration.py
    workers/
      test_processing_worker_state.py
    test_ffmpeg_processing_command.py
    test_metadata_utils.py
    test_processing_validator.py
  conftest.py
  test_smoke.py
```

# Files

## File: tests/features/test_combined_size_display.py
```python
import pytest
from pathlib import Path
@pytest.mark.usefixtures("main_window_with_monkeypatch")
class TestCombinedSizeDisplay:
    def test_combined_size_shows_on_file_add(self, main_window_with_monkeypatch, tmp_path, qtbot):
        file1 = tmp_path / "file1.mp3"
        file1.write_bytes(b"x" * 1024 * 1024)
        file2 = tmp_path / "file2.mp3"
        file2.write_bytes(b"x" * 1024 * 512)
        main_window_with_monkeypatch._controller.add_files([str(file1)])
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "1.0 MB" in size_text or "1024" in size_text
        main_window_with_monkeypatch._controller.add_files([str(file2)])
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "1.5 MB" in size_text or "1536" in size_text
    def test_combined_size_updates_on_file_removal(self, main_window_with_monkeypatch, tmp_path, qtbot):
        files = []
        for i in range(3):
            f = tmp_path / f"file{i}.mp3"
            f.write_bytes(b"x" * 1024 * 100 * (i + 1))
            files.append(str(f))
        main_window_with_monkeypatch._controller.add_files(files)
        qtbot.wait(100)
        main_window_with_monkeypatch._controller.remove_file(1)
        qtbot.wait(100)
        size_text = main_window_with_monkeypatch.combined_size_label.text()
        assert "400" in size_text or "0.4" in size_text
    def test_combined_size_formats_correctly(self, main_window_with_monkeypatch, tmp_path, qtbot):
        test_cases = [
            (500, "500 B"),
            (1024 * 2, "2.0 KB"),
            (1024 * 1024 * 5, "5.0 MB"),
            (1024 * 1024 * 1024 * 2, "2.00 GB")
        ]
        for size_bytes, expected_format in test_cases:
            main_window_with_monkeypatch._controller.clear_file_list()
            test_file = tmp_path / f"test_{size_bytes}.mp3"
            test_file.write_bytes(b"x" * size_bytes)
            main_window_with_monkeypatch._controller.add_files([str(test_file)])
            qtbot.wait(100)
            size_text = main_window_with_monkeypatch.combined_size_label.text()
            assert any(unit in size_text for unit in expected_format.split())
```

## File: tests/integration/test_golden.py
```python
import hashlib
import json
import pathlib
import time
from unittest.mock import MagicMock, patch
import pytest
from PySide6.QtCore import QEventLoop, QTimer
GOLDEN_DATA_PATH = pathlib.Path(__file__).parent.parent / "data" / "golden"
GOLDEN_JSON_PATH = GOLDEN_DATA_PATH / "golden.json"
RAW_MP3_PATH = GOLDEN_DATA_PATH / "raw.mp3"
GOLDEN_M4B_PATH = GOLDEN_DATA_PATH / "golden.m4b"
@pytest.fixture
def golden_data():
    if not GOLDEN_JSON_PATH.exists():
        pytest.skip("Golden run data not found. Run the baseline generation first.")
    with open(GOLDEN_JSON_PATH, "r") as f:
        return json.load(f)
@pytest.fixture
def qt_application(qt_app):
    return qt_app
def calculate_sha256(file_path: pathlib.Path) -> str:
    sha256 = hashlib.sha256()
    with open(file_path, "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256.update(byte_block)
    return sha256.hexdigest()
@pytest.mark.fast
def test_golden_ffmpeg_command_generation_fast(golden_data):
    start_time = time.time()
    from src.abb.ffmpeg.command_builder import FFmpegCommandBuilder
    def mock_codec_check(codec_name):
        return codec_name == "libfdk_aac"
    builder = FFmpegCommandBuilder(mock_codec_check)
    cmd = builder.build_ffmpeg_command(
        input_files=[str(RAW_MP3_PATH)],
        output_file_full_path=str(GOLDEN_DATA_PATH / "test_output.m4b"),
        metadata=golden_data["metadata"],
        settings=golden_data["settings"],
        ffmpeg_exe_path="ffmpeg",
        ffprobe_exe_path=None
    )
    assert "ffmpeg" in cmd
    assert str(RAW_MP3_PATH) in cmd
    assert "-c:a" in cmd
    assert "libfdk_aac" in cmd
    assert "-b:a" in cmd
    assert "64k" in cmd
    assert "-ac" in cmd
    assert "1" in cmd
    assert "-metadata" in cmd
    assert "title=Golden Test Audiobook" in " ".join(cmd)
    assert "artist=Test Author" in " ".join(cmd)
    elapsed = time.time() - start_time
    assert elapsed < 0.1, f"Fast test took {elapsed:.3f}s, should be <0.1s"
@pytest.mark.slow
@pytest.mark.integration
def test_processing_service_with_golden_data_mocked(golden_data, tmp_path, qt_application):
    from src.abb.services.processing_service import ProcessingService
    with patch('src.abb.processing_worker.QProcess') as mock_qprocess_class:
        mock_process = MagicMock()
        mock_qprocess_class.return_value = mock_process
        mock_process.state.return_value = MagicMock(return_value=0)
        mock_process.exitCode.return_value = 0
        mock_process.exitStatus.return_value = 0
        processor = ProcessingService()
        finished_signal_received = []
        error_signal_received = []
        try:
            processor.finished.connect(lambda path: finished_signal_received.append(path))
            processor.error.connect(lambda msg: error_signal_received.append(msg))
            processor.process_full(
                input_files=[str(RAW_MP3_PATH)],
                output_path=str(tmp_path),
                output_filename="test_output.m4b",
                metadata=golden_data["metadata"],
                settings=golden_data["settings"]
            )
            assert mock_process.start.called, "FFmpeg process should have been started"
        finally:
            try:
                processor.cleanup_worker_resources()
            except Exception as e:
                print(f"Warning: Cleanup error: {e}")
@pytest.mark.slow
@pytest.mark.integration
@pytest.mark.skip(reason="Requires actual FFmpeg execution - enable for full integration test")
def test_refactored_output_matches_golden_hash(golden_data, tmp_path, qt_application):
    from src.abb.services.processing_service import ProcessingService
    # Define the output path for the new conversion
    output_file = tmp_path / "test_output.m4b"
    # Create processor
    processor = ProcessingService()
    # Set up event loop to wait for processing
    loop = QEventLoop()
    processing_finished = False
    processing_error = None
    def on_finished(path):
        nonlocal processing_finished
        processing_finished = True
        loop.quit()
    def on_error(msg):
        nonlocal processing_error
        processing_error = msg
        loop.quit()
    try:
        processor.finished.connect(on_finished)
        processor.error.connect(on_error)
        # Start processing
        processor.process_full(
            input_files=[str(RAW_MP3_PATH)],
            output_path=str(tmp_path),
            output_filename="test_output.m4b",
            metadata=golden_data["metadata"],
            settings=golden_data["settings"]
        )
        # Set timeout (60 seconds for actual FFmpeg processing)
        timeout_timer = QTimer()
        timeout_timer.setSingleShot(True)
        timeout_timer.timeout.connect(loop.quit)
        timeout_timer.start(60000)
        # Wait for completion
        loop.exec()
        # Check for timeout
        if not processing_finished and processing_error is None:
            pytest.fail("Processing timed out after 60 seconds")
        # Check for processing error
        if processing_error:
            pytest.fail(f"Processing failed with error: {processing_error}")
        # Verify output exists
        assert output_file.exists(), f"Output file {output_file} was not created"
        # Calculate the hash of the newly generated file
        new_hash = calculate_sha256(output_file)
        # Get the golden hash from our fixture
        golden_hash = golden_data["sha256"]
        # Assert that the hashes are identical
        assert new_hash == golden_hash, f"Hash mismatch: got {new_hash}, expected {golden_hash}"
    finally:
        # Always clean up resources
        try:
            processor.cleanup_worker_resources()
        except Exception as e:
            # Log cleanup error but don't fail test
            print(f"Warning: Cleanup error: {e}")
```

## File: tests/unit/app_features/test_file_reordering.py
```python
import pytest
from unittest.mock import Mock, MagicMock, patch
from PySide6.QtWidgets import QApplication, QListWidget
from PySide6.QtCore import Qt
from PySide6.QtGui import QDropEvent, QDragEnterEvent, QDragMoveEvent
from PySide6.QtTest import QTest
from src.abb.ui.widgets.left_panel_widget import LeftPanelWidget
from src.abb.controllers.main_controller import MainController
from src.abb.services.file_service import FileService
class TestFileReorderingBehavior:
    @pytest.fixture
    def qt_app(self):
        if not QApplication.instance():
            return QApplication([])
        return QApplication.instance()
    @pytest.fixture
    def mock_controller(self):
        controller = Mock(spec=MainController)
        controller.reorder_files = Mock()
        return controller
    @pytest.fixture
    def mock_file_service(self):
        service = Mock(spec=FileService)
        service.reorder_files = Mock()
        service.get_files = Mock(return_value=[
            "/path/to/file1.mp3",
            "/path/to/file2.mp3",
            "/path/to/file3.mp3"
        ])
        return service
    @pytest.fixture
    def left_panel_widget(self, qt_app):
        widget = LeftPanelWidget()
        test_files = [
            "/path/to/file1.mp3",
            "/path/to/file2.mp3",
            "/path/to/file3.mp3"
        ]
        widget.update_file_list_display(test_files)
        return widget
    def test_reorder_files_in_list_updates_controller_and_ui(
        self, qt_app, left_panel_widget, mock_controller, mock_file_service
    ):
        initial_files = [
            "/path/to/file1.mp3",
            "/path/to/file2.mp3",
            "/path/to/file3.mp3"
        ]
        left_panel_widget.update_file_list_display(initial_files)
        assert left_panel_widget.file_list_widget.count() == 3
        assert left_panel_widget.file_list_widget.item(0).text() == "file1.mp3"
        assert left_panel_widget.file_list_widget.item(1).text() == "file2.mp3"
        assert left_panel_widget.file_list_widget.item(2).text() == "file3.mp3"
        files_reordered_signal_emitted = []
        def capture_reordered_signal(new_order):
            files_reordered_signal_emitted.append(new_order)
        left_panel_widget.files_reordered_signal.connect(capture_reordered_signal)
        list_widget = left_panel_widget.file_list_widget
        item_to_move = list_widget.takeItem(0)
        list_widget.insertItem(2, item_to_move)
        left_panel_widget._handle_files_reordered()
        assert len(files_reordered_signal_emitted) == 1
        expected_new_order = ["file2.mp3", "file3.mp3", "file1.mp3"]
        assert files_reordered_signal_emitted[0] == expected_new_order
        assert list_widget.item(0).text() == "file2.mp3"
        assert list_widget.item(1).text() == "file3.mp3"
        assert list_widget.item(2).text() == "file1.mp3"
        assert list_widget.dragDropMode() == QListWidget.InternalMove
        assert list_widget.defaultDropAction() == Qt.MoveAction
        assert list_widget.dragEnabled() is True
        assert list_widget.acceptDrops() is True
        # The actual connection between LeftPanelWidget and MainController is done in MainWindow.
        # This test focuses on the signal emission from LeftPanelWidget and the expected
        # signature of MainController.reorder_files.
        # The previous assertion about 'src' and 'dst' parameters was based on an
        # outdated understanding of the MainController's reorder_files signature.
        pass
    def test_drag_drop_widget_configuration(self, qt_app, left_panel_widget):
        list_widget = left_panel_widget.file_list_widget
        assert list_widget.dragDropMode() == QListWidget.InternalMove
        assert list_widget.defaultDropAction() == Qt.MoveAction
        assert list_widget.dragEnabled() is True
        assert list_widget.acceptDrops() is True
        assert hasattr(list_widget, 'dropEvent')
        from src.abb.ui.widgets.left_panel_widget import DraggableListWidget
        assert isinstance(list_widget, DraggableListWidget)
    def test_files_reordered_signal_exists(self, qt_app, left_panel_widget):
        assert hasattr(left_panel_widget, 'files_reordered_signal')
        assert callable(getattr(left_panel_widget.files_reordered_signal, 'emit', None))
        assert callable(getattr(left_panel_widget.files_reordered_signal, 'connect', None))
    def test_handle_files_reordered_method_exists(self, qt_app, left_panel_widget):
        assert hasattr(left_panel_widget, '_handle_files_reordered')
        assert callable(left_panel_widget._handle_files_reordered)
    @patch('src.abb.controllers.main_controller.MainController')
    def test_controller_reorder_files_method_exists(self, mock_controller_class):
        controller = mock_controller_class.return_value
        assert hasattr(controller, 'reorder_files')
        assert callable(controller.reorder_files)
    @patch('src.abb.services.file_service.FileService')
    def test_file_service_reorder_files_method_exists(self, mock_file_service_class):
        service = mock_file_service_class.return_value
        assert hasattr(service, 'reorder_files')
        assert callable(service.reorder_files)
```

## File: tests/unit/services/core/__init__.py
```python

```

## File: tests/unit/services/core/test_file_service_core.py
```python
import os
import threading
from pathlib import Path
from typing import Any, Callable, List, Optional
from unittest.mock import patch
import pytest
from src.abb.services.core.file_service_core import FileServiceCore
from src.abb.services.interfaces import ErrorCategory, ServiceError
class CallbackCapture:
    def __init__(self):
        self.success_calls: List[Any] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()
    def success_callback(self, *args, **kwargs):
        with self._lock:
            self.success_calls.append((args, kwargs))
            self.call_count += 1
    def error_callback(self, error_msg: str):
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1
    def reset(self):
        with self._lock:
            self.success_calls.clear()
            self.error_calls.clear()
            self.call_count = 0
    @property
    def has_success_calls(self) -> bool:
        return len(self.success_calls) > 0
    @property
    def has_error_calls(self) -> bool:
        return len(self.error_calls) > 0
    @property
    def last_success_args(self) -> Optional[tuple]:
        return self.success_calls[-1][0] if self.success_calls else None
    @property
    def last_error_message(self) -> Optional[str]:
        return self.error_calls[-1] if self.error_calls else None
class ThreadSafetyTester:
    @staticmethod
    def run_concurrent_operations(operations: List[Callable], num_threads: int = 5) -> List[Any]:
        results = []
        exceptions = []
        threads = []
        def worker(operation, result_list, exception_list):
            try:
                result = operation()
                result_list.append(result)
            except Exception as e:
                exception_list.append(e)
        for operation in operations:
            thread = threading.Thread(target=worker, args=(operation, results, exceptions))
            threads.append(thread)
            thread.start()
        for thread in threads:
            thread.join()
        if exceptions:
            raise exceptions[0]
        return results
class TempAudioFiles:
    def __init__(self, tmp_path: Path):
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []
    def create_mp3_file(self, name: str = "test.mp3", size_bytes: int = 1024) -> Path:
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ID3" + b"\x00" * (size_bytes - 3))
        self.created_files.append(file_path)
        return file_path
    def create_m4a_file(self, name: str = "test.m4a", size_bytes: int = 1024) -> Path:
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ftyp" + b"\x00" * (size_bytes - 4))
        self.created_files.append(file_path)
        return file_path
    def create_invalid_file(self, name: str = "test.txt", size_bytes: int = 100) -> Path:
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"invalid" + b"\x00" * (size_bytes - 7))
        self.created_files.append(file_path)
        return file_path
    def create_multiple_files(self, count: int = 3, prefix: str = "test") -> List[Path]:
        files = []
        for i in range(count):
            if i % 2 == 0:
                file_path = self.create_mp3_file(f"{prefix}_{i}.mp3", 1024 * (i + 1))
            else:
                file_path = self.create_m4a_file(f"{prefix}_{i}.m4a", 1024 * (i + 1))
            files.append(file_path)
        return files
class TestFileServiceCore:
    @pytest.fixture
    def service(self):
        return FileServiceCore()
    @pytest.fixture
    def callback_capture(self):
        return CallbackCapture()
    @pytest.fixture
    def temp_audio_files(self, tmp_path):
        return TempAudioFiles(tmp_path)
    def test_initialization(self, service):
        assert service.get_files() == []
        assert service.get_file_count() == 0
        assert service.get_combined_size() == 0
        assert service.format_combined_size() == "0 B"
    def test_add_files_validates_audio_formats(self, service, callback_capture, temp_audio_files):
        mp3_file = temp_audio_files.create_mp3_file("test.mp3")
        m4a_file = temp_audio_files.create_m4a_file("test.m4a")
        m4b_file = temp_audio_files.create_m4a_file("test.m4b")
        aac_file = temp_audio_files.create_m4a_file("test.aac")
        invalid_file = temp_audio_files.create_invalid_file("test.txt")
        valid_files = [str(mp3_file), str(m4a_file), str(m4b_file), str(aac_file)]
        service.add_files(
            valid_files, callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 4
        assert service.get_file_count() == 4
        callback_capture.reset()
        service.add_files(
            [str(invalid_file)], callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0
        assert callback_capture.has_error_calls
        assert "Invalid audio file format" in callback_capture.last_error_message
    def test_get_combined_size_accuracy(self, service, callback_capture, temp_audio_files):
        file1 = temp_audio_files.create_mp3_file("file1.mp3", 1000)
        file2 = temp_audio_files.create_m4a_file("file2.m4a", 2000)
        file3 = temp_audio_files.create_mp3_file("file3.mp3", 3000)
        service.add_files(
            [str(file1), str(file2), str(file3)],
            callback_capture.success_callback,
            callback_capture.error_callback,
        )
        expected_size = 1000 + 2000 + 3000
        assert service.get_combined_size() == expected_size
        formatted = service.format_combined_size()
        assert "KB" in formatted or "B" in formatted
    def test_reorder_files_maintains_integrity(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(3, "reorder_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        original_order = service.get_files()
        new_order = [original_order[2], original_order[0], original_order[1]]
        callback_capture.reset()
        service.reorder_files(
            new_order, callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_files() == new_order
        assert service.get_file_count() == 3
    def test_thread_safety_concurrent_operations(self, service, temp_audio_files):
        files = temp_audio_files.create_multiple_files(10, "concurrent_test")
        file_paths = [str(f) for f in files]
        def add_operation():
            capture = CallbackCapture()
            service.add_files(file_paths[:5], capture.success_callback, capture.error_callback)
            return capture
        def size_operation():
            return service.get_combined_size()
        def count_operation():
            return service.get_file_count()
        operations = [add_operation, size_operation, count_operation] * 3
        results = ThreadSafetyTester.run_concurrent_operations(operations)
        assert len(results) == 9
        final_count = service.get_file_count()
        assert final_count >= 0
        assert service.get_combined_size() >= 0
    def test_callback_invocation_patterns(self, service, callback_capture, temp_audio_files):
        valid_file = temp_audio_files.create_mp3_file("valid.mp3")
        service.add_files(
            [str(valid_file)], callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.call_count == 1
        callback_capture.reset()
        service.add_files(
            ["/non/existent/file.mp3"],
            callback_capture.success_callback,
            callback_capture.error_callback,
        )
        assert callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "File not found" in callback_capture.last_error_message
    def test_remove_file_by_index(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(3, "remove_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        assert service.get_file_count() == 3
        original_files = service.get_files()
        callback_capture.reset()
        service.remove_file(1, callback_capture.success_callback, callback_capture.error_callback)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_file_count() == 2
        remaining_files = service.get_files()
        assert original_files[0] in remaining_files
        assert original_files[1] not in remaining_files
        assert original_files[2] in remaining_files
    def test_remove_file_invalid_index(self, service, callback_capture, temp_audio_files):
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        service.add_files([str(file1)], callback_capture.success_callback)
        callback_capture.reset()
        service.remove_file(5, callback_capture.success_callback, callback_capture.error_callback)
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Invalid index" in callback_capture.last_error_message
        assert service.get_file_count() == 1
    def test_file_validation_and_normalization(self, service, callback_capture, temp_audio_files):
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        relative_path = os.path.relpath(str(file1))
        service.add_files(
            [relative_path], callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        added_files = service.get_files()
        assert len(added_files) == 1
        assert os.path.isabs(added_files[0])
    def test_duplicate_file_handling(self, service, callback_capture, temp_audio_files):
        file1 = temp_audio_files.create_mp3_file("test.mp3")
        file_path = str(file1)
        service.add_files([file_path], callback_capture.success_callback)
        assert service.get_file_count() == 1
        callback_capture.reset()
        service.add_files([file_path], callback_capture.success_callback)
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0
        assert service.get_file_count() == 1
    def test_clear_files(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(3, "clear_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        assert service.get_file_count() == 3
        callback_capture.reset()
        service.clear_files(callback_capture.success_callback, callback_capture.error_callback)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert service.get_file_count() == 0
        assert service.get_files() == []
        assert service.get_combined_size() == 0
    def test_validate_files_removes_missing(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(3, "validate_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        assert service.get_file_count() == 3
        files[1].unlink()
        callback_capture.reset()
        service.validate_files(callback_capture.success_callback, callback_capture.error_callback)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        removed_files = callback_capture.last_success_args[0]
        assert len(removed_files) == 1
        assert str(files[1]) in removed_files
        assert service.get_file_count() == 2
    def test_reorder_files_validation_error(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(2, "reorder_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        invalid_order = ["/some/other/file.mp3", str(files[0])]
        callback_capture.reset()
        service.reorder_files(
            invalid_order, callback_capture.success_callback, callback_capture.error_callback
        )
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "must contain exactly the same files" in callback_capture.last_error_message
    def test_format_combined_size_units(self, service, callback_capture, temp_audio_files):
        small_file = temp_audio_files.create_mp3_file("small.mp3", 500)
        service.add_files([str(small_file)], callback_capture.success_callback)
        assert "B" in service.format_combined_size()
        service.clear_files(callback_capture.success_callback)
        kb_file = temp_audio_files.create_mp3_file("kb.mp3", 2048)
        service.add_files([str(kb_file)], callback_capture.success_callback)
        formatted = service.format_combined_size()
        assert "KB" in formatted
        service.clear_files(callback_capture.success_callback)
        mb_file = temp_audio_files.create_mp3_file("mb.mp3", 2 * 1024 * 1024)
        service.add_files([str(mb_file)], callback_capture.success_callback)
        formatted = service.format_combined_size()
        assert "MB" in formatted
    def test_error_categorization(self, service, callback_capture):
        service.add_files(["/non/existent/file.mp3"], callback_capture.success_callback)
        # Should succeed with empty list (no files added)
        assert callback_capture.has_success_calls
        assert len(callback_capture.last_success_args[0]) == 0
        # Test remove with invalid index - should raise ServiceError
        with pytest.raises(ServiceError) as exc_info:
            service.remove_file(999, callback_capture.success_callback)
        assert exc_info.value.category == ErrorCategory.PROCESSING_ERROR
        # Test path normalization error - should raise ServiceError
        with pytest.raises(ServiceError) as exc_info:
            service._normalize_path("\x00invalid\x00path")
        assert exc_info.value.category == ErrorCategory.VALIDATION_ERROR
    def test_concurrent_add_remove_operations(self, service, temp_audio_files):
        # Create files for testing
        files = temp_audio_files.create_multiple_files(5, "concurrent_add_remove")
        file_paths = [str(f) for f in files]
        # Add initial files
        capture = CallbackCapture()
        service.add_files(file_paths[:3], capture.success_callback)
        def add_more_files():
            capture = CallbackCapture()
            service.add_files(file_paths[3:], capture.success_callback, capture.error_callback)
            return capture
        def remove_file():
            capture = CallbackCapture()
            try:
                service.remove_file(0, capture.success_callback, capture.error_callback)
            except:
                pass  # Ignore errors from concurrent access
            return capture
        def get_count():
            return service.get_file_count()
        # Run concurrent operations
        operations = [add_more_files, remove_file, get_count] * 2
        ThreadSafetyTester.run_concurrent_operations(operations)
        # Verify service is in a consistent state
        final_count = service.get_file_count()
        assert final_count >= 0
        assert len(service.get_files()) == final_count
    def test_file_access_errors_during_add(self, service, callback_capture, temp_audio_files):
        # Create a file and then make it inaccessible
        valid_file = temp_audio_files.create_mp3_file("accessible.mp3")
        # Test with a path that will cause OSError during normalization
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a path that's too long or has invalid characters
            invalid_path = os.path.join(temp_dir, "a" * 300 + ".mp3")
            service.add_files(
                [str(valid_file), invalid_path],
                callback_capture.success_callback,
                callback_capture.error_callback,
            )
            assert callback_capture.has_success_calls
            assert len(callback_capture.last_success_args[0]) == 1
            assert callback_capture.has_error_calls
    def test_get_combined_size_with_inaccessible_files(
        self, service, callback_capture, temp_audio_files
    ):
        files = temp_audio_files.create_multiple_files(2, "size_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        files[0].unlink()
        size = service.get_combined_size()
        assert size >= 0
    def test_clear_files_exception_handling(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(2, "clear_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        with patch.object(service, "_files") as mock_files:
            mock_files.clear.side_effect = RuntimeError("Mock clear error")
            callback_capture.reset()
            service.clear_files(callback_capture.success_callback, callback_capture.error_callback)
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to clear files" in callback_capture.last_error_message
    def test_validate_files_exception_handling(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(2, "validate_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        with patch("os.path.exists") as mock_exists:
            mock_exists.side_effect = RuntimeError("Mock validation error")
            callback_capture.reset()
            service.validate_files(
                callback_capture.success_callback, callback_capture.error_callback
            )
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to validate files" in callback_capture.last_error_message
    def test_format_combined_size_gb_range(self, service, callback_capture, temp_audio_files):
        file1 = temp_audio_files.create_mp3_file("large.mp3", 2 * 1024 * 1024 * 1024)
        service.add_files([str(file1)], callback_capture.success_callback)
        formatted = service.format_combined_size()
        assert "GB" in formatted
    def test_is_valid_audio_file_exception_handling(self, service):
        result = service._is_valid_audio_file("")
        assert result is False
        result = service._is_valid_audio_file("no_extension")
        assert result is False
    def test_add_files_unexpected_exception_handling(
        self, service, callback_capture, temp_audio_files
    ):
        valid_file = temp_audio_files.create_mp3_file("test.mp3")
        with patch.object(service, "_normalize_path") as mock_normalize:
            mock_normalize.side_effect = [str(valid_file), RuntimeError("Unexpected error")]
            service.add_files(
                [str(valid_file), "another_file.mp3"],
                callback_capture.success_callback,
                callback_capture.error_callback,
            )
            assert callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Unexpected error processing" in callback_capture.last_error_message
    def test_validate_files_with_access_errors(self, service, callback_capture, temp_audio_files):
        files = temp_audio_files.create_multiple_files(2, "access_error_test")
        file_paths = [str(f) for f in files]
        service.add_files(file_paths, callback_capture.success_callback)
        def mock_access(path, mode):
            if "access_error_test_0" in path:
                raise OSError("Access denied")
            return True
        with patch("os.access", side_effect=mock_access):
            callback_capture.reset()
            service.validate_files(
                callback_capture.success_callback, callback_capture.error_callback
            )
            assert callback_capture.has_success_calls
            removed_files = callback_capture.last_success_args[0]
            assert len(removed_files) == 1
            assert "access_error_test_0" in removed_files[0]
```

## File: tests/unit/services/core/test_metadata_handler_core.py
```python
import threading
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional
from unittest.mock import patch
import pytest
from src.abb.services.core.metadata_handler_core import MetadataHandlerCore
class CallbackCapture:
    def __init__(self):
        self.success_calls: List[Any] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()
    def success_callback(self, *args, **kwargs):
        with self._lock:
            self.success_calls.append((args, kwargs))
            self.call_count += 1
    def error_callback(self, error_msg: str):
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1
    def reset(self):
        with self._lock:
            self.success_calls.clear()
            self.error_calls.clear()
            self.call_count = 0
    @property
    def has_success_calls(self) -> bool:
        return len(self.success_calls) > 0
    @property
    def has_error_calls(self) -> bool:
        return len(self.error_calls) > 0
    @property
    def last_success_args(self) -> Optional[tuple]:
        return self.success_calls[-1][0] if self.success_calls else None
    @property
    def last_error_message(self) -> Optional[str]:
        return self.error_calls[-1] if self.error_calls else None
class ThreadSafetyTester:
    @staticmethod
    def run_concurrent_operations(operations: List[Callable], num_threads: int = 5) -> List[Any]:
        results = []
        exceptions = []
        threads = []
        def worker(operation, result_list, exception_list):
            try:
                result = operation()
                result_list.append(result)
            except Exception as e:
                exception_list.append(e)
        for operation in operations:
            thread = threading.Thread(target=worker, args=(operation, results, exceptions))
            threads.append(thread)
            thread.start()
        for thread in threads:
            thread.join()
        if exceptions:
            raise exceptions[0]
        return results
class TempCoverArtFiles:
    def __init__(self, tmp_path: Path):
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []
    def create_jpg_file(self, name: str = "cover.jpg", size_bytes: int = 1024) -> Path:
        file_path = self.tmp_path / name
        jpeg_header = b"\xff\xd8\xff\xe0\x00\x10JFIF"
        with open(file_path, "wb") as f:
            f.write(jpeg_header + b"\x00" * (size_bytes - len(jpeg_header)))
        self.created_files.append(file_path)
        return file_path
    def create_png_file(self, name: str = "cover.png", size_bytes: int = 1024) -> Path:
        file_path = self.tmp_path / name
        png_header = b"\x89PNG\r\n\x1a\n"
        with open(file_path, "wb") as f:
            f.write(png_header + b"\x00" * (size_bytes - len(png_header)))
        self.created_files.append(file_path)
        return file_path
    def create_invalid_image(self, name: str = "invalid.jpg", size_bytes: int = 100) -> Path:
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"invalid_image_data" + b"\x00" * (size_bytes - 18))
        self.created_files.append(file_path)
        return file_path
def create_test_metadata() -> Dict[str, Any]:
    return {
        "title": "Test Audiobook",
        "artist": "Test Author",
        "narrator": "Test Narrator",
        "genre": "Fiction",
        "year": "2023",
        "description": "A test audiobook for unit testing",
        "publisher": "Test Publisher",
        "series": "Test Series",
        "series_pos": "1",
        "album": "Test Album",
    }
class TestMetadataHandlerCore:
    @pytest.fixture
    def handler(self):
        return MetadataHandlerCore()
    @pytest.fixture
    def callback_capture(self):
        return CallbackCapture()
    @pytest.fixture
    def temp_cover_files(self, tmp_path):
        return TempCoverArtFiles(tmp_path)
    def test_initialization(self, handler):
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
        assert not handler.has_metadata()
    def test_update_field_modifies_state_correctly(self, handler, callback_capture):
        handler.update_field(
            "title", "New Title", callback_capture.success_callback, callback_capture.error_callback
        )
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        metadata = handler.get_current_metadata()
        assert metadata["title"] == "New Title"
        assert handler.has_metadata()
        callback_metadata = callback_capture.last_success_args[0]
        assert callback_metadata["title"] == "New Title"
    def test_get_for_ffmpeg_format_conversion(self, handler, callback_capture):
        test_metadata = create_test_metadata()
        for field, value in test_metadata.items():
            handler.update_field(field, value, callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["title"] == "Test Audiobook"
        assert ffmpeg_metadata["date"] == "2023"
        assert ffmpeg_metadata["genre"] == "Fiction"
        assert ffmpeg_metadata["composer"] == "Test Narrator"
        assert ffmpeg_metadata["mood"] == "Test Series"
        assert ffmpeg_metadata["track"] == "1"
        assert ffmpeg_metadata["description"] == "A test audiobook for unit testing"
        assert ffmpeg_metadata["artist"] == "Test Author"
        assert ffmpeg_metadata["album_artist"] == "Test Author"
    def test_clear_metadata_resets_state(self, handler, callback_capture):
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        assert handler.has_metadata()
        callback_capture.reset()
        handler.clear_metadata(callback_capture.success_callback, callback_capture.error_callback)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
        assert not handler.has_metadata()
    def test_set_cover_art_validates_and_stores(self, handler, callback_capture, temp_cover_files):
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        handler.set_cover_art(
            str(cover_file), callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.last_success_args[0] == str(cover_file)
        assert handler.get_cover_art_path() == str(cover_file)
        assert handler.get_cover_art_data() is not None
        assert len(handler.get_cover_art_data()) > 0
    def test_set_cover_art_clears_when_empty_path(
        self, handler, callback_capture, temp_cover_files
    ):
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        handler.set_cover_art(str(cover_file), callback_capture.success_callback)
        time.sleep(0.1)
        callback_capture.reset()
        handler.set_cover_art(
            "", callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.last_success_args[0] is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
    def test_set_cover_art_validates_file_format(self, handler, callback_capture, temp_cover_files):
        invalid_file = temp_cover_files.tmp_path / "invalid.txt"
        with open(invalid_file, "w") as f:
            f.write("This is not an image")
        handler.set_cover_art(
            str(invalid_file), callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Unsupported cover art format" in callback_capture.last_error_message
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
    def test_set_cover_art_handles_missing_file(self, handler, callback_capture):
        handler.set_cover_art(
            "/non/existent/file.jpg",
            callback_capture.success_callback,
            callback_capture.error_callback,
        )
        time.sleep(0.1)
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Cover art file not found" in callback_capture.last_error_message
    @patch("src.abb.services.core.metadata_handler_core.extract_metadata")
    @patch("src.abb.services.core.metadata_handler_core.apply_metadata_defaults")
    def test_load_from_file_extracts_all_metadata(
        self, mock_apply_defaults, mock_extract, handler, callback_capture, tmp_path
    ):
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")
        mock_metadata = create_test_metadata()
        mock_extract.return_value = mock_metadata
        mock_apply_defaults.return_value = mock_metadata
        handler.load_from_file(
            str(audio_file), callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)
        assert callback_capture.has_success_calls
        assert not callback_capture.has_error_calls
        loaded_metadata = callback_capture.last_success_args[0]
        assert loaded_metadata["title"] == "Test Audiobook"
        assert loaded_metadata["artist"] == "Test Author"
        assert handler.has_metadata()
        assert handler.get_current_file_path() == str(audio_file)
        assert handler.get_current_metadata() == mock_metadata
    def test_load_from_file_handles_missing_file(self, handler, callback_capture):
        handler.load_from_file(
            "/non/existent/file.mp3",
            callback_capture.success_callback,
            callback_capture.error_callback,
        )
        time.sleep(0.1)
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "File not found" in callback_capture.last_error_message
    @patch("src.abb.services.core.metadata_handler_core.extract_metadata")
    def test_load_from_file_handles_extraction_error(
        self, mock_extract, handler, callback_capture, tmp_path
    ):
        audio_file = tmp_path / "corrupt.mp3"
        audio_file.write_bytes(b"corrupt_data")
        mock_extract.side_effect = Exception("Extraction failed")
        handler.load_from_file(
            str(audio_file), callback_capture.success_callback, callback_capture.error_callback
        )
        time.sleep(0.1)
        assert not callback_capture.has_success_calls
        assert callback_capture.has_error_calls
        assert "Failed to load metadata" in callback_capture.last_error_message
    def test_get_field_returns_correct_values(self, handler, callback_capture):
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        handler.update_field("artist", "Test Artist", callback_capture.success_callback)
        assert handler.get_field("title") == "Test Title"
        assert handler.get_field("artist") == "Test Artist"
        assert handler.get_field("non_existent") is None
        assert handler.get_field("non_existent", "default_value") == "default_value"
    def test_reset_state_clears_everything(self, handler, callback_capture, temp_cover_files):
        handler.update_field("title", "Test Title", callback_capture.success_callback)
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        handler.set_cover_art(str(cover_file), callback_capture.success_callback)
        time.sleep(0.1)
        assert handler.has_metadata()
        assert handler.get_cover_art_path() is not None
        handler.reset_state()
        assert not handler.has_metadata()
        assert handler.get_current_metadata() == {}
        assert handler.get_current_file_path() is None
        assert handler.get_cover_art_path() is None
        assert handler.get_cover_art_data() is None
    def test_thread_safety_concurrent_operations(self, handler, callback_capture):
        def update_title():
            capture = CallbackCapture()
            handler.update_field(
                "title", "Concurrent Title", capture.success_callback, capture.error_callback
            )
            return capture
        def update_artist():
            capture = CallbackCapture()
            handler.update_field(
                "artist", "Concurrent Artist", capture.success_callback, capture.error_callback
            )
            return capture
        def get_metadata():
            return handler.get_current_metadata()
        def check_has_metadata():
            return handler.has_metadata()
        operations = [update_title, update_artist, get_metadata, check_has_metadata] * 3
        results = ThreadSafetyTester.run_concurrent_operations(operations)
        assert len(results) == 12
        final_metadata = handler.get_current_metadata()
        assert "title" in final_metadata or "artist" in final_metadata
    def test_ffmpeg_metadata_special_mappings(self, handler, callback_capture):
        handler.update_field("comment", "Test Comment", callback_capture.success_callback)
        handler.update_field("description", "Test Description", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["comment"] == "Test Comment"
        handler.update_field("comment", "", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["comment"] == "Test Description"
        handler.update_field("series_sort", "Series Sort Value", callback_capture.success_callback)
        handler.update_field("sort_album", "Album Sort Value", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["album_sort"] == "Series Sort Value"
    def test_update_field_error_handling(self, handler, callback_capture):
        with patch.object(handler, "_lock") as mock_lock:
            mock_lock.__enter__.side_effect = RuntimeError("Lock error")
            handler.update_field(
                "title",
                "Test Title",
                callback_capture.success_callback,
                callback_capture.error_callback,
            )
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to update field 'title'" in callback_capture.last_error_message
    def test_clear_metadata_error_handling(self, handler, callback_capture):
        with patch(
            "src.abb.services.core.metadata_handler_core.clear_metadata_cache"
        ) as mock_clear_cache:
            mock_clear_cache.side_effect = RuntimeError("Cache clear error")
            handler.clear_metadata(
                callback_capture.success_callback, callback_capture.error_callback
            )
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to clear metadata" in callback_capture.last_error_message
    def test_set_cover_art_read_error_handling(self, handler, callback_capture, temp_cover_files):
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        with patch("builtins.open", side_effect=IOError("Read error")):
            handler.set_cover_art(
                str(cover_file), callback_capture.success_callback, callback_capture.error_callback
            )
            time.sleep(0.1)
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to read cover art file" in callback_capture.last_error_message
    def test_get_metadata_mapping_class_method(self):
        mapping = MetadataHandlerCore.get_metadata_mapping()
        assert mapping["title"] == "title"
        assert mapping["year"] == "date"
        assert mapping["narrator"] == "composer"
        assert mapping["series"] == "mood"
        mapping["test"] = "test_value"
        original_mapping = MetadataHandlerCore.get_metadata_mapping()
        assert "test" not in original_mapping
    def test_ffmpeg_metadata_empty_values_filtered(self, handler, callback_capture):
        handler.update_field("title", "", callback_capture.success_callback)
        handler.update_field("artist", None, callback_capture.success_callback)
        handler.update_field("genre", "Fiction", callback_capture.success_callback)
        handler.update_field("year", "   ", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert "title" not in ffmpeg_metadata
        assert "artist" not in ffmpeg_metadata
        assert "date" not in ffmpeg_metadata
        assert ffmpeg_metadata["genre"] == "Fiction"
    def test_ffmpeg_metadata_sort_album_fallback(self, handler, callback_capture):
        handler.update_field("sort_album", "Album Sort Value", callback_capture.success_callback)
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert ffmpeg_metadata["album_sort"] == "Album Sort Value"
    def test_set_cover_art_general_exception_handling(
        self, handler, callback_capture, temp_cover_files
    ):
        cover_file = temp_cover_files.create_jpg_file("test_cover.jpg")
        with patch("src.abb.services.core.metadata_handler_core.Path") as mock_path:
            mock_path.side_effect = RuntimeError("Path error")
            handler.set_cover_art(
                str(cover_file), callback_capture.success_callback, callback_capture.error_callback
            )
            time.sleep(0.1)
            assert not callback_capture.has_success_calls
            assert callback_capture.has_error_calls
            assert "Failed to set cover art" in callback_capture.last_error_message
    @patch("src.abb.services.core.metadata_handler_core.extract_cover")
    def test_load_from_file_with_cover_art_extraction(
        self, mock_extract_cover, handler, callback_capture, tmp_path
    ):
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")
        mock_cover_data = b"fake_cover_data"
        mock_extract_cover.return_value = mock_cover_data
        with patch("src.abb.services.core.metadata_handler_core.extract_metadata") as mock_extract:
            with patch(
                "src.abb.services.core.metadata_handler_core.apply_metadata_defaults"
            ) as mock_apply_defaults:
                mock_metadata = create_test_metadata()
                mock_extract.return_value = mock_metadata
                mock_apply_defaults.return_value = mock_metadata
                handler.load_from_file(
                    str(audio_file),
                    callback_capture.success_callback,
                    callback_capture.error_callback,
                )
                time.sleep(0.1)
                assert handler.get_cover_art_data() == mock_cover_data
                assert handler.get_cover_art_path() == str(audio_file)
    @patch("src.abb.services.core.metadata_handler_core.extract_cover")
    def test_load_from_file_cover_art_extraction_failure(
        self, mock_extract_cover, handler, callback_capture, tmp_path
    ):
        audio_file = tmp_path / "test.mp3"
        audio_file.write_bytes(b"fake_mp3_data")
        mock_extract_cover.side_effect = Exception("Cover extraction failed")
        with patch("src.abb.services.core.metadata_handler_core.extract_metadata") as mock_extract:
            with patch(
                "src.abb.services.core.metadata_handler_core.apply_metadata_defaults"
            ) as mock_apply_defaults:
                mock_metadata = create_test_metadata()
                mock_extract.return_value = mock_metadata
                mock_apply_defaults.return_value = mock_metadata
                handler.load_from_file(
                    str(audio_file),
                    callback_capture.success_callback,
                    callback_capture.error_callback,
                )
                time.sleep(0.1)
                assert callback_capture.has_success_calls
                assert not callback_capture.has_error_calls
                assert handler.get_cover_art_data() is None
                assert handler.get_cover_art_path() is None
    def test_factory_function(self):
        from src.abb.services.core.metadata_handler_core import create_metadata_handler_core
        handler = create_metadata_handler_core()
        assert isinstance(handler, MetadataHandlerCore)
        assert handler.get_current_metadata() == {}
    def test_feature_flag_function(self):
        import os
        from src.abb.services.core.metadata_handler_core import is_core_metadata_enabled
        with patch.dict(os.environ, {}, clear=True):
            assert not is_core_metadata_enabled()
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            assert is_core_metadata_enabled()
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "TRUE"}):
            assert is_core_metadata_enabled()
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            assert not is_core_metadata_enabled()
```

## File: tests/unit/services/core/test_processing_service_core.py
```python
import os
import threading
import time
from pathlib import Path
from typing import Callable, List, Optional
from unittest.mock import Mock, patch
import pytest
from src.abb.services.core.path_service_core import PathServiceCore
from src.abb.services.core.processing_service_core import ProcessingServiceCore
class MockFFmpegProcess:
    def __init__(self, returncode: int = 0, stderr_output: str = "", stdout_output: str = ""):
        self.returncode = returncode
        self.stderr_output = stderr_output
        self.stdout_output = stdout_output
        self.terminated = False
        self.killed = False
        self._stderr_lines = stderr_output.split("\n") if stderr_output else []
        self._stderr_index = 0
        self.stderr = Mock()
        self.stdout = Mock()
        self.stderr.readline.side_effect = self._readline_stderr
    def _readline_stderr(self):
        if self._stderr_index < len(self._stderr_lines):
            line = self._stderr_lines[self._stderr_index] + "\n"
            self._stderr_index += 1
            return line
        return ""
    def poll(self):
        return self.returncode if self._stderr_index >= len(self._stderr_lines) else None
    def communicate(self):
        return self.stdout_output, self.stderr_output
    def terminate(self):
        self.terminated = True
        self.returncode = -15
    def kill(self):
        self.killed = True
        self.returncode = -9
    def wait(self, timeout=None):
        if timeout:
            time.sleep(min(0.1, timeout))
        return self.returncode
class FFmpegOutputSimulator:
    @staticmethod
    def create_progress_output(duration_seconds: float, progress_points: int = 10) -> str:
        lines = []
        lines.append("ffmpeg version 4.4.0 Copyright (c) 2000-2021 the FFmpeg developers")
        lines.append("built with gcc 9 (Ubuntu 9.3.0-17ubuntu1~20.04)")
        for i in range(progress_points):
            progress_time = (
                (duration_seconds * i) / (progress_points - 1) if progress_points > 1 else 0
            )
            hours = int(progress_time // 3600)
            minutes = int((progress_time % 3600) // 60)
            seconds = progress_time % 60
            lines.append(
                f"frame= {i * 100:4d} fps= 0.0 q=-1.0 size= {i * 1024:7d}kB "
                f"time={hours:02d}:{minutes:02d}:{seconds:06.3f} bitrate= 64.0kbits/s speed=1.0x"
            )
        lines.append(
            "video:0kB audio:12345kB subtitle:0kB other streams:0kB global headers:0kB muxing overhead: 0.123456%"
        )
        return "\n".join(lines)
    @staticmethod
    def create_error_output(error_message: str) -> str:
        return f"""ffmpeg version 4.4.0 Copyright (c) 2000-2021 the FFmpeg developers
built with gcc 9 (Ubuntu 9.3.0-17ubuntu1~20.04)
{error_message}: No such file or directory"""
class ProcessingCallbackCapture:
    def __init__(self):
        self.progress_calls: List[int] = []
        self.status_calls: List[str] = []
        self.complete_calls: List[str] = []
        self.error_calls: List[str] = []
        self.call_count = 0
        self._lock = threading.Lock()
    def progress_callback(self, progress: int):
        with self._lock:
            self.progress_calls.append(progress)
            self.call_count += 1
    def status_callback(self, status: str):
        with self._lock:
            self.status_calls.append(status)
            self.call_count += 1
    def complete_callback(self, output_file: str):
        with self._lock:
            self.complete_calls.append(output_file)
            self.call_count += 1
    def error_callback(self, error_msg: str):
        with self._lock:
            self.error_calls.append(error_msg)
            self.call_count += 1
    def reset(self):
        with self._lock:
            self.progress_calls.clear()
            self.status_calls.clear()
            self.complete_calls.clear()
            self.error_calls.clear()
            self.call_count = 0
    @property
    def has_progress_calls(self) -> bool:
        return len(self.progress_calls) > 0
    @property
    def has_status_calls(self) -> bool:
        return len(self.status_calls) > 0
    @property
    def has_complete_calls(self) -> bool:
        return len(self.complete_calls) > 0
    @property
    def has_error_calls(self) -> bool:
        return len(self.error_calls) > 0
    @property
    def last_progress(self) -> Optional[int]:
        return self.progress_calls[-1] if self.progress_calls else None
    @property
    def last_status(self) -> Optional[str]:
        return self.status_calls[-1] if self.status_calls else None
    @property
    def last_complete_file(self) -> Optional[str]:
        return self.complete_calls[-1] if self.complete_calls else None
    @property
    def last_error_message(self) -> Optional[str]:
        return self.error_calls[-1] if self.error_calls else None
class ThreadingTestHelper:
    @staticmethod
    def wait_for_condition(
        condition_func: Callable[[], bool], timeout: float = 5.0, interval: float = 0.1
    ) -> bool:
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            time.sleep(interval)
        return False
    @staticmethod
    def wait_for_thread_completion(thread: threading.Thread, timeout: float = 5.0) -> bool:
        thread.join(timeout)
        return not thread.is_alive()
class TempAudioFiles:
    def __init__(self, tmp_path: Path):
        self.tmp_path = tmp_path
        self.created_files: List[Path] = []
    def create_mp3_file(self, name: str = "test.mp3", size_bytes: int = 1024) -> Path:
        file_path = self.tmp_path / name
        with open(file_path, "wb") as f:
            f.write(b"ID3" + b"\x00" * (size_bytes - 3))
        self.created_files.append(file_path)
        return file_path
    def create_multiple_files(self, count: int = 3, prefix: str = "test") -> List[Path]:
        files = []
        for i in range(count):
            file_path = self.create_mp3_file(f"{prefix}_{i}.mp3", 1024 * (i + 1))
            files.append(file_path)
        return files
class TestProcessingServiceCore:
    @pytest.fixture
    def path_service(self):
        return PathServiceCore()
    @pytest.fixture
    def service(self, path_service):
        return ProcessingServiceCore(path_service)
    @pytest.fixture
    def callback_capture(self):
        return ProcessingCallbackCapture()
    @pytest.fixture
    def temp_audio_files(self, tmp_path):
        return TempAudioFiles(tmp_path)
    def test_initialization_with_valid_path_service(self, path_service):
        service = ProcessingServiceCore(path_service)
        assert service._path_service is path_service
        assert service._validator is not None
        assert service._lock is not None
        assert service._is_processing is False
        assert service._cancel_event is not None
        assert service._process is None
        assert service._processing_thread is None
        assert service._progress_thread is None
        assert service._temp_files == []
        assert service._output_file is None
        assert service._total_duration_seconds == 0.0
        assert service._ffmpeg_path is not None
    def test_is_processing_property(self, service):
        assert service.is_processing() is False
        with service._lock:
            service._is_processing = True
        assert service.is_processing() is True
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_path_detection(self, mock_get_path, path_service):
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        service = ProcessingServiceCore(path_service)
        assert service._ffmpeg_path == "/usr/bin/ffmpeg"
        mock_get_path.assert_called_once_with("ffmpeg")
    def test_process_prevents_concurrent_operations(
        self, service, callback_capture, temp_audio_files
    ):
        with service._lock:
            service._is_processing = True
        files = [str(temp_audio_files.create_mp3_file())]
        service.process(
            input_files=files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert callback_capture.has_error_calls
        assert "Processing already in progress" in callback_capture.last_error_message
    def test_process_preview_prevents_concurrent_operations(
        self, service, callback_capture, temp_audio_files
    ):
        with service._lock:
            service._is_processing = True
        input_file = str(temp_audio_files.create_mp3_file())
        service.process_preview(
            input_file=input_file,
            metadata={},
            settings={},
            duration_seconds=30,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert callback_capture.has_error_calls
        assert "Processing already in progress" in callback_capture.last_error_message
    def test_cancel_when_not_processing(self, service):
        success_called = threading.Event()
        def success_callback():
            success_called.set()
        service.cancel(success_callback)
        assert success_called.wait(timeout=1.0)
    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    @patch("src.abb.services.core.processing_service_core.get_duration")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    @patch("os.makedirs")
    def test_process_successful_completion(
        self,
        mock_makedirs,
        mock_get_path,
        mock_get_duration,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
        tmp_path,
    ):
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_get_duration.return_value = 120.0
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "output.m4b"]
        progress_output = FFmpegOutputSimulator.create_progress_output(120.0, 5)
        mock_process = MockFFmpegProcess(returncode=0, stderr_output=progress_output)
        mock_popen.return_value = mock_process
        input_files = [str(f) for f in temp_audio_files.create_multiple_files(2)]
        output_path = str(tmp_path / "output")
        output_filename = "test_audiobook.m4b"
        service.process(
            input_files=input_files,
            output_path=output_path,
            output_filename=output_filename,
            metadata={"title": "Test Book", "author": "Test Author"},
            settings={"bitrate": 64, "format": "m4b"},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_complete_calls or callback_capture.has_error_calls,
            timeout=5.0,
        )
        assert callback_capture.has_complete_calls
        assert not callback_capture.has_error_calls
        assert callback_capture.has_progress_calls
        assert callback_capture.has_status_calls
        assert callback_capture.last_progress == 100
        expected_output = os.path.join(output_path, output_filename)
        assert callback_capture.last_complete_file == expected_output
        mock_makedirs.assert_called_once_with(output_path, exist_ok=True)
        mock_build_command.assert_called_once()
        mock_popen.assert_called_once()
    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_preview_command")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_process_preview_successful_completion(
        self,
        mock_get_path,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
    ):
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "-t", "30", "preview.m4b"]
        progress_output = FFmpegOutputSimulator.create_progress_output(30.0, 3)
        mock_process = MockFFmpegProcess(returncode=0, stderr_output=progress_output)
        mock_popen.return_value = mock_process
        input_file = str(temp_audio_files.create_mp3_file())
        service.process_preview(
            input_file=input_file,
            metadata={"title": "Test Book"},
            settings={"bitrate": 64},
            duration_seconds=30,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_complete_calls or callback_capture.has_error_calls,
            timeout=5.0,
        )
        assert callback_capture.has_complete_calls
        assert not callback_capture.has_error_calls
        mock_build_command.assert_called_once()
        mock_popen.assert_called_once()
    def test_progress_parsing_from_ffmpeg_output(self, service):
        test_cases = [
            (
                "frame= 1000 fps= 0.0 q=-1.0 size= 1024kB time=00:01:00.000 bitrate= 64.0kbits/s speed=1.0x",
                50,
            ),
            (
                "frame= 2000 fps= 0.0 q=-1.0 size= 2048kB time=00:02:00.000 bitrate= 64.0kbits/s speed=1.0x",
                100,
            ),
            (
                "frame= 500 fps= 0.0 q=-1.0 size= 512kB time=00:00:30.000 bitrate= 64.0kbits/s speed=1.0x",
                25,
            ),
            ("Invalid line without time", None),
            ("", None),
        ]
        service._total_duration_seconds = 120.0
        for line, expected_progress in test_cases:
            result = service._parse_ffmpeg_progress(line)
            if expected_progress is not None:
                assert result == expected_progress
            else:
                assert result is None
    @patch("subprocess.Popen")
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    @patch("src.abb.services.core.processing_service_core.get_duration")
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_process_error_handling(
        self,
        mock_get_path,
        mock_get_duration,
        mock_build_command,
        mock_popen,
        service,
        callback_capture,
        temp_audio_files,
    ):
        mock_get_path.return_value = "/usr/bin/ffmpeg"
        mock_get_duration.return_value = 120.0
        mock_build_command.return_value = ["ffmpeg", "-i", "input.mp3", "output.m4b"]
        error_output = FFmpegOutputSimulator.create_error_output("Input file not found")
        mock_process = MockFFmpegProcess(returncode=1, stderr_output=error_output)
        mock_popen.return_value = mock_process
        input_files = [str(temp_audio_files.create_mp3_file())]
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )
        assert callback_capture.has_error_calls
        assert not callback_capture.has_complete_calls
        assert "FFmpeg failed" in callback_capture.last_error_message
        assert "exit code 1" in callback_capture.last_error_message
    @patch("src.abb.services.core.processing_service_core._get_executable_path")
    def test_ffmpeg_not_found_error(
        self, mock_get_path, service, callback_capture, temp_audio_files, path_service
    ):
        mock_get_path.return_value = None
        service = ProcessingServiceCore(path_service)
        input_files = [str(temp_audio_files.create_mp3_file())]
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )
        assert callback_capture.has_error_calls
        assert "FFmpeg executable not found" in callback_capture.last_error_message
    def test_cancel_terminates_process_gracefully(self, service):
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.kill = Mock()
        mock_process.wait = Mock(return_value=0)
        service._is_processing = True
        service._process = mock_process
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._processing_thread.is_alive = Mock(return_value=False)
        service._progress_thread.is_alive = Mock(return_value=False)
        cancel_success = threading.Event()
        def cancel_callback():
            cancel_success.set()
        service.cancel(cancel_callback)
        assert cancel_success.wait(timeout=5.0)
        assert ThreadingTestHelper.wait_for_condition(
            lambda: not service.is_processing(), timeout=2.0
        )
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called()
        assert not service.is_processing()
    def test_cancel_force_kills_unresponsive_process(self, service):
        import subprocess
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.kill = Mock()
        def mock_wait(timeout=None):
            if timeout and timeout <= 2.0:
                raise subprocess.TimeoutExpired("ffmpeg", timeout)
            return 0
        mock_process.wait = Mock(side_effect=mock_wait)
        service._is_processing = True
        service._process = mock_process
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._processing_thread.is_alive = Mock(return_value=False)
        service._progress_thread.is_alive = Mock(return_value=False)
        cancel_success = threading.Event()
        def cancel_callback():
            cancel_success.set()
        service.cancel(cancel_callback)
        assert cancel_success.wait(timeout=10.0)
        assert ThreadingTestHelper.wait_for_condition(
            lambda: not service.is_processing(), timeout=2.0
        )
        mock_process.terminate.assert_called_once()
        mock_process.kill.assert_called_once()
        assert not service.is_processing()
    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_after_successful_processing(self, mock_remove, mock_exists, service, tmp_path):
        mock_exists.return_value = True
        temp_file1 = str(tmp_path / "temp1.tmp")
        temp_file2 = str(tmp_path / "temp2.tmp")
        output_file = str(tmp_path / "output.m4b")
        service._temp_files = [temp_file1, temp_file2, output_file]
        service._output_file = output_file
        service._cleanup_temp_files_only()
        expected_calls = [((temp_file1,), {}), ((temp_file2,), {})]
        mock_remove.assert_has_calls(expected_calls, any_order=True)
        assert mock_remove.call_count == 2
        assert service._temp_files == [output_file]
    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_after_cancellation(self, mock_remove, mock_exists, service, tmp_path):
        mock_exists.return_value = True
        temp_file1 = str(tmp_path / "temp1.tmp")
        temp_file2 = str(tmp_path / "temp2.tmp")
        output_file = str(tmp_path / "output.m4b")
        service._temp_files = [temp_file1, temp_file2, output_file]
        service._output_file = output_file
        service._cleanup_after_cancel()
        expected_calls = [((temp_file1,), {}), ((temp_file2,), {}), ((output_file,), {})]
        mock_remove.assert_has_calls(expected_calls, any_order=True)
        assert mock_remove.call_count == 4
        assert service._temp_files == []
        assert service._output_file is None
    @patch("os.path.exists")
    @patch("os.remove")
    def test_cleanup_handles_file_removal_errors(self, mock_remove, mock_exists, service, tmp_path):
        mock_exists.return_value = True
        mock_remove.side_effect = OSError("Permission denied")
        temp_file = str(tmp_path / "temp.tmp")
        service._temp_files = [temp_file]
        service._cleanup_temp_files_only()
        mock_remove.assert_called_once_with(temp_file)
    def test_thread_safety_state_management(self, service, temp_audio_files):
        def check_processing_state():
            return service.is_processing()
        def try_start_processing():
            files = [str(temp_audio_files.create_mp3_file())]
            capture = ProcessingCallbackCapture()
            service.process(
                input_files=files,
                output_path="/tmp",
                output_filename="test.m4b",
                metadata={},
                settings={},
                progress_callback=capture.progress_callback,
                status_callback=capture.status_callback,
                complete_callback=capture.complete_callback,
                error_callback=capture.error_callback,
            )
            return capture
        operations = [check_processing_state, try_start_processing] * 5
        results = []
        def run_operations():
            for operation in operations:
                try:
                    result = operation()
                    results.append(result)
                except Exception as e:
                    results.append(e)
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=run_operations)
            threads.append(thread)
            thread.start()
        for thread in threads:
            assert ThreadingTestHelper.wait_for_thread_completion(thread, timeout=5.0)
        assert len(results) > 0
        final_state = service.is_processing()
        assert isinstance(final_state, bool)
    @patch("src.abb.services.core.processing_service_core.build_ffmpeg_command")
    def test_invalid_command_generation_error(
        self, mock_build_command, service, callback_capture, temp_audio_files
    ):
        mock_build_command.return_value = None
        input_files = [str(temp_audio_files.create_mp3_file())]
        service.process(
            input_files=input_files,
            output_path="/tmp",
            output_filename="test.m4b",
            metadata={},
            settings={},
            progress_callback=callback_capture.progress_callback,
            status_callback=callback_capture.status_callback,
            complete_callback=callback_capture.complete_callback,
            error_callback=callback_capture.error_callback,
        )
        assert ThreadingTestHelper.wait_for_condition(
            lambda: callback_capture.has_error_calls, timeout=5.0
        )
        assert callback_capture.has_error_calls
        assert "Failed to build FFmpeg command" in callback_capture.last_error_message
    def test_processing_state_cleanup_on_completion(self, service):
        service._is_processing = True
        service._process = Mock()
        service._processing_thread = Mock()
        service._progress_thread = Mock()
        service._progress_callback = Mock()
        service._status_callback = Mock()
        service._complete_callback = Mock()
        service._error_callback = Mock()
        service._cleanup_processing()
        assert service._is_processing is False
        assert service._process is None
        assert service._processing_thread is None
        assert service._progress_thread is None
        assert service._progress_callback is None
        assert service._status_callback is None
        assert service._complete_callback is None
        assert service._error_callback is None
```

## File: tests/unit/services/test_file_service_combined_size.py
```python
import pytest
from unittest.mock import patch
from src.abb.services.file_service import FileService
class TestFileServiceCombinedSize:
    def test_get_combined_size_sums_file_sizes(self, tmp_path):
        file1 = tmp_path / "a.mp3"
        file1.write_bytes(b"x" * 100)
        file2 = tmp_path / "b.mp3"
        file2.write_bytes(b"x" * 200)
        service = FileService()
        service.add_files([str(file1), str(file2)])
        with patch("os.path.getsize") as mock_getsize:
            mock_getsize.side_effect = [100, 200]
            total = service.get_combined_size()
        assert total == 300
    def test_combined_size_signal_emitted_on_add_remove(self, tmp_path, qtbot):
        file1 = tmp_path / "a.mp3"
        file1.write_bytes(b"x" * 100)
        file2 = tmp_path / "b.mp3"
        file2.write_bytes(b"x" * 200)
        service = FileService()
        with qtbot.waitSignal(service.combined_size_changed_signal, timeout=500):
            service.add_files([str(file1), str(file2)])
        with qtbot.waitSignal(service.combined_size_changed_signal, timeout=500):
            service.remove_file(0)
```

## File: tests/unit/services/test_interfaces.py
```python
import pytest
from src.abb.services.core.file_service_core import FileServiceCore
from src.abb.services.core.metadata_handler_core import MetadataHandlerCore
from src.abb.services.core.path_service_core import PathServiceCore
from src.abb.services.core.processing_service_core import ProcessingServiceCore
from src.abb.services.core.settings_manager_core import SettingsManagerCore
from src.abb.services.interfaces import (
    SERVICE_REGISTRY,
    ErrorCategory,
    ServiceError,
    get_service_factory,
    register_service_factory,
)
class TestProtocolCompliance:
    def test_file_service_core_implements_ifile_service(self):
        service = FileServiceCore()
        assert hasattr(service, 'get_files')
        assert hasattr(service, 'get_combined_size')
        assert hasattr(service, 'add_files')
        assert hasattr(service, 'remove_file')
        assert hasattr(service, 'reorder_files')
        assert callable(service.get_files)
        assert callable(service.get_combined_size)
        assert callable(service.add_files)
        assert callable(service.remove_file)
        assert callable(service.reorder_files)
    def test_metadata_handler_core_implements_imetadata_handler(self):
        service = MetadataHandlerCore()
        assert hasattr(service, 'get_current_metadata')
        assert hasattr(service, 'load_from_file')
        assert hasattr(service, 'update_field')
        assert hasattr(service, 'get_for_ffmpeg')
        assert hasattr(service, 'clear_metadata')
        assert hasattr(service, 'set_cover_art')
        assert hasattr(service, 'get_cover_art_path')
        assert callable(service.get_current_metadata)
        assert callable(service.load_from_file)
        assert callable(service.update_field)
        assert callable(service.get_for_ffmpeg)
        assert callable(service.clear_metadata)
        assert callable(service.set_cover_art)
        assert callable(service.get_cover_art_path)
    def test_processing_service_core_implements_iprocessing_service(self):
        from src.abb.services.core.path_service_core import PathServiceCore
        path_service = PathServiceCore()
        service = ProcessingServiceCore(path_service)
        assert hasattr(service, 'process')
        assert hasattr(service, 'process_preview')
        assert hasattr(service, 'cancel')
        assert hasattr(service, 'is_processing')
        assert callable(service.process)
        assert callable(service.process_preview)
        assert callable(service.cancel)
        assert callable(service.is_processing)
    def test_settings_manager_core_implements_isettings_manager(self):
        service = SettingsManagerCore('/tmp/test_settings.json')
        assert hasattr(service, 'get_setting')
        assert hasattr(service, 'set_setting')
        assert hasattr(service, 'get_all_settings')
        assert callable(service.get_setting)
        assert callable(service.set_setting)
        assert callable(service.get_all_settings)
    def test_path_service_core_implements_ipath_service(self):
        service = PathServiceCore()
        assert hasattr(service, 'calculate_output_path')
        assert hasattr(service, 'generate_output_filename')
        assert callable(service.calculate_output_path)
        assert callable(service.generate_output_filename)
class TestErrorCategories:
    def test_error_category_constants_exist(self):
        assert hasattr(ErrorCategory, 'FILE_NOT_FOUND')
        assert hasattr(ErrorCategory, 'PERMISSION_DENIED')
        assert hasattr(ErrorCategory, 'INVALID_FORMAT')
        assert hasattr(ErrorCategory, 'PROCESSING_ERROR')
        assert hasattr(ErrorCategory, 'METADATA_ERROR')
        assert hasattr(ErrorCategory, 'SETTINGS_ERROR')
        assert hasattr(ErrorCategory, 'VALIDATION_ERROR')
        assert hasattr(ErrorCategory, 'DISK_FULL')
        assert hasattr(ErrorCategory, 'FFMPEG_ERROR')
        assert hasattr(ErrorCategory, 'NETWORK_ERROR')
    def test_error_category_values_are_strings(self):
        assert isinstance(ErrorCategory.FILE_NOT_FOUND, str)
        assert isinstance(ErrorCategory.PERMISSION_DENIED, str)
        assert isinstance(ErrorCategory.INVALID_FORMAT, str)
        assert isinstance(ErrorCategory.PROCESSING_ERROR, str)
        assert isinstance(ErrorCategory.METADATA_ERROR, str)
        assert isinstance(ErrorCategory.SETTINGS_ERROR, str)
        assert isinstance(ErrorCategory.VALIDATION_ERROR, str)
        assert isinstance(ErrorCategory.DISK_FULL, str)
        assert isinstance(ErrorCategory.FFMPEG_ERROR, str)
        assert isinstance(ErrorCategory.NETWORK_ERROR, str)
    def test_error_category_values_are_unique(self):
        categories = [
            ErrorCategory.FILE_NOT_FOUND,
            ErrorCategory.PERMISSION_DENIED,
            ErrorCategory.INVALID_FORMAT,
            ErrorCategory.PROCESSING_ERROR,
            ErrorCategory.METADATA_ERROR,
            ErrorCategory.SETTINGS_ERROR,
            ErrorCategory.VALIDATION_ERROR,
            ErrorCategory.DISK_FULL,
            ErrorCategory.FFMPEG_ERROR,
            ErrorCategory.NETWORK_ERROR,
        ]
        assert len(categories) == len(set(categories))
class TestServiceError:
    def test_service_error_creation_with_message_only(self):
        error = ServiceError("Test error message")
        assert error.message == "Test error message"
        assert error.category == ErrorCategory.PROCESSING_ERROR
        assert str(error) == f"[{ErrorCategory.PROCESSING_ERROR}] Test error message"
    def test_service_error_creation_with_message_and_category(self):
        error = ServiceError("File not found", ErrorCategory.FILE_NOT_FOUND)
        assert error.message == "File not found"
        assert error.category == ErrorCategory.FILE_NOT_FOUND
        assert str(error) == f"[{ErrorCategory.FILE_NOT_FOUND}] File not found"
    def test_service_error_inherits_from_exception(self):
        error = ServiceError("Test error")
        assert isinstance(error, Exception)
        with pytest.raises(ServiceError) as exc_info:
            raise error
        assert exc_info.value.message == "Test error"
    def test_service_error_string_representation(self):
        error = ServiceError("Invalid file format", ErrorCategory.INVALID_FORMAT)
        expected = f"[{ErrorCategory.INVALID_FORMAT}] Invalid file format"
        assert str(error) == expected
class TestServiceRegistry:
    def test_register_service_factory_stores_factory(self):
        def test_factory():
            return "test_service"
        register_service_factory("TestService", test_factory)
        assert "TestService" in SERVICE_REGISTRY
        assert SERVICE_REGISTRY["TestService"] is test_factory
    def test_get_service_factory_returns_registered_factory(self):
        def test_factory():
            return "test_service"
        register_service_factory("TestService2", test_factory)
        retrieved_factory = get_service_factory("TestService2")
        assert retrieved_factory is test_factory
    def test_get_service_factory_returns_none_for_unregistered(self):
        factory = get_service_factory("NonExistentService")
        assert factory is None
    def test_service_registry_is_global_dict(self):
        assert isinstance(SERVICE_REGISTRY, dict)
        from src.abb.services.interfaces import SERVICE_REGISTRY as registry2
        assert SERVICE_REGISTRY is registry2
```

## File: tests/unit/services/test_path_service.py
```python
import pytest
from src.abb.services.path_service import PathService
class TestPathService:
    def test_path_service_exists_and_has_required_methods(self):
        service = PathService()
        assert hasattr(service, 'calculate_output_path')
        assert hasattr(service, 'generate_output_filename')
        assert callable(service.calculate_output_path)
        assert callable(service.generate_output_filename)
    def test_sanitize_path_removes_invalid_chars(self):
        service = PathService()
        result = service._sanitize_path('Test:File*Name?.txt')
        assert ':' not in result
        assert '*' not in result
        assert '?' not in result
        assert '_' in result
    def test_generate_output_filename_returns_valid_filename(self):
        service = PathService()
        metadata = {'title': 'Test Book', 'artist': 'Test Author'}
        filename = service.generate_output_filename(metadata, 0)
        assert filename == 'Test Book.m4b'
        filename = service.generate_output_filename(metadata, 1)
        assert filename == 'Test Author - Test Book.m4b'
```

## File: tests/unit/services/test_processing_service_start.py
```python
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from src.abb.services.processing_service import ProcessingService
class TestProcessingServiceStart:
    @pytest.fixture
    def service(self):
        with patch('src.abb.services.processing_service._get_executable_path'), \
             patch('src.abb.services.processing_service.ProcessingValidator'), \
             patch('src.abb.services.processing_service.PathService'), \
             patch('src.abb.services.processing_service.ProcessingWorker'), \
             patch('src.abb.services.processing_service.QThread'):
            service = ProcessingService()
            service._validator = Mock()
            service._path_service = Mock()
            return service
    @pytest.fixture
    def mock_file_list(self):
        return ["file1.mp3", "file2.mp3", "file3.mp3"]
    @pytest.fixture
    def mock_output_settings(self):
        return {
            'output_directory': '/output/base',
            'output_filename_pattern': 0,
            'use_subdirectory_pattern': True,
            'output_bitrate': 64,
            'output_channels': 1,
            'output_sample_rate': None
        }
    @pytest.fixture
    def mock_metadata(self):
        return {
            'title': 'Test Book',
            'artist': 'Test Author',
            'album': 'Test Album',
            'year': '2025'
        }
    def test_service_starts_worker_with_correct_params_and_connects_signals(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output/final'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full') as mock_process:
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata,
                cover_art_path='/path/to/cover.jpg'
            )
            mock_process.assert_called_once()
            call_args = mock_process.call_args[1]
            assert call_args['input_files'] == mock_file_list
            assert call_args['output_path'] == '/output/final'
            assert call_args['output_filename'] == 'output.m4b'
            assert call_args['metadata']['cover_art_temp_path'] == '/path/to/cover.jpg'
            assert call_args['settings']['bitrate'] == 64
            assert call_args['settings']['channels'] == 1
            assert 'sample_rate' not in call_args['settings']
    def test_validation_failure_emits_error(
        self, service, mock_file_list, mock_output_settings, mock_metadata, qtbot
    ):
        with patch.object(service._validator, 'validate_all',
                         return_value=(False, "Insufficient disk space")), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output/test'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300):
            with qtbot.waitSignal(service.error, timeout=100) as blocker:
                service.start_processing(
                    file_list=mock_file_list,
                    output_settings=mock_output_settings,
                    metadata=mock_metadata
                )
            assert len(blocker.args) == 1
            assert "Validation failed: Insufficient disk space" in blocker.args[0]
    def test_estimated_size_calculation(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        with patch('src.abb.services.processing_service.get_duration', return_value=3600), \
             patch.object(service._validator, 'validate_all') as mock_validate, \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch.object(service, 'process_full'):
            mock_validate.return_value = (True, None)
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            call_args = mock_validate.call_args[1]
            assert abs(call_args['estimated_size_mb'] - 82.4) < 0.1
    def test_sample_rate_included_when_specified(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        mock_output_settings['output_sample_rate'] = 44100
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full') as mock_process:
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            call_args = mock_process.call_args[1]
            assert call_args['settings']['sample_rate'] == 44100
    def test_subdirectory_pattern_usage(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path') as mock_calc_path, \
             patch.object(service._path_service, 'generate_output_filename', return_value='output.m4b'), \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full'):
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            mock_calc_path.assert_called_once_with(
                base_directory='/output/base',
                metadata=mock_metadata,
                use_subdirectory=True
            )
    def test_filename_pattern_passed_correctly(
        self, service, mock_file_list, mock_output_settings, mock_metadata
    ):
        mock_output_settings['output_filename_pattern'] = 1
        with patch.object(service._validator, 'validate_all', return_value=(True, None)), \
             patch.object(service._path_service, 'calculate_output_path', return_value='/output'), \
             patch.object(service._path_service, 'generate_output_filename') as mock_gen_filename, \
             patch('src.abb.services.processing_service.get_duration', return_value=300), \
             patch.object(service, 'process_full'):
            service.start_processing(
                file_list=mock_file_list,
                output_settings=mock_output_settings,
                metadata=mock_metadata
            )
            mock_gen_filename.assert_called_once_with(
                metadata=mock_metadata,
                pattern=1
            )
```

## File: tests/unit/services/test_service_factory.py
```python
import os
from unittest.mock import patch
from src.abb.services.file_service import FileService
from src.abb.services.metadata_service import MetadataService
from src.abb.services.path_service import PathService
from src.abb.services.processing_service import ProcessingService
from src.abb.services.service_factory import (
    ServiceFactory,
    clear_service_cache,
    create_file_service,
    create_metadata_service,
    create_path_service,
    create_processing_service,
    create_settings_manager,
    get_service_info,
    is_pure_services_enabled,
)
from src.abb.services.settings_manager import SettingsManager
class TestFeatureFlagDetection:
    def test_feature_flag_disabled_by_default(self):
        with patch.dict(os.environ, {}, clear=True):
            assert is_pure_services_enabled() is False
    def test_feature_flag_enabled_true_lowercase(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            assert is_pure_services_enabled() is True
    def test_feature_flag_enabled_true_uppercase(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "TRUE"}):
            assert is_pure_services_enabled() is True
    def test_feature_flag_enabled_true_mixed_case(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "True"}):
            assert is_pure_services_enabled() is True
    def test_feature_flag_disabled_false(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            assert is_pure_services_enabled() is False
    def test_feature_flag_disabled_invalid_value(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "invalid"}):
            assert is_pure_services_enabled() is False
class TestServiceCreationFunctions:
    def test_create_file_service_flag_disabled_returns_legacy(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            service = create_file_service()
            assert isinstance(service, FileService)
    def test_create_file_service_flag_enabled_returns_adapter(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            service = create_file_service()
            assert hasattr(service, "_core")
            assert service.__class__.__name__ == "QtFileServiceAdapter"
    def test_create_metadata_service_flag_disabled_returns_legacy(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            service = create_metadata_service()
            assert isinstance(service, MetadataService)
    def test_create_metadata_service_flag_enabled_returns_adapter(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            service = create_metadata_service()
            assert hasattr(service, "_core_service_ref")
            assert service.__class__.__name__ == "QtMetadataHandlerAdapter"
    def test_create_processing_service_flag_disabled_returns_legacy(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            service = create_processing_service()
            assert isinstance(service, ProcessingService)
    def test_create_processing_service_flag_enabled_returns_adapter(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            service = create_processing_service()
            assert hasattr(service, "_core_service_ref")
            assert service.__class__.__name__ == "QtProcessingServiceAdapter"
    def test_create_settings_manager_flag_disabled_returns_legacy(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            service = create_settings_manager("/tmp/test_settings.json")
            assert isinstance(service, SettingsManager)
    def test_create_settings_manager_flag_enabled_returns_adapter(self):
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False) as f:
            temp_path = f.name
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            try:
                service = create_settings_manager(temp_path)
                assert hasattr(service, "_core")
                assert service.__class__.__name__ == "QtSettingsManagerAdapter"
            except TypeError as e:
                assert "stat: path should be string" in str(e)
    def test_create_path_service_flag_disabled_returns_legacy(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            service = create_path_service()
            assert isinstance(service, PathService)
    def test_create_path_service_flag_enabled_returns_core(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            service = create_path_service()
            assert service.__class__.__name__ == "PathServiceCore"
class TestServiceFactoryClass:
    def test_service_factory_caches_file_service(self):
        factory = ServiceFactory()
        service1 = factory.get_file_service()
        service2 = factory.get_file_service()
        assert service1 is service2
    def test_service_factory_caches_metadata_service(self):
        factory = ServiceFactory()
        service1 = factory.get_metadata_service()
        service2 = factory.get_metadata_service()
        assert service1 is service2
    def test_service_factory_caches_processing_service(self):
        factory = ServiceFactory()
        service1 = factory.get_processing_service()
        service2 = factory.get_processing_service()
        assert service1 is service2
    def test_service_factory_caches_settings_manager(self):
        factory = ServiceFactory()
        service1 = factory.get_settings_manager("/tmp/test.json")
        service2 = factory.get_settings_manager("/tmp/test.json")
        assert service1 is service2
    def test_service_factory_different_settings_paths_not_cached(self):
        factory = ServiceFactory()
        service1 = factory.get_settings_manager("/tmp/test1.json")
        service2 = factory.get_settings_manager("/tmp/test2.json")
        assert service1 is not service2
    def test_service_factory_caches_path_service(self):
        factory = ServiceFactory()
        service1 = factory.get_path_service()
        service2 = factory.get_path_service()
        assert service1 is service2
    def test_service_factory_clear_cache_removes_all_services(self):
        factory = ServiceFactory()
        factory.get_file_service()
        factory.get_metadata_service()
        factory.get_path_service()
        assert len(factory._services) > 0
        factory.clear_cache()
        assert len(factory._services) == 0
    def test_service_factory_get_service_info_returns_configuration(self):
        factory = ServiceFactory()
        factory.get_file_service()
        factory.get_metadata_service()
        info = factory.get_service_info()
        assert "pure_services_enabled" in info
        assert "cached_services" in info
        assert "environment_variable" in info
        assert isinstance(info["cached_services"], list)
        assert len(info["cached_services"]) >= 2
class TestDefaultFactoryAndConvenienceFunctions:
    def test_default_factory_is_singleton(self):
        from src.abb.services.service_factory import default_factory as factory1
        from src.abb.services.service_factory import default_factory as factory2
        assert factory1 is factory2
    def test_get_service_info_uses_default_factory(self):
        clear_service_cache()
        info = get_service_info()
        assert "pure_services_enabled" in info
        assert "cached_services" in info
        assert "environment_variable" in info
    def test_clear_service_cache_clears_default_factory(self):
        from src.abb.services.service_factory import get_file_service, get_metadata_service
        get_file_service()
        get_metadata_service()
        info_before = get_service_info()
        assert len(info_before["cached_services"]) >= 2
        clear_service_cache()
        info_after = get_service_info()
        assert len(info_after["cached_services"]) == 0
class TestServiceFactoryBehavioralEquivalence:
    def test_file_service_behavioral_equivalence(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            legacy_service = create_file_service()
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            pure_service = create_file_service()
        legacy_methods = {name for name in dir(legacy_service) if not name.startswith("_")}
        pure_methods = {name for name in dir(pure_service) if not name.startswith("_")}
        core_methods = {"get_files", "get_combined_size", "add_files", "remove_file"}
        assert core_methods.issubset(legacy_methods)
        assert core_methods.issubset(pure_methods)
    def test_metadata_service_behavioral_equivalence(self):
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "false"}):
            legacy_service = create_metadata_service()
        with patch.dict(os.environ, {"ABB_PURE_SERVICES": "true"}):
            pure_service = create_metadata_service()
        legacy_methods = {name for name in dir(legacy_service) if not name.startswith("_")}
        pure_methods = {name for name in dir(pure_service) if not name.startswith("_")}
        core_methods = {"extract_metadata", "update_current_metadata", "set_cover_art"}
        assert core_methods.issubset(legacy_methods)
        assert core_methods.issubset(pure_methods)
```

## File: tests/unit/services/test_unified_metadata_handler.py
```python
import os
import pytest
from unittest.mock import patch, MagicMock
from pathlib import Path
from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler, create_metadata_handler
class TestUnifiedMetadataHandler:
    def setup_method(self):
        self.handler = UnifiedMetadataHandler()
    def test_initialization(self):
        assert self.handler._current_metadata == {}
        assert self.handler._current_file_path is None
        assert not self.handler.has_metadata()
    def test_factory_function(self):
        handler = create_metadata_handler()
        assert isinstance(handler, UnifiedMetadataHandler)
        assert handler._current_metadata == {}
    @patch('src.abb.services.unified_metadata_handler.extract_metadata')
    def test_load_from_file_wraps_extract_metadata(self, mock_extract):
        test_file_path = "/test/path/audio.mp3"
        expected_metadata = {
            "title": "Test Title",
            "artist": "Test Artist",
            "album": "Test Album",
            "narrator": "Test Narrator",
            "year": "2023",
            "genre": "Test Genre",
            "series": "Test Series",
            "series_position": "1",
            "series_sort": "Test Series",
            "description": "Test Description",
            "cover_art": None
        }
        mock_extract.return_value = expected_metadata
        result = self.handler.load_from_file(test_file_path)
        mock_extract.assert_called_once_with(test_file_path)
        assert result == expected_metadata
        assert self.handler._current_metadata == expected_metadata
        assert self.handler._current_file_path == test_file_path
        assert self.handler.has_metadata()
    @patch('src.abb.services.unified_metadata_handler.extract_tags')
    def test_extract_tags_only_wraps_extract_tags(self, mock_extract_tags):
        test_file_path = "/test/path/audio.mp3"
        expected_tags = {
            "title": "Test Title",
            "artist": "Test Artist",
            "album": "Test Album",
            "genre": "Test Genre",
            "track_number": "1",
            "year": "2023"
        }
        mock_extract_tags.return_value = expected_tags
        result = self.handler.extract_tags_only(test_file_path)
        mock_extract_tags.assert_called_once_with(test_file_path)
        assert result == expected_tags
    @patch('src.abb.services.unified_metadata_handler.extract_cover')
    def test_extract_cover_art_wraps_extract_cover(self, mock_extract_cover):
        test_file_path = "/test/path/audio.mp3"
        expected_cover_data = b"fake_image_data"
        mock_extract_cover.return_value = expected_cover_data
        result = self.handler.extract_cover_art(test_file_path)
        mock_extract_cover.assert_called_once_with(test_file_path)
        assert result == expected_cover_data
    def test_update_field_modifies_current_metadata(self):
        self.handler._current_metadata = {"title": "Old Title", "artist": "Old Artist"}
        self.handler.update_field("title", "New Title")
        self.handler.update_field("genre", "New Genre")
        assert self.handler._current_metadata["title"] == "New Title"
        assert self.handler._current_metadata["artist"] == "Old Artist"
        assert self.handler._current_metadata["genre"] == "New Genre"
    def test_get_current_metadata_returns_copy(self):
        original_metadata = {"title": "Test Title", "artist": "Test Artist"}
        self.handler._current_metadata = original_metadata
        result = self.handler.get_current_metadata()
        result["title"] = "Modified Title"
        assert result != self.handler._current_metadata
        assert self.handler._current_metadata["title"] == "Test Title"
    def test_get_for_ffmpeg_uses_command_builder_mapping(self):
        self.handler._current_metadata = {
            "title": "Test Title",
            "album": "Test Album",
            "year": "2023",
            "genre": "Fiction",
            "narrator": "Test Narrator",
            "series": "Test Series",
            "description": "Test Description"
        }
        result = self.handler.get_for_ffmpeg()
        assert result["title"] == "Test Title"
        assert result["album"] == "Test Album"
        assert result["date"] == "2023"
        assert result["genre"] == "Fiction"
        assert result["composer"] == "Test Narrator"
        assert result["mood"] == "Test Series"
        assert result["comment"] == "Test Description"
    def test_get_for_ffmpeg_handles_artist_special_case(self):
        self.handler._current_metadata = {"artist": "Test Artist"}
        result = self.handler.get_for_ffmpeg()
        assert result["artist"] == "Test Artist"
        assert result["album_artist"] == "Test Artist"
    def test_get_for_ffmpeg_comment_description_priority(self):
        self.handler._current_metadata = {
            "comment": "Test Comment",
            "description": "Test Description"
        }
        result = self.handler.get_for_ffmpeg()
        assert result["comment"] == "Test Comment"
        self.handler._current_metadata = {"description": "Test Description"}
        result = self.handler.get_for_ffmpeg()
        assert result["comment"] == "Test Description"
    def test_get_for_ffmpeg_series_sort_priority(self):
        self.handler._current_metadata = {
            "series_sort": "Test Series Sort",
            "sort_album": "Test Album Sort"
        }
        result = self.handler.get_for_ffmpeg()
        assert result["album_sort"] == "Test Series Sort"
        self.handler._current_metadata = {"sort_album": "Test Album Sort"}
        result = self.handler.get_for_ffmpeg()
        assert result["album_sort"] == "Test Album Sort"
    def test_get_for_ffmpeg_filters_empty_values(self):
        self.handler._current_metadata = {
            "title": "Valid Title",
            "album": "",
            "year": None,
            "genre": "  ",
            "artist": "Valid Artist"
        }
        result = self.handler.get_for_ffmpeg()
        assert "title" in result
        assert "artist" in result
        assert "album_artist" in result
        assert "album" not in result
        assert "date" not in result
        assert "genre" not in result
    @patch('src.abb.services.unified_metadata_handler.apply_metadata_defaults')
    def test_apply_defaults_wraps_function(self, mock_apply_defaults):
        original_metadata = {"title": "Test Title"}
        modified_metadata = {"title": "Test Title", "album": "Test Title"}
        self.handler._current_metadata = original_metadata
        mock_apply_defaults.return_value = modified_metadata
        self.handler.apply_defaults()
        mock_apply_defaults.assert_called_once_with(original_metadata)
        assert self.handler._current_metadata == modified_metadata
    @patch('src.abb.services.unified_metadata_handler.clear_metadata_cache')
    def test_clear_cache_wraps_function(self, mock_clear_cache):
        self.handler.clear_cache()
        mock_clear_cache.assert_called_once()
    def test_reset_state_clears_internal_state(self):
        self.handler._current_metadata = {"title": "Test"}
        self.handler._current_file_path = "/test/path"
        self.handler.reset_state()
        assert self.handler._current_metadata == {}
        assert self.handler._current_file_path is None
        assert not self.handler.has_metadata()
    def test_get_current_file_path(self):
        assert self.handler.get_current_file_path() is None
        test_path = "/test/path/audio.mp3"
        self.handler._current_file_path = test_path
        assert self.handler.get_current_file_path() == test_path
    def test_has_metadata(self):
        assert not self.handler.has_metadata()
        self.handler._current_metadata = {"title": "Test"}
        assert self.handler.has_metadata()
    def test_get_field(self):
        self.handler._current_metadata = {"title": "Test Title", "artist": "Test Artist"}
        assert self.handler.get_field("title") == "Test Title"
        assert self.handler.get_field("artist") == "Test Artist"
        assert self.handler.get_field("nonexistent") is None
        assert self.handler.get_field("nonexistent", "default") == "default"
    def test_is_enabled_environment_variable(self):
        assert not UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            assert UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'True'}):
            assert UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'TRUE'}):
            assert UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}):
            assert not UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'False'}):
            assert not UnifiedMetadataHandler.is_enabled()
        with patch.dict(os.environ, {'ABB_NEW_META': 'other'}):
            assert not UnifiedMetadataHandler.is_enabled()
class TestUnifiedMetadataHandlerIntegration:
    @pytest.fixture
    def golden_files_path(self):
        return Path(__file__).parent.parent.parent / "data" / "golden"
    @pytest.fixture
    def handler(self):
        return UnifiedMetadataHandler()
    def test_load_real_mp3_file(self, handler, golden_files_path):
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
        metadata = handler.load_from_file(str(mp3_file))
        assert isinstance(metadata, dict)
        assert handler.has_metadata()
        assert handler.get_current_file_path() == str(mp3_file)
        expected_keys = [
            "title", "artist", "album", "narrator", "year", "genre",
            "series", "series_position", "series_sort", "description", "cover_art"
        ]
        for key in expected_keys:
            assert key in metadata
    def test_load_real_m4b_file(self, handler, golden_files_path):
        m4b_file = golden_files_path / "golden.m4b"
        if not m4b_file.exists():
            pytest.skip("Golden M4B file not found")
        metadata = handler.load_from_file(str(m4b_file))
        assert isinstance(metadata, dict)
        assert handler.has_metadata()
        expected_keys = [
            "title", "artist", "album", "narrator", "year", "genre",
            "series", "series_position", "series_sort", "description", "cover_art"
        ]
        for key in expected_keys:
            assert key in metadata
    def test_extract_tags_real_files(self, handler, golden_files_path):
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
        tags = handler.extract_tags_only(str(mp3_file))
        assert isinstance(tags, dict)
        expected_keys = ["title", "artist", "album", "genre", "track_number", "year"]
        for key in expected_keys:
            assert key in tags
    def test_extract_cover_real_files(self, handler, golden_files_path):
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
        cover_data = handler.extract_cover_art(str(mp3_file))
        assert cover_data is None or isinstance(cover_data, bytes)
    def test_compatibility_with_existing_extract_metadata(self, handler, golden_files_path):
        from src.abb.metadata_utils import extract_metadata
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
        handler_result = handler.load_from_file(str(mp3_file))
        direct_result = extract_metadata(str(mp3_file))
        assert handler_result == direct_result
    def test_ffmpeg_format_conversion(self, handler, golden_files_path):
        mp3_file = golden_files_path / "raw.mp3"
        if not mp3_file.exists():
            pytest.skip("Golden MP3 file not found")
        handler.load_from_file(str(mp3_file))
        ffmpeg_metadata = handler.get_for_ffmpeg()
        assert isinstance(ffmpeg_metadata, dict)
        for key, value in ffmpeg_metadata.items():
            assert isinstance(key, str)
            assert isinstance(value, str)
            assert value.strip() != ""
        if handler.get_field("title"):
            assert "title" in ffmpeg_metadata
        if handler.get_field("artist"):
            assert "artist" in ffmpeg_metadata
            assert "album_artist" in ffmpeg_metadata  # Should be copied
```

## File: tests/unit/ui_widgets/test_progress_integration.py
```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtWidgets import QApplication
from src.abb.main_window import MainWindow
from src.abb.ui.widgets.right_panel_widget import RightPanelWidget
class TestProgressIntegration:
    @pytest.fixture
    def qt_app(self, qapp):
        return qapp
    @pytest.fixture
    def mock_controller(self):
        controller = Mock()
        controller.file_list_updated_signal = Mock()
        controller.metadata_updated_signal = Mock()
        controller.cover_art_updated_signal = Mock()
        controller.status_message_updated_signal = Mock()
        controller.processing_started_signal = Mock()
        controller.processing_progress_signal = Mock()
        controller.processing_finished_signal = Mock()
        controller.error_occurred_signal = Mock()
        controller.setting_changed = Mock()
        controller.selected_file_data_changed = Mock()
        controller.selected_file_properties_updated_signal = Mock()
        controller.combined_size_updated_signal = Mock()
        for attr_name in dir(controller):
            attr = getattr(controller, attr_name)
            if hasattr(attr, 'connect'):
                attr.connect = Mock()
        controller.get_files = Mock(return_value=[])
        controller.get_metadata = Mock(return_value={})
        controller.get_setting = Mock(return_value=None)
        controller.get_output_path = Mock(return_value="/test/output")
        controller.get_output_filename = Mock(return_value="test.m4b")
        controller.start_processing = Mock()
        controller.cancel_processing = Mock()
        return controller
    @pytest.fixture
    def main_window(self, qt_app, mock_controller):
        with patch('src.abb.main_window.MainController', return_value=mock_controller), \
             patch('src.abb.main_window.SettingsManager'):
            window = MainWindow()
            window._controller = mock_controller
            return window
    def test_progress_signal_connections_exist(self, main_window, mock_controller):
        mock_controller.processing_started_signal.connect.assert_called()
        mock_controller.processing_progress_signal.connect.assert_called()
        mock_controller.processing_finished_signal.connect.assert_called()
        mock_controller.error_occurred_signal.connect.assert_called()
    def test_start_processing_calls_controller_method(self, main_window, mock_controller):
        main_window.start_processing()
        mock_controller.start_processing.assert_called_once()
    def test_progress_update_delegates_to_right_panel(self, main_window):
        main_window.right_panel_widget = Mock()
        main_window._update_progress(75)
        main_window.right_panel_widget.update_progress.assert_called_once_with(75)
    def test_processing_start_sets_ui_state(self, main_window):
        main_window.right_panel_widget = Mock()
        main_window.left_panel_widget = Mock()
        main_window._set_ui_for_processing_start(True)
        main_window.right_panel_widget.set_processing_state.assert_called_once_with(True)
        main_window.left_panel_widget.setEnabled.assert_called_once_with(False)
    def test_processing_done_resets_progress(self, main_window):
        main_window.right_panel_widget = Mock()
        main_window.left_panel_widget = Mock()
        main_window._processing_done("/test/output.m4b")
        main_window.right_panel_widget.reset_progress.assert_called_once()
        main_window.right_panel_widget.set_processing_state.assert_called_once_with(False)
    def test_right_panel_progress_methods_exist(self):
        mock_controller = Mock()
        widget = RightPanelWidget(mock_controller)
        assert hasattr(widget, 'update_progress')
        assert hasattr(widget, 'reset_progress')
        assert hasattr(widget, 'set_processing_state')
        assert hasattr(widget, 'progress_bar')
    def test_right_panel_update_progress_shows_bar(self):
        mock_controller = Mock()
        widget = RightPanelWidget(mock_controller)
        widget.show()
        assert not widget.progress_bar.isVisible()
        widget.update_progress(50)
        assert widget.progress_bar.isVisible()
        assert widget.progress_bar.value() == 50
    def test_right_panel_reset_progress_hides_bar(self):
        mock_controller = Mock()
        widget = RightPanelWidget(mock_controller)
        widget.show()
        widget.progress_bar.setVisible(True)
        widget.progress_bar.setValue(75)
        widget.reset_progress()
        assert not widget.progress_bar.isVisible()
        assert widget.progress_bar.value() == 0
    def test_right_panel_processing_state_toggles_buttons(self):
        mock_controller = Mock()
        widget = RightPanelWidget(mock_controller)
        widget.show()
        widget.set_processing_state(True)
        assert not widget.process_button.isEnabled()
        assert widget.cancel_button.isEnabled()
        widget.set_processing_state(False)
        assert widget.process_button.isEnabled()
        assert not widget.cancel_button.isEnabled()
```

## File: tests/unit/workers/test_processing_worker_state.py
```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QProcess
from src.abb.processing_worker import ProcessingWorker
from src.abb.ffmpeg_utils import _get_executable_path
class TestProcessingWorkerStateManagement:
    @pytest.fixture
    def worker(self):
        return ProcessingWorker("/usr/bin/ffmpeg")
    @pytest.fixture
    def mock_process_params(self):
        return {
            "file_list": ["file1.mp3", "file2.mp3"],
            "output_path": "/output",
            "output_filename": "book.m4b",
            "metadata": {"title": "Test Book"},
            "settings": {"bitrate": 64},
            "total_duration_seconds": 3600
        }
    def test_worker_tracks_processing_state_correctly(self, worker, mock_process_params):
        assert worker._is_processing is False
        with patch('src.abb.processing_worker.build_ffmpeg_command', return_value=None):
            worker.process(**mock_process_params)
        assert worker._is_processing is False
    def test_worker_prevents_concurrent_processing(self, worker, mock_process_params, qtbot):
        worker._is_processing = True
        with qtbot.waitSignal(worker.error, timeout=100) as blocker:
            worker.process(**mock_process_params)
        assert len(blocker.args) == 1
        assert "Processing already in progress" in blocker.args[0]
    def test_worker_cleanup_always_executes_on_error(self, worker, mock_process_params):
        worker.ffmpeg_path = "/usr/bin/ffmpeg"
        with patch('src.abb.processing_worker.build_ffmpeg_command', side_effect=Exception("Test error")):
            with patch.object(worker, '_cleanup_temp_files') as mock_cleanup:
                with patch('os.path.isfile', return_value=True), \
                     patch('os.access', return_value=True), \
                     patch('src.abb.processing_worker._get_executable_path', return_value='/usr/bin/ffprobe'):
                    worker.process(**mock_process_params)
                mock_cleanup.assert_called_once()
        assert worker._is_processing is False
    def test_cancel_checks_processing_state(self, worker):
        worker._is_processing = False
        worker.cancel()
        assert worker._process is None
    def test_state_reset_on_process_finished(self, worker):
        worker._is_processing = True
        worker._is_cancelled = False
        mock_process = Mock(spec=QProcess)
        mock_process.readAllStandardError.return_value = b""
        worker._process = mock_process
        worker._process_finished(0, QProcess.NormalExit)
        assert worker._is_processing is False
        assert worker._is_cancelled is False
    def test_state_reset_on_cancelled_process(self, worker):
        worker._is_processing = True
        worker._is_cancelled = True
        with patch.object(worker, '_cleanup_after_cancel_or_error'):
            worker._process_finished(1, QProcess.NormalExit)
        assert worker._is_processing is False
        assert worker._is_cancelled is False
    def test_preview_process_respects_state(self, worker, qtbot):
        worker._is_processing = True
        with qtbot.waitSignal(worker.error, timeout=100) as blocker:
            worker.start_preview_process(
                input_file_path="test.mp3",
                metadata={},
                settings={}
            )
        assert len(blocker.args) == 1
        assert "Processing already in progress" in blocker.args[0]
    def test_multiple_early_returns_reset_state(self, worker, mock_process_params):
        worker.ffmpeg_path = "/nonexistent/ffmpeg"
        worker.process(**mock_process_params)
        assert worker._is_processing is False
        with patch('src.abb.processing_worker.build_ffmpeg_command', return_value=None):
            worker.process(**mock_process_params)
        assert worker._is_processing is False
```

## File: tests/unit/test_ffmpeg_processing_command.py
```python
import pytest
from unittest.mock import patch, Mock
from src.abb.ffmpeg_utils import build_ffmpeg_processing_command
class TestBuildFFmpegProcessingCommand:
    def test_build_ffmpeg_command_concatenates_inputs(self):
        file_list = ["file1.mp3", "file2.mp3", "file3.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test Book"}
        audio_settings = {"bitrate": 64, "channels": 1}
        ffmpeg_path = "/usr/bin/ffmpeg"
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            mock_build.return_value = ["ffmpeg", "-i", "file1.mp3"]
            result = build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            mock_build.assert_called_once_with(
                input_files=file_list,
                output_file_full_path=output_path,
                metadata=metadata_dict,
                settings=audio_settings,
                ffmpeg_exe_path=ffmpeg_path
            )
    def test_build_ffmpeg_command_embeds_metadata_tags(self):
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {
            "title": "My Audiobook",
            "artist": "John Doe",
            "album": "Great Series",
            "year": "2025",
            "comment": "A great book"
        }
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            call_args = mock_build.call_args[1]
            assert call_args['metadata'] == metadata_dict
    def test_build_ffmpeg_command_embeds_cover_art(self):
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test"}
        cover_art_path = "/path/to/cover.jpg"
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=cover_art_path,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            call_args = mock_build.call_args[1]
            assert call_args['metadata']['cover_art_temp_path'] == cover_art_path
    def test_build_ffmpeg_command_applies_audio_settings(self):
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Test"}
        audio_settings = {
            "bitrate": 96,
            "channels": 2,
            "sample_rate": 44100
        }
        ffmpeg_path = "/usr/bin/ffmpeg"
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command') as mock_build:
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=None,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
            call_args = mock_build.call_args[1]
            assert call_args['settings'] == audio_settings
    def test_build_ffmpeg_command_preserves_original_metadata(self):
        file_list = ["input.mp3"]
        output_path = "/output/book.m4b"
        metadata_dict = {"title": "Original Title"}
        cover_art_path = "/path/to/cover.jpg"
        audio_settings = {"bitrate": 64}
        ffmpeg_path = "/usr/bin/ffmpeg"
        original_metadata = metadata_dict.copy()
        with patch('src.abb.ffmpeg_utils.build_ffmpeg_command'):
            build_ffmpeg_processing_command(
                file_list=file_list,
                output_path=output_path,
                metadata_dict=metadata_dict,
                cover_art_path=cover_art_path,
                audio_settings_dict=audio_settings,
                ffmpeg_path=ffmpeg_path
            )
        assert metadata_dict == original_metadata
        assert 'cover_art_temp_path' not in metadata_dict
```

## File: tests/unit/test_metadata_utils.py
```python
import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from src.abb.metadata_utils import extract_tags, extract_cover
class TestMetadataUtils:
    def test_extract_tags_returns_dict_with_relevant_tags(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_audio.tags = {
                "TIT2": Mock(__str__=lambda self: "Test Title"),
                "TPE1": Mock(__str__=lambda self: "Test Artist"),
                "TALB": Mock(__str__=lambda self: "Test Album"),
                "TCON": Mock(__str__=lambda self: "Test Genre"),
                "TRCK": Mock(__str__=lambda self: "1"),
                "TDRC": Mock(__str__=lambda self: "2023")
            }
            mock_file.return_value = mock_audio
            result = extract_tags("/path/to/test.mp3")
            assert isinstance(result, dict)
            assert result["title"] == "Test Title"
            assert result["artist"] == "Test Artist"
            assert result["album"] == "Test Album"
            assert result["genre"] == "Test Genre"
            assert result["track_number"] == "1"
            assert result["year"] == "2023"
    def test_extract_tags_handles_missing_tags(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_audio.tags = {
                "TIT2": Mock(__str__=lambda self: "Only Title"),
                "TPE1": Mock(__str__=lambda self: "Only Artist")
            }
            mock_file.return_value = mock_audio
            result = extract_tags("/path/to/test.mp3")
            assert result["title"] == "Only Title"
            assert result["artist"] == "Only Artist"
            assert result["album"] is None
            assert result["genre"] is None
            assert result["track_number"] is None
            assert result["year"] is None
    def test_extract_tags_handles_m4a_format(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_audio.tags = {
                "©nam": ["M4A Title"],
                "©ART": ["M4A Artist"],
                "©alb": ["M4A Album"],
                "©gen": ["M4A Genre"],
                "trkn": [(5, 10)],
                "©day": ["2024"]
            }
            mock_file.return_value = mock_audio
            result = extract_tags("/path/to/test.m4a")
            assert result["title"] == "M4A Title"
            assert result["artist"] == "M4A Artist"
            assert result["album"] == "M4A Album"
            assert result["genre"] == "M4A Genre"
            assert result["track_number"] == "5"
            assert result["year"] == "2024"
    def test_extract_tags_handles_file_not_found(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.return_value = None
            result = extract_tags("/path/to/nonexistent.mp3")
            assert isinstance(result, dict)
            assert all(value is None for value in result.values())
    def test_extract_tags_handles_exceptions(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.side_effect = Exception("Test error")
            result = extract_tags("/path/to/error.mp3")
            assert isinstance(result, dict)
            assert all(value is None for value in result.values())
    def test_extract_cover_art_returns_image_data_or_none(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_apic = Mock()
            mock_apic.data = b"fake_image_data"
            mock_audio.tags = {"APIC:": mock_apic}
            mock_file.return_value = mock_audio
            result = extract_cover("/path/to/test.mp3")
            assert result == b"fake_image_data"
            assert isinstance(result, bytes)
    def test_extract_cover_returns_none_when_no_cover_art(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_audio.tags = {"TIT2": "Title only, no cover"}
            mock_file.return_value = mock_audio
            result = extract_cover("/path/to/test.mp3")
            assert result is None
    def test_extract_cover_handles_m4a_format(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_audio = Mock()
            mock_cover = b"m4a_cover_data"
            mock_audio.tags = {"covr": [mock_cover]}
            mock_file.return_value = mock_audio
            result = extract_cover("/path/to/test.m4a")
            assert result == b"m4a_cover_data"
            assert isinstance(result, bytes)
    def test_extract_cover_handles_file_not_found(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.return_value = None
            result = extract_cover("/path/to/nonexistent.mp3")
            assert result is None
    def test_extract_cover_handles_exceptions(self):
        with patch('src.abb.metadata_utils.mutagen.File') as mock_file:
            mock_file.side_effect = Exception("Test error")
            result = extract_cover("/path/to/error.mp3")
            assert result is None
```

## File: tests/unit/test_processing_validator.py
```python
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from src.abb.processing_validator import ProcessingValidator
class TestProcessingValidator:
    @pytest.fixture
    def validator(self):
        return ProcessingValidator()
    @pytest.fixture
    def mock_input_files(self, tmp_path):
        files = []
        for i in range(3):
            file = tmp_path / f"input{i}.mp3"
            file.write_text("mock audio data")
            files.append(file)
        return files
    def test_validate_all_success(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "output"
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
            assert success is True
            assert error is None
    def test_validate_no_input_files(self, validator, tmp_path):
        output_dir = tmp_path / "output"
        success, error = validator.validate_all([], output_dir, 100)
        assert success is False
        assert error == "No input files provided"
    def test_validate_input_file_not_found(self, validator, tmp_path):
        missing_file = tmp_path / "missing.mp3"
        output_dir = tmp_path / "output"
        success, error = validator.validate_all([missing_file], output_dir, 100)
        assert success is False
        assert "Input file not found" in error
    def test_validate_input_file_not_readable(self, validator, tmp_path):
        file = tmp_path / "unreadable.mp3"
        file.write_text("data")
        output_dir = tmp_path / "output"
        with patch('os.access', return_value=False):
            success, error = validator.validate_all([file], output_dir, 100)
        assert success is False
        assert "File not readable" in error
    def test_validate_output_directory_not_writable(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "output"
        output_dir.mkdir()
        with patch('pathlib.Path.touch', side_effect=PermissionError):
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        assert success is False
        assert "Output directory not writable" in error
    def test_validate_insufficient_disk_space(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "output"
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=100*1024*1024)
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        assert success is False
        assert "Insufficient disk space" in error
        assert "Required: 120.0 MB" in error
        assert "Available: 100.0 MB" in error
    def test_validate_disk_space_check_fails_gracefully(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "output"
        with patch('shutil.disk_usage', side_effect=Exception("Disk error")):
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        assert success is True
        assert error is None
    def test_validate_creates_output_directory(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "new_output"
        assert not output_dir.exists()
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)
            success, error = validator.validate_all(
                mock_input_files, output_dir, 100
            )
        assert success is True
        assert error is None
        assert output_dir.exists()
    def test_processing_validator_checks_all_requirements(self, validator, mock_input_files, tmp_path):
        output_dir = tmp_path / "output"
        with patch('shutil.disk_usage') as mock_disk:
            mock_disk.return_value = MagicMock(free=1024*1024*1024)
            with patch.object(validator, '_validate_input_files', wraps=validator._validate_input_files) as spy_input, \
                 patch.object(validator, '_validate_output_directory', wraps=validator._validate_output_directory) as spy_output, \
                 patch.object(validator, '_validate_disk_space', wraps=validator._validate_disk_space) as spy_disk:
                success, error = validator.validate_all(
                    mock_input_files, output_dir, 100
                )
                spy_input.assert_called_once()
                spy_output.assert_called_once()
                spy_disk.assert_called_once()
        assert success is True
        assert error is None
```

## File: tests/test_smoke.py
```python
def test_smoke():
    assert True
```

## File: tests/integration/test_file_import_integration.py
```python
import pytest
import logging
from pathlib import Path
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager
from src.abb.ui.widgets.left_panel_widget import LeftPanelWidget
class TestFileImportIntegration:
    def test_file_drop_updates_ui_through_controller(self, qtbot, tmp_path):
        settings_file = tmp_path / "settings.json"
        settings_manager = SettingsManager(str(settings_file), {})
        controller = MainController(settings_manager=settings_manager)
        left_panel = LeftPanelWidget()
        qtbot.addWidget(left_panel)
        left_panel.files_dropped_signal.connect(controller.add_files)
        controller.file_list_updated_signal.connect(left_panel.update_file_list_display)
        test_file = tmp_path / "test.mp3"
        test_file.touch()
        left_panel.files_dropped_signal.emit([str(test_file)])
        qtbot.wait(100)
        assert left_panel.file_list_widget.count() == 1
        assert left_panel.file_list_widget.item(0).text() == "test.mp3"
```

## File: tests/integration/test_file_properties_integration.py
```python
import pytest
import logging
from unittest.mock import patch
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager
from src.abb.services.file_service import FileService
class TestFilePropertiesIntegration:
    def test_file_selection_triggers_metadata_extraction(self, qtbot, tmp_path):
        settings_file = tmp_path / "settings.json"
        settings_manager = SettingsManager(str(settings_file), {})
        controller = MainController(settings_manager=settings_manager)
        test_file = tmp_path / "file.mp3"
        test_file.write_bytes(b"x" * 100)
        controller.add_files([str(test_file)])
        with patch.object(controller._metadata_service, 'extract_metadata') as mock_extract:
            mock_extract.return_value = {
                'title': 'Test',
                'artist': 'Artist'
            }
            spy = QSignalSpy(controller.selected_file_data_changed)
            controller.handle_file_selection_changed([str(test_file)])
            mock_extract.assert_called_once_with(str(test_file))
            assert spy.count() == 1
```

## File: tests/unit/app_features/test_output_settings.py
```python
import os
import tempfile
import json
import pytest
from unittest.mock import Mock, patch
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.ui.widgets.output_settings_widget import OutputSettingsWidget
class TestOutputSettings:
    def test_changing_output_settings_updates_controller_and_settings_manager(self, qtbot, tmp_path):
        settings_file = tmp_path / "settings.json"
        from src.abb.services.settings_manager import SettingsManager
        settings_manager = SettingsManager(str(settings_file), {})
        controller = MainController(settings_manager=settings_manager)
        initial_settings = {
            "output_bitrate": 64,
            "output_channels": 2,
            "output_create_subdirectory": True,
            "output_filename_pattern": 0
        }
        widget = OutputSettingsWidget(initial_settings=initial_settings)
        qtbot.addWidget(widget)
        widget.setting_changed_signal.connect(controller.update_output_setting)
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.bitrate_combo.setCurrentText("96")
            qtbot.wait(50)
            mock_set.assert_called_with('output_bitrate', 96)
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.samplerate_combo.setCurrentText("44100")
            qtbot.wait(50)
            mock_set.assert_called_with('output_sample_rate', 44100)
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.channels_combo.setCurrentText("Mono")
            qtbot.wait(50)
            mock_set.assert_called_with('output_channels', 1)
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.output_dir_edit.setText("/custom/output/dir")
            qtbot.wait(50)
            widget._on_setting_changed("output_directory", "/custom/output/dir")
            mock_set.assert_called_with('output_directory', "/custom/output/dir")
        # Test filename pattern change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            # Simulate changing the filename pattern
            widget.filename_pattern_edit.setText("1")
            qtbot.wait(50)  # Wait for signal to be processed
            # Trigger the textChanged signal manually
            widget._on_setting_changed("output_filename_pattern", 1)
            mock_set.assert_called_with('output_filename_pattern', 1)
        # Test subdirectory checkbox change
        with patch.object(controller._settings_manager, 'set_setting') as mock_set:
            widget.subdir_checkbox.setChecked(False)
            qtbot.wait(50)  # Wait for signal to be processed
            mock_set.assert_called_with('output_create_subdirectory', False)
    def test_output_settings_persist_on_app_restart(self, qtbot, tmp_path):
        settings_file = tmp_path / "settings.json"
        # Create a real SettingsManager with initial settings
        from src.abb.services.settings_manager import SettingsManager
        initial_settings = {
            'output_bitrate': 96,
            'output_channels': 1,
            'output_create_subdirectory': False,
            'output_filename_pattern': 2,
            'output_directory': '/custom/output',
            'output_sample_rate': 44100
        }
        # First controller instance - set up settings
        settings_manager1 = SettingsManager(str(settings_file), initial_settings)
        controller1 = MainController(settings_manager=settings_manager1)
        # Verify settings are correctly set
        assert controller1._settings_manager.get_setting('output_bitrate') == 96
        assert controller1._settings_manager.get_setting('output_channels') == 1
        assert controller1._settings_manager.get_setting('output_create_subdirectory') == False
        assert controller1._settings_manager.get_setting('output_filename_pattern') == 2
        # Change a setting
        controller1._settings_manager.set_setting('output_bitrate', 128)
        # Create a second controller instance - should load from the same file
        settings_manager2 = SettingsManager(str(settings_file))
        controller2 = MainController(settings_manager=settings_manager2)
        # Verify the changed setting was persisted
        assert controller2._settings_manager.get_setting('output_bitrate') == 128
        assert controller2._settings_manager.get_setting('output_channels') == 1  # Unchanged
        assert controller2._settings_manager.get_setting('output_create_subdirectory') == False  # Unchanged
        assert controller2._settings_manager.get_setting('output_filename_pattern') == 2
def test_settings_manager_persistence():
    fd, temp_path = tempfile.mkstemp(suffix=".json")
    os.close(fd)
    try:
        from src.abb.services.settings_manager import SettingsManager
        manager1 = SettingsManager(temp_path)
        manager1.set_setting("test_key", "test_value")
        manager2 = SettingsManager(temp_path)
        assert manager2.get_setting("test_key") == "test_value"
    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)
```

## File: tests/unit/ffmpeg/test_command_builder.py
```python
import os
import pytest
from unittest.mock import Mock, patch
from src.abb.ffmpeg.command_builder import FFmpegCommandBuilder
from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler
class TestFFmpegCommandBuilder:
    def test_command_builder_exists_and_has_required_methods(self):
        mock_codec_check = Mock(return_value=True)
        builder = FFmpegCommandBuilder(mock_codec_check)
        assert hasattr(builder, 'build_ffmpeg_command')
        assert hasattr(builder, 'build_ffmpeg_preview_command')
        assert callable(builder.build_ffmpeg_command)
        assert callable(builder.build_ffmpeg_preview_command)
    def test_build_command_includes_basic_elements(self):
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        cmd = builder.build_ffmpeg_command(
            input_files=['/path/to/input.mp3'],
            output_file_full_path='/path/to/output.m4b',
            metadata={'title': 'Test'},
            settings={'bitrate': 64},
            ffmpeg_exe_path='ffmpeg'
        )
        assert 'ffmpeg' in cmd
        assert '-i' in cmd
        assert '/path/to/input.mp3' in cmd
        assert '/path/to/output.m4b' in cmd
        assert '-metadata' in cmd
        assert 'title=Test' in cmd
    def test_legacy_path_uses_unified_handler_mapping(self):
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'year': '2023'
        }
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}):
            result = builder._get_ffmpeg_tags_legacy(metadata)
        expected_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        assert 'title' in result
        assert 'artist' in result
        assert 'date' in result
    def test_unified_path_uses_handler_directly(self):
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'year': '2023'
        }
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            result = builder._get_ffmpeg_tags_via_handler(metadata)
        assert 'title' in result
        assert 'artist' in result
        assert 'album_artist' in result
        assert 'date' in result
    def test_both_paths_produce_identical_results(self):
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        metadata = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'album': 'Test Album',
            'year': '2023',
            'genre': 'Fiction'
        }
        legacy_result = builder._get_ffmpeg_tags_legacy(metadata)
        unified_result = builder._get_ffmpeg_tags_via_handler(metadata)
        assert legacy_result == unified_result
    def test_deprecated_constant_still_exists_for_backward_compatibility(self):
        assert hasattr(FFmpegCommandBuilder, 'ABB_TO_FFMPEG_METADATA_MAP_GENERAL')
        builder_mapping = FFmpegCommandBuilder.ABB_TO_FFMPEG_METADATA_MAP_GENERAL
        handler_mapping = UnifiedMetadataHandler.get_metadata_mapping()
        assert builder_mapping == handler_mapping
    def test_environment_variable_controls_path_selection(self):
        mock_codec_check = Mock(return_value=False)
        builder = FFmpegCommandBuilder(mock_codec_check)
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}, clear=False):
            assert not builder._should_use_unified_handler()
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}, clear=False):
            assert builder._should_use_unified_handler()
        with patch.dict(os.environ, {}, clear=True):
            assert not builder._should_use_unified_handler()
```

## File: tests/unit/services/test_settings_manager.py
```python
import unittest
import os
import json
import tempfile
import shutil
import logging
from unittest.mock import patch, MagicMock
from src.abb.services.settings_manager import SettingsManager
from PySide6.QtCore import QObject, Signal
logging.disable(logging.CRITICAL)
class TestSettingsManager(unittest.TestCase):
    def setUp(self):
        self.test_dir = tempfile.mkdtemp()
        self.settings_file = os.path.join(self.test_dir, "test_settings.json")
        self.default_settings = {
            "output_bitrate": 128,
            "output_directory": "/tmp/output",
            "output_filename_pattern": 0,
            "some_other_setting": "value"
        }
    def tearDown(self):
        shutil.rmtree(self.test_dir)
        logging.disable(logging.CRITICAL)
    def _create_settings_file(self, content):
        with open(self.settings_file, 'w') as f:
            json.dump(content, f)
    def test_initialization_with_defaults_and_no_file(self):
        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128)
        self.assertEqual(manager.get_setting("output_directory"), "/tmp/output")
        self.assertEqual(manager.get_setting("some_other_setting"), "value")
        self.assertFalse(os.path.exists(self.settings_file))
    def test_initialization_loads_from_file(self):
        initial_file_content = {
            "output_bitrate": 96,
            "output_directory": "/home/<USER>/music",
            "new_setting_from_file": "loaded"
        }
        self._create_settings_file(initial_file_content)
        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 96)
        self.assertEqual(manager.get_setting("output_directory"), "/home/<USER>/music")
        self.assertEqual(manager.get_setting("output_filename_pattern"), 0)
        self.assertEqual(manager.get_setting("new_setting_from_file"), "loaded")
    @patch('src.abb.services.settings_manager.logger')
    def test_initialization_handles_corrupt_file(self, mock_logger):
        with open(self.settings_file, 'w') as f:
            f.write("this is not valid json")
        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128)
        mock_logger.warning.assert_called_with(f"Corrupt settings file: {self.settings_file}. Using default settings.")
    def test_initialization_handles_missing_file(self):
        if os.path.exists(self.settings_file):
            os.remove(self.settings_file)
        manager = SettingsManager(self.settings_file, self.default_settings)
        self.assertEqual(manager.get_setting("output_bitrate"), 128)
        self.assertFalse(os.path.exists(self.settings_file))
    def test_get_setting_retrieves_value_and_default(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("test_key", "test_value")
        self.assertEqual(manager.get_setting("test_key"), "test_value")
        self.assertEqual(manager.get_setting("non_existent_key"), None)
        self.assertEqual(manager.get_setting("non_existent_key", "my_default"), "my_default")
    def test_set_setting_updates_memory_and_file(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("output_bitrate", 64)
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        with open(self.settings_file, 'r') as f:
            loaded_content = json.load(f)
            self.assertEqual(loaded_content.get("output_bitrate"), 64)
        manager.set_setting("new_key", "new_value")
        self.assertEqual(manager.get_setting("new_key"), "new_value")
        with open(self.settings_file, 'r') as f:
            loaded_content = json.load(f)
            self.assertEqual(loaded_content.get("new_key"), "new_value")
    def test_set_setting_emits_signal(self):
        manager = SettingsManager(self.settings_file)
        mock_slot = MagicMock()
        manager.settings_changed.connect(mock_slot)
        manager.set_setting("output_bitrate", 64)
        mock_slot.assert_called_once_with("output_bitrate", 64)
        mock_slot.reset_mock()
        manager.set_setting("output_directory", "/new/path")
        mock_slot.assert_called_once_with("output_directory", "/new/path")
    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_bitrate(self, mock_logger):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("output_bitrate", 64)
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        mock_logger.warning.assert_not_called()
        manager.set_setting("output_bitrate", 100)
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        mock_logger.warning.assert_called_with("Invalid output_bitrate: 100. Must be one of [32, 48, 56, 64, 96, 128].")
        mock_logger.reset_mock()
        manager.set_setting("output_bitrate", "abc")
        self.assertEqual(manager.get_setting("output_bitrate"), 64)
        mock_logger.warning.assert_called_with("Invalid output_bitrate: abc. Must be one of [32, 48, 56, 64, 96, 128].")
    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_directory(self, mock_logger):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("output_directory", "/valid/path")
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path")
        mock_logger.warning.assert_not_called()
        manager.set_setting("output_directory", "")
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path")
        mock_logger.warning.assert_called_with("Invalid output_directory: . Must be a non-empty string.")
        mock_logger.reset_mock()
        manager.set_setting("output_directory", 123)
        self.assertEqual(manager.get_setting("output_directory"), "/valid/path")
        mock_logger.warning.assert_called_with("Invalid output_directory: 123. Must be a non-empty string.")
    @patch('src.abb.services.settings_manager.logger')
    def test_validation_output_filename_pattern(self, mock_logger):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("output_filename_pattern", 1)
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1)
        mock_logger.warning.assert_not_called()
        manager.set_setting("output_filename_pattern", 3)
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1)
        mock_logger.warning.assert_called_with("Invalid output_filename_pattern: 3. Must be 0, 1, or 2.")
        mock_logger.reset_mock()
        manager.set_setting("output_filename_pattern", "a")
        self.assertEqual(manager.get_setting("output_filename_pattern"), 1)
        mock_logger.warning.assert_called_with("Invalid output_filename_pattern: a. Must be 0, 1, or 2.")
    def test_set_setting_unknown_key_no_validation_error(self):
        manager = SettingsManager(self.settings_file)
        manager.set_setting("new_unvalidated_key", "some_value")
        self.assertEqual(manager.get_setting("new_unvalidated_key"), "some_value")
        with patch('src.abb.services.settings_manager.logger') as mock_logger:
            manager.set_setting("another_unvalidated_key", 123)
            mock_logger.warning.assert_not_called()
            mock_logger.error.assert_not_called()
        self.assertEqual(manager.get_setting("another_unvalidated_key"), 123)
if __name__ == '__main__':
    unittest.main()
```

## File: tests/unit/ui_widgets/test_dropzone.py
```python
import pytest
from PySide6.QtCore import Qt, QUrl, QMimeData, QPoint
from PySide6.QtGui import QDragEnterEvent, QDropEvent
from PySide6.QtTest import QSignalSpy
from src.abb.ui.widgets.dropzone import DropZone
class TestDropZone:
    def test_accepts_supported_files(self, qtbot):
        widget = DropZone()
        qtbot.addWidget(widget)
        mime = QMimeData()
        mime.setUrls([QUrl.fromLocalFile("/test/file.mp3")])
        event = QDragEnterEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        widget.dragEnterEvent(event)
        assert event.isAccepted()
    def test_rejects_unsupported_files(self, qtbot):
        widget = DropZone()
        qtbot.addWidget(widget)
        mime = QMimeData()
        mime.setUrls([QUrl.fromLocalFile("/test/image.jpg")])
        event = QDragEnterEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        widget.dragEnterEvent(event)
        assert not event.isAccepted()
    def test_emits_files_dropped_signal(self, qtbot):
        widget = DropZone()
        qtbot.addWidget(widget)
        signal_spy = QSignalSpy(widget.filesDropped)
        mime = QMimeData()
        mime.setUrls([
            QUrl.fromLocalFile("/test/file1.mp3"),
            QUrl.fromLocalFile("/test/file2.m4a")
        ])
        event = QDropEvent(
            QPoint(0, 0), Qt.CopyAction, mime,
            Qt.LeftButton, Qt.NoModifier
        )
        widget.dropEvent(event)
        assert signal_spy.count() == 1
        emitted_files = signal_spy.at(0)[0]
        assert len(emitted_files) == 2
```

## File: tests/unit/ui_widgets/test_left_panel_widget.py
```python
from unittest.mock import Mock
import pytest
from PySide6.QtCore import QMimeData, QPoint, Qt
from PySide6.QtGui import QDropEvent
from PySide6.QtWidgets import QListWidgetItem
from src.abb.ui.widgets.left_panel_widget import (
    LeftPanelWidget,
)
@pytest.fixture
def left_panel_widget(qtbot):
    widget = LeftPanelWidget()
    qtbot.addWidget(widget)
    return widget
def test_list_widget_allows_internal_drag_drop_and_emits_reorder_signal(left_panel_widget, qtbot):
    list_widget = left_panel_widget.file_list_widget
    from PySide6.QtWidgets import QListWidget
    assert list_widget.dragDropMode() == QListWidget.InternalMove
    assert list_widget.defaultDropAction() == Qt.MoveAction
    assert list_widget.dragEnabled() is True
    assert list_widget.acceptDrops() is True
    item1 = QListWidgetItem("file1.mp3")
    item2 = QListWidgetItem("file2.mp3")
    item3 = QListWidgetItem("file3.mp3")
    list_widget.addItem(item1)
    list_widget.addItem(item2)
    list_widget.addItem(item3)
    assert list_widget.count() == 3
    assert list_widget.item(0).text() == "file1.mp3"
    assert list_widget.item(1).text() == "file2.mp3"
    assert list_widget.item(2).text() == "file3.mp3"
    mock_signal = Mock()
    left_panel_widget.files_reordered_signal.connect(mock_signal)
    item_to_move = list_widget.takeItem(0)
    list_widget.insertItem(2, item_to_move)
    left_panel_widget._handle_files_reordered()
    assert list_widget.count() == 3
    assert list_widget.item(0).text() == "file2.mp3"
    assert list_widget.item(1).text() == "file3.mp3"
    assert list_widget.item(2).text() == "file1.mp3"
    mock_signal.assert_called_once_with(["file2.mp3", "file3.mp3", "file1.mp3"])
```

## File: tests/features/test_file_import.py
```python
import pytest
from pathlib import Path
from unittest.mock import patch
from PySide6.QtCore import Qt, QUrl, QMimeData, QPoint
from PySide6.QtGui import QDropEvent
from PySide6.QtTest import QSignalSpy
from src.abb.main_window import MainWindow
class TestFileImportFeature:
    @pytest.fixture
    def main_window(self, qtbot, monkeypatch):
        monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
        window = MainWindow()
        qtbot.addWidget(window)
        return window
    def test_import_files_via_drag_and_drop(self, main_window, tmp_path, qtbot):
        mp3_file = tmp_path / "chapter1.mp3"
        mp3_file.write_text("dummy")
        mime_data = QMimeData()
        mime_data.setUrls([QUrl.fromLocalFile(str(mp3_file))])
        drop_event = QDropEvent(
            QPoint(50, 50), Qt.CopyAction, mime_data,
            Qt.LeftButton, Qt.NoModifier
        )
        main_window.show()
        qtbot.waitExposed(main_window)
        drop_zone = main_window.left_panel_widget.drop_zone
        assert drop_zone.isVisible()
        assert drop_zone.acceptDrops()
        with patch('PySide6.QtWidgets.QFileDialog.getOpenFileNames') as mock_dialog:
            with qtbot.waitSignal(main_window._controller.file_list_updated_signal):
                drop_zone.dropEvent(drop_event)
            assert not mock_dialog.called
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 1
        assert file_list.item(0).text() == "chapter1.mp3"
    def test_import_files_via_add_button_triggers_dialog(self, main_window, qtbot):
        with patch.object(main_window, '_on_add_files_requested') as mock_handler:
            main_window.left_panel_widget.add_files_button.click()
            mock_handler.assert_called_once()
    def test_import_files_via_controller(self, main_window, tmp_path, qtbot):
        files = []
        for i in range(3):
            f = tmp_path / f"chapter{i}.mp3"
            f.write_text("dummy")
            files.append(str(f))
        with qtbot.waitSignal(main_window._controller.file_list_updated_signal):
            main_window._controller.add_files(files)
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 3
    def test_file_filtering_during_import(self, main_window, tmp_path, qtbot):
        valid = tmp_path / "audio.mp3"
        invalid = tmp_path / "image.jpg"
        valid.write_text("dummy")
        invalid.write_text("dummy")
        main_window._controller.add_files([str(valid), str(invalid)])
        qtbot.wait(100)
        file_list = main_window.left_panel_widget.file_list_widget
        assert file_list.count() == 1
        assert file_list.item(0).text() == "audio.mp3"
```

## File: tests/features/test_file_properties_display.py
```python
import pytest
from unittest.mock import patch, MagicMock
from PySide6.QtTest import QSignalSpy
from src.abb.main_window import MainWindow
class TestFilePropertiesDisplay:
    @pytest.fixture
    def main_window(self, qtbot, monkeypatch):
        monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
        window = MainWindow()
        qtbot.addWidget(window)
        return window
    @pytest.fixture
    def sample_files_with_properties(self, tmp_path):
        files = []
        file1 = tmp_path / "chapter01.mp3"
        file1.write_bytes(b"dummy" * 1000)
        files.append({
            'path': str(file1),
            'properties': {
                'bitrate': 320,
                'sample_rate': 48000,
                'channels': 2,
                'duration': 180.5,
                'file_size': file1.stat().st_size
            }
        })
        file2 = tmp_path / "chapter02.m4a"
        file2.write_bytes(b"dummy" * 500)
        files.append({
            'path': str(file2),
            'properties': {
                'bitrate': 128,
                'sample_rate': 44100,
                'channels': 1,
                'duration': 240.0,
                'file_size': file2.stat().st_size
            }
        })
        return files
    def test_selecting_file_displays_properties(self, main_window, sample_files_with_properties, qtbot):
        file_data = sample_files_with_properties[0]
        with patch('src.abb.controllers.main_controller.get_audio_properties') as mock_get_props, \
             patch('src.abb.metadata_utils.mutagen.File') as mock_mutagen_file, \
             patch('src.abb.metadata_utils.ID3') as mock_id3:
            mock_get_props.return_value = (file_data['properties'], None)
            mock_mutagen_file.return_value = MagicMock(info=MagicMock(length=180.5))
            mock_id3_instance = MagicMock()
            mock_id3_instance.__contains__.side_effect = lambda k: k in ["TIT2", "TPE1", "TALB"]
            mock_id3_instance.__getitem__.side_effect = lambda k: {
                "TIT2": "Test Title",
                "TPE1": "Test Artist",
                "TALB": "Test Album"
            }[k]
            mock_id3.return_value = mock_id3_instance
            main_window._controller.add_files([file_data['path']])
            qtbot.wait(100)
            file_list = main_window.left_panel_widget.file_list_widget
            file_list.setCurrentRow(0)
            with qtbot.waitSignal(main_window._controller.selected_file_data_changed, timeout=1000):
                main_window.left_panel_widget._on_selection_changed()
            left_panel = main_window.left_panel_widget
            assert str(file_data['properties']['bitrate']) in left_panel.bitrate_value_label.text()
    def test_no_file_selected_shows_empty_properties(self, main_window, qtbot):
        left_panel = main_window.left_panel_widget
        assert left_panel.duration_value_label.text() == ""
        assert left_panel.bitrate_value_label.text() == ""
        assert left_panel.sample_rate_value_label.text() == ""
        assert left_panel.channels_value_label.text() == ""
    def test_properties_display_through_controller_flow(self, main_window, sample_files_with_properties, qtbot):
        file_data = sample_files_with_properties[0]
        with patch('src.abb.metadata_utils.extract_metadata') as mock_extract:
            mock_extract.return_value = {
                'title': 'Test Title',
                'artist': 'Test Artist'
            }
            with patch('src.abb.ffmpeg_utils.get_audio_properties') as mock_get_props:
                mock_get_props.return_value = (file_data['properties'], None)
                main_window._controller.add_files([file_data['path']])
                qtbot.wait(100)
                spy = QSignalSpy(main_window._controller.selected_file_data_changed)
                file_list = main_window.left_panel_widget.file_list_widget
                file_list.setCurrentRow(0)
                main_window.left_panel_widget._on_selection_changed()
                qtbot.wait(200)
                assert spy.count() > 0
```

## File: tests/unit/services/test_file_service.py
```python
import pytest
from pathlib import Path
from PySide6.QtTest import QSignalSpy
from unittest.mock import Mock
from src.abb.services.file_service import FileService
class TestFileService:
    def test_add_files_filters_extensions(self, tmp_path):
        service = FileService()
        valid = tmp_path / "test.mp3"
        invalid = tmp_path / "test.txt"
        valid.touch()
        invalid.touch()
        added = service.add_files([str(valid), str(invalid)])
        assert len(added) == 1
        assert added[0] == str(valid.resolve())
    def test_add_files_prevents_duplicates(self, tmp_path):
        service = FileService()
        file = tmp_path / "test.mp3"
        file.touch()
        service.add_files([str(file)])
        added = service.add_files([str(file)])
        assert len(added) == 0
        assert len(service.get_files()) == 1
    def test_reorder_files(self, tmp_path):
        service = FileService()
        files = []
        for i in range(3):
            f = tmp_path / f"file{i}.mp3"
            f.touch()
            files.append(str(f))
        service.add_files(files)
        current_files = service.get_files()
        new_order = [current_files[2], current_files[0], current_files[1]]
        service.reorder_files(new_order)
        result = service.get_files()
        assert Path(result[0]).name == "file2.mp3"
        assert Path(result[1]).name == "file0.mp3"
        assert Path(result[2]).name == "file1.mp3"
    def test_file_service_reorder_files_updates_internal_list_and_emits_signal(self, tmp_path):
        service = FileService()
        file_a = tmp_path / "file_a.mp3"
        file_b = tmp_path / "file_b.mp3"
        file_c = tmp_path / "file_c.mp3"
        file_a.touch()
        file_b.touch()
        file_c.touch()
        initial_files = [str(file_a), str(file_b), str(file_c)]
        service.add_files(initial_files)
        mock_slot = Mock()
        service.files_changed.connect(mock_slot)
        new_order_paths = [str(file_c), str(file_a), str(file_b)]
        service.reorder_files(new_order_paths)
        current_files = service.get_files()
        assert current_files == [str(file_c.resolve()), str(file_a.resolve()), str(file_b.resolve())]
        mock_slot.assert_called_once()
        mock_slot.assert_called_with([str(file_c.resolve()), str(file_a.resolve()), str(file_b.resolve())])
```

## File: tests/unit/ui_widgets/test_metadata_form_widget.py
```python
import pytest
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from src.abb.ui.widgets.metadata_form_widget import MetadataFormWidget
class TestMetadataFormWidget:
    @pytest.fixture
    def widget(self, qtbot):
        widget = MetadataFormWidget()
        qtbot.addWidget(widget)
        return widget
    def test_metadata_form_populates_fields_on_slot_call(self, widget):
        metadata_dict = {
            'title': 'Test Title',
            'artist': 'Test Artist',
            'album': 'Test Album',
            'genre': 'Test Genre',
            'track': '5',
            'date': '2023'
        }
        widget.update_metadata_display(metadata_dict)
        assert widget.title_edit.text() == 'Test Title'
        assert widget.artist_edit.text() == 'Test Artist'
        assert widget.album_edit.text() == 'Test Album'
        assert widget.genre_edit.text() == 'Test Genre'
        assert widget.track_number_edit.text() == '5'
        assert widget.year_edit.text() == '2023'
        assert widget.author_edit.text() == 'Test Artist'
    def test_metadata_form_handles_missing_tags_gracefully(self, widget):
        metadata_dict = {
            'title': 'Only Title',
            'genre': 'Only Genre'
        }
        widget.update_metadata_display(metadata_dict)
        assert widget.title_edit.text() == 'Only Title'
        assert widget.genre_edit.text() == 'Only Genre'
        assert widget.artist_edit.text() == ''
        assert widget.album_edit.text() == ''
        assert widget.track_number_edit.text() == ''
        assert widget.year_edit.text() == ''
        assert widget.author_edit.text() == ''
    def test_metadata_form_handles_empty_dict(self, widget):
        widget.title_edit.setText('Previous Title')
        widget.artist_edit.setText('Previous Artist')
        widget.update_metadata_display({})
        assert widget.title_edit.text() == ''
        assert widget.artist_edit.text() == ''
        assert widget.album_edit.text() == ''
        assert widget.genre_edit.text() == ''
        assert widget.track_number_edit.text() == ''
        assert widget.year_edit.text() == ''
        assert widget.author_edit.text() == ''
    def test_metadata_form_preserves_other_fields(self, widget):
        widget.narrator_edit.setText('Existing Narrator')
        widget.series_edit.setText('Existing Series')
        widget.description_edit.setText('Existing Description')
        # Update with metadata that doesn't include these fields
        metadata_dict = {
            'title': 'New Title',
            'artist': 'New Artist'
        }
        widget.update_metadata_display(metadata_dict)
        assert widget.title_edit.text() == 'New Title'
        assert widget.artist_edit.text() == 'New Artist'
        assert widget.narrator_edit.text() == 'Existing Narrator'
        assert widget.series_edit.text() == 'Existing Series'
        assert widget.description_edit.text() == 'Existing Description'
    def test_field_edit_emits_metadata_field_changed_signal(self, widget, qtbot):
        field_name = "title"
        new_value = "New Title Value"
        with qtbot.waitSignal(widget.metadata_field_changed) as blocker:
            widget.title_edit.setText(new_value)
            widget.title_edit.editingFinished.emit()
        assert blocker.args == [field_name, new_value]
```

## File: tests/unit/services/test_metadata_service.py
```python
import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QObject
from src.abb.services.metadata_service import MetadataService
class TestMetadataService:
    def test_metadata_service_extract_and_load_metadata_populates_attributes_and_emits_signals(self, qtbot):
        mock_tags = {
            "title": "Test Title",
            "artist": "Test Artist",
            "album": "Test Album",
            "genre": "Test Genre",
            "track_number": "1",
            "year": "2023"
        }
        mock_cover_data = b"fake_cover_image_data"
        with patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            service = MetadataService()
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            test_filepath = "/path/to/test.mp3"
            service.extract_and_load_metadata(test_filepath)
            mock_extract_tags.assert_called_once_with(test_filepath)
            mock_extract_cover.assert_called_once_with(test_filepath)
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data == mock_cover_data
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            for key, value in mock_tags.items():
                assert emitted_metadata[key] == value
            assert emitted_metadata['cover_art_data'] == mock_cover_data
    def test_metadata_service_handles_none_cover_art(self, qtbot):
        mock_tags = {"title": "No Cover", "artist": "Test", "album": None, "genre": None, "track_number": None, "year": None}
        mock_cover_data = None
        with patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            service = MetadataService()
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            service.extract_and_load_metadata("/path/to/nocoverart.mp3")
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data is None
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            for key, value in mock_tags.items():
                assert emitted_metadata[key] == value
            assert 'cover_art_data' not in emitted_metadata
    def test_metadata_service_update_current_metadata_modifies_internal_state_and_emits_signal(self, qtbot):
        service = MetadataService()
        initial_metadata = {"title": "Old Title", "artist": "Old Artist"}
        service.current_metadata = initial_metadata.copy()
        field_name = "title"
        new_value = "New Title"
        metadata_updated_spy = qtbot.waitSignal(service.metadata_updated)
        service.update_current_metadata(field_name, new_value)
        assert service.current_metadata[field_name] == new_value
        assert service.current_metadata == {"title": "New Title", "artist": "Old Artist"}
        assert metadata_updated_spy.signal_triggered
        emitted_metadata = metadata_updated_spy.args[0]
        assert emitted_metadata == service.current_metadata
    def test_metadata_service_set_cover_art_updates_internal_state_and_emits_signal(self, qtbot):
        service = MetadataService()
        initial_path = "/path/to/old_cover.jpg"
        service.current_cover_art_path = initial_path
        new_image_path = "/path/to/new_cover.png"
        cover_art_updated_spy = qtbot.waitSignal(service.metadata_updated)
        service.set_cover_art(new_image_path)
        assert service.current_cover_art_path == new_image_path
        assert cover_art_updated_spy.signal_triggered
        emitted_metadata = cover_art_updated_spy.args[0]
        assert emitted_metadata['cover_art_path'] == new_image_path
    def test_metadata_service_signals_are_qt_signals(self):
        service = MetadataService()
        assert hasattr(service, 'metadata_loaded')
        assert hasattr(service, 'metadata_updated')
        assert hasattr(service, 'metadata_error')
        def dummy_slot(data):
            pass
        service.metadata_loaded.connect(dummy_slot)
        service.metadata_updated.connect(dummy_slot)
        service.metadata_error.connect(dummy_slot)
    def test_metadata_service_inherits_from_qobject(self):
        service = MetadataService()
        assert isinstance(service, QObject)
    def test_metadata_service_initialization(self):
        service = MetadataService()
        assert service.current_metadata is None
        assert service.current_cover_art_data is None
        assert service.current_cover_art_path is None
        assert hasattr(service, '_metadata')
        assert service._metadata == {}
class TestMetadataServiceDualPath:
    def test_extract_metadata_flag_disabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            mock_extract.return_value = mock_metadata
            service = MetadataService()
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            result = service.extract_metadata(test_filepath)
            mock_extract.assert_called_once_with(test_filepath)
            assert result == mock_metadata
            assert service._metadata == mock_metadata
            assert metadata_spy.signal_triggered
            assert metadata_spy.args[0] == mock_metadata
    def test_extract_metadata_flag_enabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            service = MetadataService()
            with patch.object(service._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                result = service.extract_metadata(test_filepath)
                # Verify unified handler was used, not original function
                mock_load.assert_called_once_with(test_filepath)
                mock_extract.assert_not_called()
                # Verify results
                assert result == mock_metadata
                assert service._metadata == mock_metadata
                # Verify signal emission
                assert metadata_spy.signal_triggered
                assert metadata_spy.args[0] == mock_metadata
    def test_extract_metadata_both_paths_produce_identical_results(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist", "year": "2023"}
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_loaded)
            result_disabled = service_disabled.extract_metadata(test_filepath)
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_loaded)
                result_enabled = service_enabled.extract_metadata(test_filepath)
        # Verify identical results
        assert result_disabled == result_enabled
        assert service_disabled._metadata == service_enabled._metadata
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    def test_apply_defaults_flag_disabled(self, qtbot):
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            mock_apply.return_value = expected_metadata
            service = MetadataService()
            # Set up signal spy
            metadata_spy = qtbot.waitSignal(service.metadata_updated)
            result = service.apply_defaults(input_metadata)
            # Verify original function was called
            mock_apply.assert_called_once_with(input_metadata)
            # Verify results
            assert result == expected_metadata
            assert service._metadata == expected_metadata
            # Verify signal emission
            assert metadata_spy.signal_triggered
            assert metadata_spy.args[0] == expected_metadata
    def test_apply_defaults_flag_enabled(self, qtbot):
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            service = MetadataService()
            # Mock the unified handler methods
            with patch.object(service._unified_handler, 'apply_defaults') as mock_handler_apply, \
                 patch.object(service._unified_handler, 'get_current_metadata') as mock_get_current:
                mock_get_current.return_value = expected_metadata
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_updated)
                result = service.apply_defaults(input_metadata)
                # Verify unified handler was used, not original function
                assert service._unified_handler._current_metadata == input_metadata
                mock_handler_apply.assert_called_once()
                mock_get_current.assert_called_once()
                mock_apply.assert_not_called()
                # Verify results
                assert result == expected_metadata
                assert service._metadata == expected_metadata
                # Verify signal emission
                assert metadata_spy.signal_triggered
                assert metadata_spy.args[0] == expected_metadata
    def test_apply_defaults_both_paths_produce_identical_results(self, qtbot):
        input_metadata = {"title": "Test Title"}
        expected_metadata = {"title": "Test Title", "artist": "Unknown", "album": "Unknown"}
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.apply_metadata_defaults') as mock_apply:
            mock_apply.return_value = expected_metadata
            service_disabled = MetadataService()
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_updated)
            result_disabled = service_disabled.apply_defaults(input_metadata)
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            with patch.object(service_enabled._unified_handler, 'apply_defaults'), \
                 patch.object(service_enabled._unified_handler, 'get_current_metadata') as mock_get_current:
                mock_get_current.return_value = expected_metadata
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_updated)
                result_enabled = service_enabled.apply_defaults(input_metadata)
        # Verify identical results
        assert result_disabled == result_enabled
        assert service_disabled._metadata == service_enabled._metadata
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    def test_extract_and_load_metadata_flag_disabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            service = MetadataService()
            # Set up signal spy
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            service.extract_and_load_metadata(test_filepath)
            # Verify original functions were called
            mock_extract_tags.assert_called_once_with(test_filepath)
            mock_extract_cover.assert_called_once_with(test_filepath)
            # Verify internal state
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data == mock_cover_data
            # Verify signal emission
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            assert emitted_metadata['title'] == "Test Title"
            assert emitted_metadata['artist'] == "Test Artist"
            assert emitted_metadata['cover_art_data'] == mock_cover_data
    def test_extract_and_load_metadata_flag_enabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            service = MetadataService()
            # Mock unified handler methods
            with patch.object(service._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service._unified_handler, 'extract_cover_art') as mock_handler_cover:
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                # Set up signal spy
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                service.extract_and_load_metadata(test_filepath)
                # Verify unified handler was used, not original functions
                mock_handler_tags.assert_called_once_with(test_filepath)
                mock_handler_cover.assert_called_once_with(test_filepath)
                mock_extract_tags.assert_not_called()
                mock_extract_cover.assert_not_called()
                # Verify internal state
                assert service.current_metadata == mock_tags
                assert service.current_cover_art_data == mock_cover_data
                # Verify signal emission
                assert metadata_spy.signal_triggered
                emitted_metadata = metadata_spy.args[0]
                assert emitted_metadata['title'] == "Test Title"
                assert emitted_metadata['artist'] == "Test Artist"
                assert emitted_metadata['cover_art_data'] == mock_cover_data
    def test_extract_and_load_metadata_both_paths_produce_identical_results(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "Test Title", "artist": "Test Artist"}
        mock_cover_data = b"fake_cover_data"
        # Test with flag disabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            service_disabled = MetadataService()
            metadata_spy_disabled = qtbot.waitSignal(service_disabled.metadata_loaded)
            service_disabled.extract_and_load_metadata(test_filepath)
        # Test with flag enabled
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            with patch.object(service_enabled._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service_enabled._unified_handler, 'extract_cover_art') as mock_handler_cover:
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                metadata_spy_enabled = qtbot.waitSignal(service_enabled.metadata_loaded)
                service_enabled.extract_and_load_metadata(test_filepath)
        # Verify identical results
        assert service_disabled.current_metadata == service_enabled.current_metadata
        assert service_disabled.current_cover_art_data == service_enabled.current_cover_art_data
        assert metadata_spy_disabled.args[0] == metadata_spy_enabled.args[0]
    def test_extract_and_load_metadata_none_cover_art_flag_disabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "No Cover"}
        mock_cover_data = None
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_tags') as mock_extract_tags, \
             patch('src.abb.services.metadata_service.extract_cover') as mock_extract_cover:
            mock_extract_tags.return_value = mock_tags
            mock_extract_cover.return_value = mock_cover_data
            service = MetadataService()
            metadata_spy = qtbot.waitSignal(service.metadata_loaded)
            service.extract_and_load_metadata(test_filepath)
            # Verify internal state
            assert service.current_metadata == mock_tags
            assert service.current_cover_art_data is None
            # Verify signal emission excludes cover art data when None
            assert metadata_spy.signal_triggered
            emitted_metadata = metadata_spy.args[0]
            assert emitted_metadata['title'] == "No Cover"
            assert 'cover_art_data' not in emitted_metadata
    def test_extract_and_load_metadata_none_cover_art_flag_enabled(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_tags = {"title": "No Cover"}
        mock_cover_data = None
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service = MetadataService()
            with patch.object(service._unified_handler, 'extract_tags_only') as mock_handler_tags, \
                 patch.object(service._unified_handler, 'extract_cover_art') as mock_handler_cover:
                mock_handler_tags.return_value = mock_tags
                mock_handler_cover.return_value = mock_cover_data
                metadata_spy = qtbot.waitSignal(service.metadata_loaded)
                service.extract_and_load_metadata(test_filepath)
                # Verify internal state
                assert service.current_metadata == mock_tags
                assert service.current_cover_art_data is None
                # Verify signal emission excludes cover art data when None
                assert metadata_spy.signal_triggered
                emitted_metadata = metadata_spy.args[0]
                assert emitted_metadata['title'] == "No Cover"
                assert 'cover_art_data' not in emitted_metadata
    def test_unified_handler_integration_is_enabled_detection(self):
        # Test various flag values
        test_cases = [
            ('true', True),
            ('True', True),
            ('TRUE', True),
            ('false', False),
            ('False', False),
            ('FALSE', False),
            ('', False),
            ('invalid', False),
        ]
        for flag_value, expected in test_cases:
            with patch.dict(os.environ, {'ABB_NEW_META': flag_value}):
                from src.abb.services.unified_metadata_handler import UnifiedMetadataHandler
                assert UnifiedMetadataHandler.is_enabled() == expected
    def test_signal_emission_consistency_across_paths(self, qtbot):
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title"}
        # Track signal emissions for both paths
        signals_disabled = []
        signals_enabled = []
        # Test disabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            # Connect to all signals
            service_disabled.metadata_loaded.connect(lambda data: signals_disabled.append(('loaded', data)))
            service_disabled.metadata_updated.connect(lambda data: signals_disabled.append(('updated', data)))
            service_disabled.metadata_error.connect(lambda msg: signals_disabled.append(('error', msg)))
            service_disabled.extract_metadata(test_filepath)
        # Test enabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                # Connect to all signals
                service_enabled.metadata_loaded.connect(lambda data: signals_enabled.append(('loaded', data)))
                service_enabled.metadata_updated.connect(lambda data: signals_enabled.append(('updated', data)))
                service_enabled.metadata_error.connect(lambda msg: signals_enabled.append(('error', msg)))
                service_enabled.extract_metadata(test_filepath)
        # Verify signal patterns are identical
        assert len(signals_disabled) == len(signals_enabled)
        assert len(signals_disabled) == 1  # Should have one 'loaded' signal
        assert signals_disabled[0][0] == 'loaded'
        assert signals_enabled[0][0] == 'loaded'
        assert signals_disabled[0][1] == signals_enabled[0][1]
    def test_state_management_consistency_across_paths(self):
        test_filepath = "/path/to/test.mp3"
        mock_metadata = {"title": "Test Title", "artist": "Test Artist"}
        # Test disabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'false'}), \
             patch('src.abb.services.metadata_service.extract_metadata') as mock_extract:
            mock_extract.return_value = mock_metadata
            service_disabled = MetadataService()
            service_disabled.extract_metadata(test_filepath)
            disabled_state = {
                '_metadata': service_disabled._metadata,
                'get_metadata': service_disabled.get_metadata()
            }
        # Test enabled path
        with patch.dict(os.environ, {'ABB_NEW_META': 'true'}):
            service_enabled = MetadataService()
            with patch.object(service_enabled._unified_handler, 'load_from_file') as mock_load:
                mock_load.return_value = mock_metadata
                service_enabled.extract_metadata(test_filepath)
                enabled_state = {
                    '_metadata': service_enabled._metadata,
                    'get_metadata': service_enabled.get_metadata()
                }
        # Verify state consistency
        assert disabled_state['_metadata'] == enabled_state['_metadata']
        assert disabled_state['get_metadata'] == enabled_state['get_metadata']
```

## File: tests/unit/ui_widgets/test_output_settings_widget.py
```python
import pytest
from PySide6.QtWidgets import QApplication, QFileDialog
from PySide6.QtCore import Qt
from unittest.mock import MagicMock, patch
from src.abb.ui.widgets.output_settings_widget import OutputSettingsWidget
@pytest.fixture
def widget(qtbot):
    initial_settings = {
        "output_directory": "/test/output",
        "output_bitrate": 64,
        "output_sample_rate": 48000,
        "output_channels": 1,
        "output_filename_pattern": 1,
        "output_create_subdirectory": True,
    }
    return OutputSettingsWidget(initial_settings=initial_settings)
def test_output_settings_widget_loads_initial_values(widget):
    assert widget.output_dir_edit.text() == "/test/output"
    assert widget.bitrate_combo.currentText() == "64"
    assert widget.samplerate_combo.currentText() == "48000"
    assert widget.channels_combo.currentText() == "Mono"
    assert widget.filename_pattern_edit.text() == "1"
    assert widget.subdir_checkbox.isChecked() is True
def test_output_settings_widget_emits_setting_changed_on_interaction(widget):
    mock_slot = MagicMock()
    widget.setting_changed_signal.connect(mock_slot)
    widget.bitrate_combo.setCurrentText("96")
    mock_slot.assert_called_with("output_bitrate", 96)
    mock_slot.reset_mock()
    widget.samplerate_combo.setCurrentText("22050")
    mock_slot.assert_called_with("output_sample_rate", 22050)
    mock_slot.reset_mock()
    widget.channels_combo.setCurrentText("Stereo")
    mock_slot.assert_called_with("output_channels", 2)
    mock_slot.reset_mock()
    widget.channels_combo.setCurrentText("Mono")
    mock_slot.assert_called_with("output_channels", 1)
    mock_slot.reset_mock()
    widget.output_dir_edit.setText("/new/path")
    mock_slot.assert_called_with("output_directory", "/new/path")
    mock_slot.reset_mock()
    widget.filename_pattern_edit.setText("0")
    mock_slot.assert_called_with("output_filename_pattern", 0)
    mock_slot.reset_mock()
    widget.filename_pattern_edit.setText("invalid")
    mock_slot.assert_called_with("output_filename_pattern", "invalid")
    mock_slot.reset_mock()
    widget.subdir_checkbox.setChecked(False)
    mock_slot.assert_called_with("output_create_subdirectory", False)
    mock_slot.reset_mock()
    widget.subdir_checkbox.setChecked(True)
    mock_slot.assert_called_with("output_create_subdirectory", True)
    mock_slot.reset_mock()
def test_browse_output_directory_updates_lineedit_and_emits_signal(widget):
    mock_slot = MagicMock()
    widget.setting_changed_signal.connect(mock_slot)
    mock_dir = "/mock/selected/directory"
    with patch.object(QFileDialog, 'getExistingDirectory', return_value=mock_dir) as mock_get_dir:
        widget.output_dir_button.click()
        mock_get_dir.assert_called_once()
        assert widget.output_dir_edit.text() == mock_dir
        mock_slot.assert_called_with("output_directory", mock_dir)
```

## File: tests/unit/controllers/test_main_controller_properties.py
```python
import pytest
import logging
from unittest.mock import MagicMock, patch
from PySide6.QtTest import QSignalSpy
from src.abb.controllers.main_controller import MainController
from src.abb.services.settings_manager import SettingsManager
class TestMainControllerProperties:
    @pytest.fixture
    def controller_with_settings(self, tmp_path):
        settings_file = tmp_path / "settings.json"
        default_settings = {
            "output_bitrate": 64,
            "output_channels": 2,
            "output_sample_rate": 44100,
            "output_directory": str(tmp_path),
            "output_filename_pattern": 0,
            "output_create_subdirectory": True
        }
        settings_manager = SettingsManager(str(settings_file), default_settings)
        controller = MainController(settings_manager=settings_manager)
        return controller
    def test_handle_file_selection_changed_with_metadata(self, qtbot, tmp_path, controller_with_settings):
        controller = controller_with_settings
        test_file = tmp_path / "test.mp3"
        test_file.write_bytes(b"x" * 100)
        controller.add_files([str(test_file)])
        with patch.object(controller._metadata_service, 'extract_metadata') as mock_extract, \
             patch('src.abb.ffmpeg_utils.get_audio_properties') as mock_get_props, \
             patch('src.abb.metadata_utils.mutagen.File') as mock_mutagen_file, \
             patch('src.abb.metadata_utils.ID3') as mock_id3:
            expected_metadata = {
                'title': 'Test Title',
                'artist': 'Test Artist',
                'bitrate': 128
            }
            def mock_extract_side_effect(file_path):
                controller._metadata_service._metadata = expected_metadata.copy()
                # Emit the signal as the real method would (Phase 1B: use consolidated signal)
                controller._metadata_service.metadata_loaded.emit(expected_metadata.copy())
                return expected_metadata
            mock_extract.side_effect = mock_extract_side_effect
            mock_get_props.return_value = ({'bitrate': 128, 'sample_rate': 44100, 'channels': 2, 'duration': 60, 'file_size': 1000}, None)
            mock_mutagen_file.return_value = MagicMock(info=MagicMock(length=60))
            mock_id3_instance = MagicMock()
            mock_id3_instance.__contains__.side_effect = lambda k: k in ["TIT2", "TPE1", "TALB"]
            mock_id3_instance.__getitem__.side_effect = lambda k: {
                "TIT2": "Test Title",
                "TPE1": "Test Artist",
                "TALB": "Test Album"
            }[k]
            mock_id3.return_value = mock_id3_instance
            # Spy on the signal
            spy = QSignalSpy(controller.selected_file_data_changed)
            # Handle selection
            controller.handle_file_selection_changed([str(test_file)])
            # Should have called extract_metadata
            mock_extract.assert_called_once_with(str(test_file))
            # Should have emitted signal with metadata
            qtbot.wait(100)
            assert spy.count() == 1
            emitted_data = spy.at(0)[0]
            assert 'title' in emitted_data
    def test_handle_file_selection_changed_no_files(self, qtbot, controller_with_settings):
        controller = controller_with_settings
        spy = QSignalSpy(controller.selected_file_data_changed)
        # Handle empty selection
        controller.handle_file_selection_changed([])
        # Should emit empty dict
        assert spy.count() == 1
        assert spy.at(0)[0] == {}
    def test_files_changed_updates_ui(self, qtbot, controller_with_settings):
        controller = controller_with_settings
        # Mock file service
        controller._file_service = MagicMock()
        # Spy on file list signal
        spy = QSignalSpy(controller.file_list_updated_signal)
        # Trigger files changed
        controller._on_files_changed(['file1.mp3', 'file2.mp3'])
        # Should emit signal
        assert spy.count() == 1
        assert spy.at(0)[0] == ['file1.mp3', 'file2.mp3']
```

## File: tests/unit/ui_widgets/test_coverart.py
```python
from unittest.mock import MagicMock, patch
import pytest
from PySide6.QtCore import QBuffer, QByteArray, QIODevice, Qt
from PySide6.QtGui import QImage, QPixmap
from src.abb.ui.widgets.coverart import CoverArtWidget
class TestCoverArtWidget:
    @pytest.fixture
    def widget(self, qtbot):
        widget = CoverArtWidget()
        qtbot.addWidget(widget)
        return widget
    def test_cover_art_widget_displays_image_on_slot_call(self, widget):
        image = QImage(1, 1, QImage.Format.Format_ARGB32)
        image.fill(Qt.GlobalColor.red)
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.OpenModeFlag.WriteOnly)
        image.save(buffer, "PNG")
        png_data = byte_array.data()
        widget.update_cover_art_display(png_data)
        assert widget.current_pixmap is not None
        assert not widget.current_pixmap.isNull()
        label_pixmap = widget.image_label.pixmap()
        assert label_pixmap is not None
        assert not label_pixmap.isNull()
        assert widget.image_label.text() == ""
    def test_cover_art_widget_clears_on_none(self, widget):
        widget.clear_cover_art = MagicMock()
        widget.update_cover_art_display(None)
        widget.clear_cover_art.assert_called_once()
    def test_update_cover_art_display_image_too_large(self, widget):
        large_image_data = b"a" * (50 * 1024 * 1024 + 1)
        # Mock clear_cover_art and set_cover_art_from_pixmap to ensure they are called/not called
        widget.clear_cover_art = MagicMock()
        widget.set_cover_art_from_pixmap = MagicMock()
        # Call the slot with the large image data
        widget.update_cover_art_display(large_image_data)
        # Verify clear_cover_art was called
        widget.clear_cover_art.assert_called_once()
        # Verify set_cover_art_from_pixmap was NOT called
        widget.set_cover_art_from_pixmap.assert_not_called()
        # Verify the error message was set
        assert widget.image_label.text() == "Image too large (max 50MB)"
    def test_cover_art_widget_handles_invalid_image_data(self, widget):
        # Mock clear_cover_art
        widget.clear_cover_art = MagicMock()
        # Test with actual invalid image data that will fail to load
        test_image_data = b"invalid_image_data"
        # Call the slot - this should fail to load as it's not valid image data
        widget.update_cover_art_display(test_image_data)
        widget.clear_cover_art.assert_called_once()
        assert widget.image_label.text() == "Invalid image data"
    def test_cover_art_widget_clear_functionality(self, widget):
        widget.current_pixmap = QPixmap(100, 100)
        widget.image_label.setText("")
        widget.clear_cover_art()
        assert widget.current_pixmap is None
        assert widget.image_label.text() == "Drag Image Here"
        assert widget.image_label.pixmap() is None or widget.image_label.pixmap().isNull()
    @pytest.fixture
    def dummy_image_path(self, tmp_path):
        dummy_file = tmp_path / "test_image.png"
        image = QImage(1, 1, QImage.Format.Format_ARGB32)
        image.fill(Qt.GlobalColor.red)
        byte_array = QByteArray()
        buffer = QBuffer(byte_array)
        buffer.open(QIODevice.OpenModeFlag.WriteOnly)
        image.save(buffer, "PNG")
        png_data = byte_array.data()
        with open(dummy_file, "wb") as f:
            f.write(png_data)
        return str(dummy_file)
    def test_cover_art_drop_emits_cover_art_changed_signal(self, widget, qtbot, dummy_image_path):
        from PySide6.QtCore import QMimeData, QUrl
        from PySide6.QtGui import QDropEvent
        mime_data = QMimeData()
        mime_data.setUrls([QUrl.fromLocalFile(dummy_image_path)])
        drop_event = QDropEvent(
            widget.pos(),
            Qt.DropAction.CopyAction,
            mime_data,
            Qt.MouseButton.NoButton,
            Qt.KeyboardModifier.NoModifier,
        )
        with qtbot.waitSignal(widget.cover_art_path_changed) as blocker:
            widget.dropEvent(drop_event)
        assert blocker.args == [dummy_image_path]
    def test_cover_art_button_select_emits_cover_art_changed_signal(self, widget, qtbot, dummy_image_path):
        with patch("src.abb.ui.widgets.coverart.QFileDialog.getOpenFileName",
                   return_value=(dummy_image_path, "PNG Files (*.png)")):
            with qtbot.waitSignal(widget.cover_art_path_changed) as blocker:
                widget.load_button.click()
            assert blocker.args == [dummy_image_path]
```

## File: tests/conftest.py
```python
import pytest
import tempfile
import logging
from pathlib import Path
from PySide6.QtCore import QSettings, Qt
from PySide6.QtWidgets import QVBoxLayout, QSplitter, QStatusBar, QWidget
from src.abb.main_window import MainWindow
@pytest.fixture(scope="session")
def qt_app(qapp):
    return qapp
@pytest.fixture
def sample_audio_files_with_properties(tmp_path):
    files = []
    file1 = tmp_path / "chapter01.mp3"
    file1.write_bytes(b"dummy" * 1000)
    files.append({
        'path': str(file1),
        'properties': {
            'bitrate': 320,
            'sample_rate': 48000,
            'channels': 2,
            'duration': 180.5,
            'file_size': file1.stat().st_size
        }
    })
    file2 = tmp_path / "chapter02.m4a"
    file2.write_bytes(b"dummy" * 500)
    files.append({
        'path': str(file2),
        'properties': {
            'bitrate': 128,
            'sample_rate': 44100,
            'channels': 1,
            'duration': 240.0,
            'file_size': file2.stat().st_size
        }
    })
    return files
@pytest.fixture
def main_window_with_monkeypatch(qtbot, monkeypatch, tmp_path):
    monkeypatch.setattr(MainWindow, '_check_system_dependencies', lambda self: None)
    settings_file = tmp_path / "settings.json"
    settings_file.parent.mkdir(exist_ok=True)
    def mock_init(self):
        super(MainWindow, self).__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(1280, 820)
        self.settings = QSettings("AudiobookBoss", "ABB")
        self.logger = logging.getLogger("AudiobookBoss.MainWindow")
        self.app_state = {
            "file_list": [],
            "metadata": {},
            "settings": {},
            "processing_state": {},
        }
        from src.abb.services.settings_manager import SettingsManager
        self._settings_manager = SettingsManager(str(settings_file), self.app_state["settings"])
        from src.abb.controllers.main_controller import MainController
        self._controller = MainController(settings_manager=self._settings_manager)
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(12, 12, 12, 10)
        self.main_layout.setSpacing(8)
        self.splitter = QSplitter(Qt.Horizontal)
        self.left_panel = QWidget()
        self.left_panel.setObjectName("left_panel")
        self.right_panel = QWidget()
        self.right_panel.setObjectName("right_panel")
        self._setup_left_panel()
        self._setup_right_panel()
        self._setup_property_labels()
        self.setup_controller_connections()
        self.splitter.addWidget(self.left_panel)
        self.splitter.addWidget(self.right_panel)
        self.splitter.setSizes([33, 67])
        self.main_layout.addWidget(self.splitter)
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self._setup_menu_bar()
        self.apply_theme()
    monkeypatch.setattr(MainWindow, '__init__', mock_init)
    window = MainWindow()
    qtbot.addWidget(window)
    return window
```

## File: tests/unit/controllers/test_main_controller.py
```python
import pytest
from unittest.mock import MagicMock, patch, Mock
from PySide6.QtTest import QSignalSpy
from PySide6.QtCore import QObject, Signal
from pathlib import Path
from src.abb.controllers.main_controller import MainController
@pytest.fixture
def main_controller_with_mocked_services():
    with patch('src.abb.controllers.main_controller.FileService') as MockFileService, \
         patch('src.abb.controllers.main_controller.MetadataService') as MockMetadataService, \
         patch('src.abb.controllers.main_controller.ProcessingService') as MockProcessingService, \
         patch('src.abb.controllers.main_controller.SettingsManager') as MockSettingsManager, \
         patch('src.abb.controllers.main_controller.PathService') as MockPathService:
        mock_file_service = MockFileService.return_value
        mock_metadata_service = MockMetadataService.return_value
        mock_processing_service = MockProcessingService.return_value
        mock_settings_manager = MockSettingsManager.return_value
        mock_path_service = MockPathService.return_value
        controller = MainController(settings_manager=mock_settings_manager)
        controller._file_service = mock_file_service
        controller._metadata_service = mock_metadata_service
        controller._processing_service = mock_processing_service
        controller._path_service = mock_path_service
        yield controller, {
            'file_service': mock_file_service,
            'metadata_service': mock_metadata_service,
            'processing_service': mock_processing_service,
            'settings_manager': mock_settings_manager,
            'path_service': mock_path_service
        }
class TestMainControllerFileOperations:
    def test_add_files_delegates_to_service(self, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_file_service = mocks['file_service']
        mock_settings_manager = mocks['settings_manager']
        mock_file_service.add_files.return_value = ["/path/to/file1.mp3"]
        result = controller.add_files(["/path/to/file1.mp3", "/path/to/file2.txt"])
        mock_file_service.add_files.assert_called_once_with(["/path/to/file1.mp3", "/path/to/file2.txt"])
        assert result == ["/path/to/file1.mp3"]
        mock_settings_manager.set_setting.assert_called_once_with(
            "last_input_dir", "/path/to"
        )
    def test_updates_last_input_dir_setting(self, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_settings_manager = mocks['settings_manager']
        mock_file_service = mocks['file_service']
        mock_file_service.add_files.return_value = ["/path/to/file.mp3"]
        controller.add_files(["/path/to/file.mp3"])
        mock_settings_manager.set_setting.assert_called_with(
            "last_input_dir", "/path/to"
        )
    def test_controller_reorder_files_delegates_to_file_service(self, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_file_service = mocks['file_service']
        new_order_paths = ["file2.mp3", "file1.mp3", "file3.mp3"]
        controller.reorder_files(new_order_paths)
        mock_file_service.reorder_files.assert_called_once_with(new_order_paths)
class TestMainControllerMetadataIntegration:
    def test_metadata_service_extract_called_on_files_changed_with_no_metadata(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        mock_metadata_service.current_metadata = None
        files = ["/path/to/file1.mp3", "/path/to/file2.mp3"]
        controller._on_files_changed(files)
        mock_metadata_service.extract_and_load_metadata.assert_called_once_with("/path/to/file1.mp3")
    def test_metadata_service_not_called_when_metadata_exists(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        mock_metadata_service.current_metadata = {"title": "Existing"}
        files = ["/path/to/file1.mp3"]
        controller._on_files_changed(files)
        mock_metadata_service.extract_and_load_metadata.assert_not_called()
    def test_metadata_service_not_called_when_no_files(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        mock_metadata_service = mocks['metadata_service']
        mock_metadata_service.current_metadata = None
        controller._on_files_changed([])
        mock_metadata_service.extract_and_load_metadata.assert_not_called()
    def test_metadata_updated_signal_emitted_on_metadata_loaded(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service
        controller._connect_service_signals()
        spy = QSignalSpy(controller.metadata_updated_signal)
        test_metadata = {"title": "Test Title", "artist": "Test Artist"}
        metadata_service.metadata_loaded.emit(test_metadata)
        assert spy.count() == 1
        assert spy.at(0)[0] == test_metadata
    def test_cover_art_updated_signal_emitted_on_cover_art_loaded(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service
        controller._connect_service_signals()
        spy = QSignalSpy(controller.cover_art_updated_signal)
        test_metadata_with_cover = {"cover_art_data": b"fake_image_data"}
        metadata_service.metadata_loaded.emit(test_metadata_with_cover)
        assert spy.count() == 1
        assert spy.at(0)[0] == b"fake_image_data"
    def test_cover_art_updated_signal_emitted_with_none(self, qtbot, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        from src.abb.services.metadata_service import MetadataService
        metadata_service = MetadataService()
        controller._metadata_service = metadata_service
        controller._connect_service_signals()
        spy = QSignalSpy(controller.cover_art_updated_signal)
        test_metadata = {"title": "Test"}
        metadata_service.metadata_loaded.emit(test_metadata)
        assert spy.count() == 0
class TestMainControllerSignalDisconnection:
    @pytest.fixture
    def controller_with_mocks(self, main_controller_with_mocked_services):
        controller, mocks = main_controller_with_mocked_services
        def create_signal_mock():
            signal_mock = MagicMock()
            signal_mock.connect = MagicMock()
            signal_mock.disconnect = MagicMock()
            return signal_mock
        mocks['file_service'].files_changed = create_signal_mock()
        mocks['file_service'].combined_size_changed_signal = create_signal_mock()
        # Phase 1B: Use consolidated MetadataService signals
        mocks['metadata_service'].metadata_loaded = create_signal_mock()
        mocks['metadata_service'].metadata_updated = create_signal_mock()
        mocks['metadata_service'].metadata_error = create_signal_mock()
        mocks['processing_service'].progress = create_signal_mock()
        mocks['processing_service'].finished = create_signal_mock()
        mocks['processing_service'].error = create_signal_mock()
        mocks['processing_service'].status = create_signal_mock()
        mocks['settings_manager'].settings_changed = create_signal_mock()
        # Re-connect signals to the mocked services if __init__ didn't already
        controller._connect_service_signals()
        yield controller, mocks
```
