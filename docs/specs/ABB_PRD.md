# **Audiobook Boss Summary**

Audiobook Boss (ABB) is intended to replace 4 separate tools currently used to process audiobooks: \- Metadata viewer and editor \- [FFMPEG script](?tab=t.cd0fefb5wrp6) that processes that compresses larger books into smaller files \- Merging multiple files into a single M4B file \- Sync and download from Audible This will streamline the process of processing audiobooks and make it more accessible to non-technical users. The app will be initially packaged for MacOS. Windows and Linux support will be done separately, in that order, at some future time.

# **Target Audience**

ABB is a personal project and will be developed and used by a single person who is comfortable using tech. This person is the user of the four aforementioned tools and it was frustration jumping between those tools that inspired ABB. This person will be using Ai agents to help plan and code the project with the user also acting as product tester/manager and hands on testing.

# **Functional MVP Requirements for working Prototype**

## **GUI Sections**

A working "v0" prototype of the GUI is available at [https://github.com/Allmight97/audiobook-alchemy-forge.git](https://github.com/Allmight97/audiobook-alchemy-forge.git) that demonstrates basic UI elements. You can also [view UI here](https://drive.google.com/file/d/15dtS-rCEpbefhGlIYhSOf_JB6ScQIQRh/view?usp=drive_link) or the [HTML code here](?tab=t.qgs4bbnu2waq). It doesn't demonstrate the processing functionality nor represent the tech stack used in production MVP.

### **Input Section**

- **Input Audio files** \- where users will select input files  
  - Drag & Drop files or click to select is the input picker  
  - Supported file types: mp3, m4a, m4b, AAC  
  - Non-audio files are ignored/greyed out from file selection  
  - File picker should support multiple file selection  
  - File picker should remember last used directory

- **M4B Input Handling (MVP Scope):** M4B files can be added to the input list.
	- If an M4B is the first file, its metadata and cover art will be loaded for editing/pass-through.
	- If feasible within MVP constraints, existing chapter markers from a single M4B input file may be passed through.
	- The core processing merges compatible audio streams (MP3, M4A, AAC, and the primary audio stream from M4B inputs) into the final M4B.
	- Merging *multiple* M4B input files or handling complex internal M4B structures is **not** an MVP requirement.

  - **File Order -** how selected files should be handled and ordered
    - Initial state: List files in the order they were chosen from the OS file picker. This initial order is preserved.
    - Files can be reordered **within the ABB file list view** using drag-and-drop with a visual indicator. Column header sorting within ABB is **not** an MVP requirement.
  
- **Selected File Properties** \- This section will display the properties of the selected input file(s). When multiple files are selected it not show anything, only display audio properties of a single file.
  - Bitrate: passed through from input file(s)  
  - Sample rate: passed through from input file(s)  
  - Channels: passed through from input file(s)  
  - File size: size of selected file(s)  
  - Combined Size field will show total file size of all files in the 'file order' section. (Individual selected file size is shown in the 'File Size' field).
	  - Functional requirement: The calculation and display of audio properties must be fast in the event the user needs to skim through many files to see audio properties of each.

### **Folder Monitoring**

- **Folder monitoring** \- Not currently implemented  
  - Placeholder for future functionality of monitoring folder for new files which will give the user the ability to add files to the processing queue

### **Metadata & Output Sections**

Note: Metadata from input file(s) will be used to populate the fields below. If multiple files are loaded, metadata from the **first file in the input list** will be used as the initial values for editing. Existing metadata from the first file will be passed through to the output M4B unless edited by the user. [Reference Meta Tag Chart](?tab=t.xk6extcrnq9r) as needed.

Functional requirements:
- Metadata loaded from the first input file and subsequently edited by the user in the UI fields **must persist** even if the user changes file selection or reorders the input file list.
- When processing multiple input files into a single output M4B, the **single set of metadata currently displayed and edited in the UI** will be applied to the final output file. (Bulk editing of metadata across multiple selected list items simultaneously is **not** an MVP requirement).

- **Metadata Display area \-** These are typical tags used when hosting Audiobooks via Plex.  
  - Cover Art Section: maps to Book Cover Art  
    - Default state: Placeholder image or blank area.  
    - If the first input file contains cover art, display it automatically.  
    - Load Cover Art button: Opens a standard MacOS file picker (filtering for `jpg`, `jpeg`, `png`). Updates the image display.  
    - Drag and Drop: Area accepts dropped image files (`jpg`, `jpeg`, `png`) to update the cover art. Ignore dropped non-image files.  
  - Book Title: maps to Book Title (\`TITLE\` tag). Populated from first input file's \`title\` tag if available.  
  - Book Author: maps to ALBUMARTIST tag. Populated from first input file's \`artist\` tag if available.  
  - Album: maps to ALBUM tag. Defaults to the value entered in the \`Book Title\` field for consistency.  
  - Year: maps to YEAR tag (\`DATE\` or \`TDOR\` frame in ID3). Populated from first input file if available.  
  - Genre: maps to GENRE tag. Populated from first input file if available. Supports multiple genres separated by semicolons.  
  - Series: maps to MOOD tag (for plex series filtering)  
    - May require manual user input  
  - Series Position: maps to TRACK tag (for proper series ordering)  
    - Should accept numbers (e.g., "1", "2", "01").  
    - May require manual user input  
  - Series Sort: maps to ALBUMSORT tag.  
    - May require manual user input  
    - Should likely auto-populate based on Series and Position (e.g., "My Series 01") but remain editable.  
  - Narrator: maps to STYLE tag (for filtering by narrator in Plex). \*Requires manual input or Post-MVP fetch.\*  
  - Description: maps to COMMENT tag (include multi-line support)

- **Output**  
    
  - **Audio Settings**  
    - Bitrate: Defaults to 64k with user selectable menu of `32`, `48`, `56`, `64`, `96`, `128` kbps.
      - If input bitrate is less than 64k, default to that value and DO NOT reencode to standard 64k.  
    - Sample rate: Passed from Input, DO NOT reencode, keep same as input even if higher than 44.1k  
    - Channels: Defaults to mono with user Selectable menu: `Mono`, `Stereo`.  
  - **Output directory**  
    - Clickable text field displays the full *calculated* output path (read-only).  
    - "Browse..." button: Opens a MacOS directory picker. Sets the *base* output directory. Remembers the last used *base* directory.  
    - Default base output directory: Same as chosen input, with option for user to set new default output directory  
    - Output Subdirectory Pattern: **Use Default Pattern** checkbox (checked by default).  
      - If checked, path is: `[Base Directory]/[Author Metadata Field]/[Series Metadata Field]/[Year Metadata Field] - [Book Title Metadata Field]/`  
      - Handle missing metadata gracefully (e.g., use "Unknown Author", omit "Series" if blank, use "Unknown Title"). Replace invalid characters in directory names (e.g., replace `/` with `-`).  
    - If unchecked, path is just `[Base Directory]/`.  
  - **Output Filename Pattern**  
    - Radio buttons or dropdown to select pattern:  
      - Option 1 (Default): `Book Title (Year).m4b` (e.g., `The Great Novel (2025).m4b`)  
      - Option 2: `Author - Book Title.m4b` (e.g., `John Doe - The Great Novel.m4b`)  
    - Use metadata fields for values. Handle missing metadata gracefully (e.g., omit `(Year)` if blank, use "Unknown Author", "Unknown Title").  
    - Invalid characters will be replaced with a dash/hyphen (e.g., replace `/` with `-`).  
  - **Estimated Output Size**  
    - Calculated based on `Total Duration (seconds) * Selected Bitrate (kbps) / 8 / 1024 = Size (MB)`.  
    - Displayed as `~ XXX MB`. Dynamically updates depending upon chosen output audio parameters.  
  - **Preview Button**
    - Generates a `_preview.m4b` file in the **system's temporary directory**. This file is **not persistent** and may be cleaned up upon application closure or job completion.
    - Takes the **first 30 seconds** of the **first input file**.
    - Encodes this segment using the **currently selected Output Audio Settings (Bitrate, Sample Rate, Channels), Metadata, and Cover Art**.
    - After generation, **opens the generated preview file using the system's default OS audio player**.
    - Displays a status message during preview generation (e.g., "Generating preview...") and on completion/failure.

### **Status Monitoring Section**

Spans entire width of the window below other sections \- see [reference UI mock](?tab=t.mrrbmo2end34)

- **Progress Indicator**  
  - Small thumbnail of the cover art positioned at far left of the progress bar  
  - **Progress Bar**  
    - Spans the width of the monitoring section  
    - Details displayed inside (or just under) progress bar:  
      - Hours processed: e.g., 0.5 / 1.2 hours (percentage of job processed) **Details below the bar:**  
      - Current step: e.g., Processing file 1 of 10...  
    - Far right of bar (displayed above the bar):  
      - Status text: e.g., Idle, Processing, Paused, Stopped, Error  
- **Process Audio button**  
  - Displayed as Dynamic Button at the very end of the bar that will start the job, and when a job is active, dynamically change as a pause or stop button. Perhaps we show a small split within the button itself that acts as a selector drop down that allows the user to select "pause" or 'stop' \- I don't know what small "sub buttons" attached to larger actionable buttons are.
	  - MVP functional requirement: A single button that initially says "Process Audiobook". When clicked, it changes its text to "Cancel Processing". Clicking "Cancel" should safely terminate the ffmpeg process and potentially clean up any temporary/partial output file.

## Tech Stack - Python for MacOS, packaged for personal use

- **GUI Framework:** PySide6 (Qt for Python)
- **Core Libraries:**
    - `subprocess` (for FFmpeg/ffprobe interaction)
    - `mutagen` (for audio metadata reading/writing)
    - `PySide6.QtWidgets.QFileDialog` (for file/directory dialogs)
    - `tempfile` (for temporary preview file storage)
    - `threading` or `PySide6.QtCore.QThread` (for background processing to keep UI responsive)

# **Non-Functional Requirements**

Placeholder in case needed (which they won’t be because this is a solo developer project).

# **Future Features**

Out of scope items for the MVP and prototype versions. These are “nice to haves” intended for future versions of ABB.

- Metadata search: Ability to search audible (or other sources?) for metadata and apply to selected file(s) in the output section.  
- Ability to sync audible libraries and download raw audiobook files and PDF.  
- Watch Folder: Files in this folder will be automatically added to the input list from the Audible downloader or from external sources.   
- Complex job processing (queueing multiple jobs that could contain single or even multiple audio files to be re-sampled into single m4b output file)  
- Pause/Resume processing jobs and updates to process audio button to support the feature.
- Embedded Player for Preview feature to replace calling system default media player (QTMultimedia?)
- Windows & Linux builds.
- Enhanced cover functionality (cropping tools or automatic scaling and cropping).
- TODO: Button to clear metadata and cover art.
- Dockerization - possibly integrate with the ARSE-stack.

# **User Stories for Audiobook Boss MVP**

## **File Input & Management:**

As a user, I want ...

1. ...to drag and drop multiple audio files (MP3, M4A, M4B, AAC) onto a designated area so that I can easily add books to be processed without navigating menus.  
2. ...to click a button or area so that I can open a file browser to select multiple audio files (MP3, M4A, AAC) for processing.  
3. ...the application to only allow selection of supported audio file types in the file browser so that I don't accidentally add incompatible files.  
4. ...the file browser to remember the last directory I selected files from so that I don't have to navigate back to my common audiobook source folder every time.  
5. ...to see the list of added audio files displayed in the order I selected them so that I can confirm the input and the initial sequence for merging.  
6. ...to reorder the files in the input list using drag and drop so that I can ensure chapters or parts are merged in the correct sequence.  
7. ...to click column headers (like filename or number) in the file list so that I can quickly sort the files alphabetically or numerically (ascending/descending) if needed.  
8. ...to select one or more files from the input list so that I can view their combined technical properties (like total size).  
9. ...to see the basic audio properties (bitrate, sample rate, channels, file size) of the currently selected file so that I can understand the source material's characteristics.  
10. ...to see the total combined size of all files currently in the input list so that I know the starting size before processing.  
    

## **Metadata Editing & Output Configuration:**

As a user, I want ...

1. ...the application to automatically load metadata (Title, Author, Year, Genre, Description, Cover Art) from the first file in the input list so that I have a starting point for editing and don't have to re-type common information.  
2. ...to edit text fields for Title, Author, Narrator, Album, Year, Genre, Series, Series Position, and Description so that I can ensure the final audiobook has accurate and complete metadata suitable for Plex.  
3. ...to click a "Load Cover Art" button so that I can select a JPG or PNG image file from my computer to use as the audiobook cover.  
4. ...to drag and drop a JPG or PNG image file onto the cover art display area so that I can quickly update the audiobook cover.  
5. ...to select a target audio bitrate (from 32kbps to 128kbps) so that I can control the audio quality and file size of the output M4B.  
6. ...the application to pass through the source bitrate if it's lower than or equal to the default 64kbps setting so that I avoid unnecessary re-encoding when the source is already small enough and I haven't specified otherwise.  
7. ...the application to pass through the source sample rate so that I don’t need to resample to higher or lower (it’s never needed).  
8. ...to choose between Mono and Stereo output channels (defaulting to Mono) so that I can optimize for voice content and potentially save file space.  
9. ...the application to default the output base directory to where I last loaded input files from so that the output is likely placed near the source files initially.  
10. ...to click a "Browse..." button so that I can choose a different base output directory for my processed files.  
11. ...the application to remember the last base output directory I selected via "Browse..." across sessions so that I don't have to re-select my preferred output location every time I open the app.  
12. ...the option to have the application automatically create subdirectories based on Author, Series, and Title/Year metadata so that my output files are organized according to Plex best practices automatically.  
13. ...to choose between different output filename patterns (e.g., Title (Year).m4b or Author \- Title.m4b) so that the final file is named according to my preference.  
14. ...to see an estimated output file size based on the total duration and selected audio settings so that I have an idea of the result before starting the full process.  
15. ...to click a "Preview" button so that I can generate a short (30s) sample using my current audio settings and metadata to quickly check the audio quality and tag information before committing to the full conversion.  
    

## **Processing & Status:**

As a user, I want ...

1. ...to click a "Process Audiobook" button so that the application starts merging the input files (in order), applying the specified metadata and audio settings, and creating the final M4B file.  
2. ...to see a progress bar showing the percentage/time elapsed versus the total estimated time so that I know how much of the processing job is complete.  
3. ...to see the cover art thumbnail next to the progress bar so that I can visually confirm which book is currently being processed.  
4. ...to see text indicating the current processing step (e.g., "Merging files", "Encoding audio", "Writing metadata") so that I understand what the application is currently doing.  
5. ...to see an overall status indicator (e.g., "Idle", "Processing", "Completed", "Error") so that I know the current state of the application/job.  
6. ...the "Process" button to change to a "Pause" or "Stop" button while processing is active so that I can interrupt the job if needed. (We still need to refine the exact Pause/Stop interaction).  
7. ...the application to handle invalid characters in metadata fields when creating directories and filenames (e.g., replace with '-') so that the file system operations do not fail.  
8. ...to see basic error feedback (e.g., "Error: Input file corrupted", "Error: Cannot write to output directory") so that I know if something went wrong during processing.

# **UI Mockups**

## **HTML Code Prototype** 

This is an HTML version of the UI to demonstrate basic functions. It is NOT intended to be used as reference for production UI, just a simple prototype/demo of the intended UI so the user has something to see and play with quickly.

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audiobook Boss - Mockup v2</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'], // Using Inter as requested
                    },
                }
            }
        }
    </script>
    <style>
        /* Basic body styling */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6; /* Light gray background */
            display: flex;
            flex-direction: column;
            height: 100vh; /* Use full viewport height */
            margin: 0;
        }
        /* Main container styling */
        .main-container {
            display: flex;
            flex-grow: 1; /* Allow main content to grow */
            gap: 1rem; /* Gap between panels */
            padding: 1rem;
            overflow: hidden; /* Prevent layout issues */
        }
        /* Panel styling */
        .panel {
            border: 1px solid #e5e7eb; /* Gray border */
            border-radius: 0.5rem; /* Rounded corners */
            background-color: #ffffff; /* White background */
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* Subtle shadow */
            display: flex;
            flex-direction: column;
            padding: 1rem;
        }
        /* Input panel specific width */
        .input-panel {
            width: 33.3333%; /* Approx 1/3 width */
        }
        /* Metadata/Output panel specific width */
        .metadata-output-panel {
            flex-grow: 1; /* Takes remaining space */
            overflow-y: auto; /* Allow scrolling if content overflows */
        }
        /* Status panel specific height */
        .status-panel {
            /* height: auto; */ /* Let content define height */
             margin: 0 1rem 1rem 1rem; /* Add margin to match main container padding */
        }
        /* Drag and drop area */
        .drag-drop-area {
            border: 2px dashed #d1d5db; /* Dashed border */
            border-radius: 0.375rem; /* Rounded corners */
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            background-color: #fafafa; /* Slightly off-white background */
        }
        .drag-drop-area:hover {
            background-color: #f0f0f0; /* Slightly darker on hover */
        }
        /* File list placeholder */
        .file-list-placeholder {
            min-height: 8rem; /* Min height */
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af; /* Gray text */
            font-style: italic;
            border: 1px solid #e5e7eb; /* Solid border */
            border-radius: 0.375rem; /* Rounded corners */
            padding: 1rem;
            flex-grow: 1; /* Allow list to take available space */
            overflow-y: auto; /* Add scroll if many files */
        }
         /* Buttons */
        button, select, input[type="text"], textarea {
           margin-top: 0.25rem; /* Add small space above form elements */
        }
        button {
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
            white-space: nowrap;
        }
        .button-primary { background-color: #3b82f6; color: white; }
        .button-primary:hover { background-color: #2563eb; }
        .button-secondary { background-color: #6b7280; color: white; }
        .button-secondary:hover { background-color: #4b5563; }

        /* Input fields and labels */
        label {
             display: block;
             font-size: 0.875rem; /* text-sm */
             font-weight: 500; /* font-medium */
             color: #374151; /* text-gray-700 */
             margin-bottom: 0.25rem; /* Small space below label */
        }
        input[type="text"], select, textarea {
            width: 100%;
            border-radius: 0.375rem;
            border: 1px solid #d1d5db; /* border-gray-300 */
            padding: 0.5rem;
            box-shadow: inset 0 1px 2px 0 rgba(0,0,0,0.05);
        }
        input[readonly] {
             background-color: #f3f4f6; /* bg-gray-100 */
             cursor: default;
        }
        input:focus, select:focus, textarea:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            border-color: #6366f1; /* focus:border-indigo-500 */
            box-shadow: 0 0 0 2px #a5b4fc; /* focus:ring-indigo-500 */
        }
        /* Property labels/values */
        .property-label { font-weight: 500; color: #4b5563; }
        .property-value { color: #1f2937; font-family: monospace; margin-left: 0.25rem; }

         /* Cover Art Placeholder */
        .cover-art-area {
            width: 100%;
            padding-top: 100%; /* Creates a square aspect ratio */
            position: relative;
            background-color: #e5e7eb; /* Gray background */
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            overflow: hidden; /* Ensure image stays within bounds */
        }
        .cover-art-area img {
             position: absolute;
             top: 0;
             left: 0;
             width: 100%;
             height: 100%;
             object-fit: cover; /* Cover the area, cropping if needed */
        }
         .cover-art-area .placeholder-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #9ca3af;
            font-size: 0.875rem;
            text-align: center;
        }

        /* Checkbox styling */
        .checkbox-label {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.875rem;
        }
         .checkbox-label input[type="checkbox"] {
            width: auto; /* Override default width */
            margin-right: 0.5rem;
        }

         /* Ensure gap for grid layouts */
         .grid { gap: 1rem; }
         .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
         .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
         .col-span-1 { grid-column: span 1 / span 1; }
         .col-span-2 { grid-column: span 2 / span 2; }

         /* Flex layout for output dir */
         .output-dir-flex { display: flex; }
         .output-dir-flex input { border-top-right-radius: 0; border-bottom-right-radius: 0; }
         .output-dir-flex button { border-top-left-radius: 0; border-bottom-left-radius: 0; margin-top: 0; /* Align button */ }

         /* Radio buttons container */
        .radio-group label {
            display: inline-flex; /* Align radio buttons horizontally */
            align-items: center;
            margin-right: 1rem; /* Space between options */
            font-weight: normal; /* Normal weight for radio labels */
        }
        .radio-group input[type="radio"] {
            width: auto; /* Override default width */
            margin-right: 0.3rem;
        }

        /* Status Bar alignment */
        .status-panel-content {
             display: flex;
             align-items: center;
             gap: 1rem;
        }
        .progress-details {
             flex-grow: 1; /* Allow progress bar area to take space */
        }
        .progress-bar-bg {
            background-color: #e5e7eb; /* bg-gray-200 */
            border-radius: 9999px; /* rounded-full */
            height: 0.625rem; /* h-2.5 */
            overflow: hidden; /* Ensure inner bar stays rounded */
        }
        .progress-bar-fg {
            background-color: #3b82f6; /* bg-blue-600 */
            height: 100%;
            border-radius: 9999px;
            transition: width 0.3s ease-linear; /* Smooth transition */
        }
    </style>
</head>
<body class="p-0"> <!-- Remove body padding, handle via containers -->

    <h1 class="text-2xl font-bold p-4 text-center text-gray-800 bg-white border-b border-gray-200">Audiobook Boss</h1>

    <div class="main-container">

        <!-- Input Panel -->
        <div class="panel input-panel">
            <h2 class="text-lg font-semibold mb-3 text-gray-700">Input Audio Files</h2>
            <div class="drag-drop-area mb-4">
                <p class="text-gray-500 text-sm">Drag & Drop files here or Click to Select</p>
                <p class="text-xs text-gray-400 mt-1">Supports: mp3, m4a, m4b, aac</p>
            </div>
            <h3 class="text-md font-medium mb-1 text-gray-600">File Order:</h3>
            <div class="file-list-placeholder mb-4">
                <!-- File list will be populated here dynamically -->
                 File List Area (Drag to Reorder)
                <!-- Example (static for mockup):
                <ul class="w-full text-left text-sm text-gray-700 list-none p-0 m-0">
                    <li class="p-2 border-b border-gray-200 cursor-grab">01 - Chapter One.mp3</li>
                    <li class="p-2 border-b border-gray-200 cursor-grab">02 - Chapter Two.mp3</li>
                    <li class="p-2 border-b border-gray-200 cursor-grab">03 - Chapter Three.mp3</li>
                </ul>
                 -->
            </div>

            <div class="pt-4 border-t border-gray-200 mt-auto"> <!-- Push properties to bottom -->
                <h3 class="text-md font-medium mb-2 text-gray-600">Selected File Properties</h3>
                <div class="grid grid-cols-2 gap-x-4 gap-y-1 text-sm mb-2">
                    <span class="property-label">Bitrate:</span><span class="property-value" id="prop-bitrate">--- kbps</span>
                    <span class="property-label">Sample Rate:</span><span class="property-value" id="prop-samplerate">--- Hz</span>
                    <span class="property-label">Channels:</span><span class="property-value" id="prop-channels">---</span>
                    <span class="property-label">File Size:</span><span class="property-value" id="prop-filesize">--- MB</span>
                    <span class="property-label">Combined Size:</span><span class="property-value" id="prop-combinedsize">--- MB</span>
                </div>
                <p class="text-xs text-gray-500 italic">(Properties shown for single selected file. Combined size shows total.)</p>
            </div>

            <!-- Folder Monitoring Placeholder -->
            <div class="mt-4 pt-4 border-t border-gray-200 opacity-50"> <!-- Dimmed for future state -->
                 <h3 class="text-md font-medium mb-1 text-gray-600">Folder Monitoring (Future)</h3>
                 <div class="text-sm text-gray-500 mb-2">Monitored Folder: N/A</div>
                 <button class="button-secondary text-sm w-full" disabled>Change Watched Folder</button>
            </div>
        </div>

        <!-- Metadata & Output Panel -->
        <div class="panel metadata-output-panel">
            <h2 class="text-lg font-semibold mb-3 text-gray-700">Metadata & Output</h2>
            <div class="grid grid-cols-3 gap-x-4 mb-4">
                <!-- Cover Art Column -->
                <div class="col-span-1 flex flex-col">
                     <label for="load-cover-art" class="block text-sm font-medium text-gray-700 mb-1">Cover Art</label>
                     <div class="cover-art-area mb-2">
                         <!-- Placeholder Text -->
                         <div class="placeholder-text">Drag Image Here</div>
                         <!-- Image will be placed here -->
                         <img src="" alt="Book Cover Art" id="cover-art-img" class="hidden"> <!-- Hidden initially -->
                     </div>
                     <button id="load-cover-art" class="button-secondary text-sm w-full">Load Cover Art</button>
                </div>
                 <!-- Metadata Fields Column -->
                <div class="col-span-2">
                    <div class="grid grid-cols-2 gap-x-4 gap-y-3">
                        <div>
                            <label for="meta-title">Title</label>
                            <input type="text" id="meta-title" placeholder="Book Title">
                        </div>
                         <div>
                            <label for="meta-author">Author (ALBUMARTIST)</label>
                            <input type="text" id="meta-author" placeholder="Author Name">
                        </div>
                        <div>
                            <label for="meta-album">Album (ALBUM)</label>
                            <input type="text" id="meta-album" placeholder="Usually Same as Title">
                        </div>
                         <div>
                            <label for="meta-narrator">Narrator (STYLE)</label>
                            <input type="text" id="meta-narrator" placeholder="Narrator Name">
                        </div>
                        <div>
                            <label for="meta-year">Year (YYYY)</label>
                            <input type="text" id="meta-year" placeholder="Year">
                        </div>
                        <div>
                            <label for="meta-genre">Genre(s) (semicolon sep.)</label>
                            <input type="text" id="meta-genre" placeholder="Fiction; Thriller">
                        </div>
                        <div>
                            <label for="meta-series">Series (MOOD)</label>
                            <input type="text" id="meta-series" placeholder="Series Name">
                        </div>
                         <div>
                            <label for="meta-series-pos">Series Pos. (TRACK)</label>
                            <input type="text" id="meta-series-pos" placeholder="e.g., 1 or 01">
                        </div>
                        <div class="col-span-2">
                             <label for="meta-series-sort">Series Sort (ALBUMSORT)</label>
                             <input type="text" id="meta-series-sort" placeholder="e.g., Series Name 01">
                        </div>

                    </div>
                     <div class="mt-3">
                        <label for="meta-description">Description (COMMENT)</label>
                        <textarea id="meta-description" rows="4" placeholder="Enter book description..."></textarea>
                    </div>
                </div>
            </div>

            <!-- Output Settings Section -->
            <h3 class="text-md font-medium mb-2 text-gray-600 pt-4 border-t border-gray-200">Output Settings</h3>
            <div class="grid grid-cols-3 gap-x-4 mb-4">
                 <div>
                    <label for="output-bitrate">Bitrate (kbps)</label>
                    <select id="output-bitrate">
                        <option value="32">32</option>
                        <option value="48">48</option>
                        <option value="56">56</option>
                        <option value="64" selected>64 (Default)</option>
                        <option value="96">96</option>
                        <option value="128">128</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1 italic">Re-encodes only if source > 64k, unless explicitly selected.</p>
                 </div>
                 <div>
                    <label for="output-samplerate">Sample Rate (Hz)</label>
                     <select id="output-samplerate">
                        <option value="auto" selected>Auto (Pass-through)</option>
                        <option value="22050">22050</option>
                        <option value="32000">32000</option>
                        <option value="44100">44100</option>
                        <option value="48000">48000</option>
                    </select>
                    <p class="text-xs text-gray-500 mt-1 italic">Resamples only if specific value selected.</p>
                 </div>
                 <div>
                    <label for="output-channels">Channels</label>
                     <select id="output-channels">
                        <option value="mono" selected>Mono (Default)</option>
                        <option value="stereo">Stereo</option>
                    </select>
                 </div>
            </div>
             <div class="mb-4">
                <label for="output-dir-text">Output Directory</label>
                <div class="output-dir-flex">
                    <input type="text" id="output-dir-text" placeholder="/path/to/calculated/output/location" readonly>
                    <button id="output-dir-browse" class="button-secondary">Browse...</button>
                </div>
                 <label for="output-subdir-pattern" class="checkbox-label">
                     <input type="checkbox" id="output-subdir-pattern" checked>
                     Use Default Subdirectory Pattern ([Author]/[Series]/[Year-Title]/)
                 </label>
            </div>

            <div class="mb-4">
                 <label class="block text-sm font-medium text-gray-700 mb-1">Output Filename Pattern</label>
                 <div class="radio-group">
                     <label>
                         <input type="radio" name="filename_pattern" value="title_year" checked>
                         Book Title (Year).m4b
                     </label>
                     <label>
                         <input type="radio" name="filename_pattern" value="author_title">
                         Author - Book Title.m4b
                     </label>
                 </div>
             </div>


            <div class="pt-4 border-t border-gray-200 text-sm mb-4">
                 <span class="property-label">Estimated Output Size: </span>
                 <span class="property-value" id="output-estimated-size">~ --- MB</span>
            </div>

            <div class="mt-auto pt-4 border-t border-gray-200 flex justify-end"> <!-- Push preview button to bottom -->
                 <button id="preview-button" class="button-secondary">Preview (30s)</button>
            </div>

        </div>

    </div>

    <!-- Status Panel -->
    <div class="panel status-panel">
        <div class="status-panel-content">
            <div class="flex-shrink-0">
                <!-- Thumbnail placeholder -->
                <div class="h-10 w-10 rounded object-cover border border-gray-300 bg-gray-200 flex items-center justify-center">
                     <span class="text-gray-400 text-xs">Art</span>
                </div>
                <!-- Actual Image (hidden until cover art loaded)
                <img src="" alt="Processing Cover Art" class="h-10 w-10 rounded object-cover border border-gray-300 hidden">
                -->
            </div>
            <div class="progress-details">
                 <div class="flex justify-between mb-1">
                    <span class="text-sm font-medium text-gray-700">
                        Progress:
                        <span id="hours-processed" class="property-value">--.-- / --.-- hours</span>
                        (<span id="percentage-processed" class="property-value">--%</span>)
                    </span>
                    <span id="status-text" class="text-sm font-medium text-gray-700 font-semibold">Idle</span>
                </div>
                <div class="progress-bar-bg">
                    <div id="progress-bar" class="progress-bar-fg" style="width: 0%"></div>
                </div>
                <div id="step-text" class="text-xs text-gray-500 mt-1">Current Step: Waiting for files...</div>
            </div>
            <div class="flex-shrink-0">
                <button id="process-button" class="button-primary">Process Audiobook</button>
                <!-- Button state will change dynamically, e.g., to "Cancel Processing" -->
            </div>
        </div>
    </div>

</body>
</html>
```

## PySide 6 Prototype UI Code
NOT intended for production code, but is useful to visualize the UI. There are elements missing from the HTML version that will be in production.
```Python
# audiobook_boss_pyside6.py
"""A *minimal but runnable* PySide6 prototype of the Audiobook Boss UI.

Install deps (inside a venv is recommended):

    pip install --upgrade pyside6

Run:

    python audiobook_boss_pyside6.py

The prototype focuses **only** on layout & widget plumbing—no business logic,
encoding, or drag‑drop file handling is implemented yet.  It gives you a live
window to click around, resize, and sanity‑check proportions on macOS.
"""

from __future__ import annotations

import sys
from pathlib import Path
from typing import Final

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import (
    QApplication,
    QComboBox,
    QFormLayout,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QListWidget,
    QListWidgetItem,
    QMainWindow,
    QPushButton,
    QProgressBar,
    QRadioButton,
    QSizePolicy,
    QSplitter,
    QVBoxLayout,
    QWidget,
    QFileDialog,
)

# ---------------------------------------------------------------------------
# Helper widgets
# ---------------------------------------------------------------------------

class DropZone(QLabel):
    """Simple drag‑and‑drop area for files.  Currently visual only."""

    def __init__(self, text: str = "Drag & Drop files here or Click to Select") -> None:
        super().__init__(text)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFrameStyle(QFrame.Shape.StyledPanel | QFrame.Shadow.Raised)
        self.setStyleSheet(
            "border: 2px dashed #bbb; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px;"
        )
        self.setAcceptDrops(True)

    # --- Drag‑drop events placeholders ------------------------------------
    def dragEnterEvent(self, event):  # noqa: N802 (Qt naming)
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):  # noqa: N802 (Qt naming)
        event.acceptProposedAction()

    # --- Click to open file dialog ---------------------------------------
    def mouseReleaseEvent(self, event):  # noqa: N802
        if event.button() == Qt.MouseButton.LeftButton:
            paths, _ = QFileDialog.getOpenFileNames(
                self,
                "Select audio files",
                str(Path.home()),
                "Audio Files (*.mp3 *.m4a *.m4b *.aac *.wav *.flac)",
            )
            if paths:
                print("Files chosen:", paths)


class CoverArt(QLabel):
    """Square placeholder for cover art (drop or click to load)."""

    MIN_SIZE: Final[int] = 180

    def __init__(self):
        super().__init__("Drag Image Here")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFixedSize(QSize(self.MIN_SIZE, self.MIN_SIZE))
        self.setStyleSheet(
            "background: #e5e7eb; color: #9ca3af; border: 1px solid #d1d5db;"
            "font-size: 12px;"
        )
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event):  # noqa: N802
        if event.mimeData().hasImage() or event.mimeData().hasUrls():
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):  # noqa: N802
        if event.mimeData().hasUrls():
            url = event.mimeData().urls()[0]
            pixmap = QPixmap(url.toLocalFile()).scaled(
                self.size(), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
            )
            self.setPixmap(pixmap)
            self.setText("")
            event.acceptProposedAction()

    def mouseReleaseEvent(self, event):  # noqa: N802
        if event.button() == Qt.MouseButton.LeftButton:
            file, _ = QFileDialog.getOpenFileName(
                self, "Select cover art", str(Path.home()), "Images (*.png *.jpg *.jpeg *.webp *.bmp)"
            )
            if file:
                self.setPixmap(QPixmap(file).scaled(
                    self.size(), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation
                ))
                self.setText("")


# ---------------------------------------------------------------------------
# Main Window
# ---------------------------------------------------------------------------

class MainWindow(QMainWindow):
    """Audiobook Boss prototype main window."""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(1280, 820)
        self._build_ui()

    # ---------------------------------------------------------------------
    # UI construction
    # ---------------------------------------------------------------------

    def _build_ui(self) -> None:
        central = QWidget()
        self.setCentralWidget(central)
        root = QVBoxLayout(central)
        root.setContentsMargins(12, 12, 12, 12)
        root.setSpacing(8)

        splitter = QSplitter(Qt.Orientation.Horizontal)
        root.addWidget(splitter, 1)

        # -------------------------------------------------- Left panel ----
        splitter.addWidget(self._build_left_panel())

        # -------------------------------------------------- Right panel ---
        splitter.addWidget(self._build_right_panel())
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)

        # -------------------------------------------------- Status bar ----
        root.addWidget(self._build_status_bar())

    # ..............................................................
    def _build_left_panel(self) -> QWidget:
        left = QWidget()
        lyt = QVBoxLayout(left)
        lyt.setSpacing(8)

        drop = DropZone()
        lyt.addWidget(drop)

        self.file_list = QListWidget()
        lyt.addWidget(self.file_list, 1)  # stretch

        props_box = QGroupBox("Selected File Properties")
        props_form = QFormLayout(props_box)
        for lbl in ("Bitrate", "Sample Rate", "Channels", "File Size", "Combined Size"):
            props_form.addRow(lbl + ":", QLabel("—"))
        lyt.addWidget(props_box)

        folder_btn = QPushButton("Change Watched Folder (Future)")
        folder_btn.setEnabled(False)
        lyt.addWidget(folder_btn)

        return left

    # ..............................................................
    def _build_right_panel(self) -> QWidget:
        right = QWidget()
        v = QVBoxLayout(right)
        v.setSpacing(8)

        meta_out = QGroupBox("Metadata & Output")
        grid = QGridLayout(meta_out)
        grid.setHorizontalSpacing(6)
        grid.setVerticalSpacing(4)

        cover = CoverArt()
        grid.addWidget(cover, 0, 0, 5, 1)  # rowspan 5 rows

        # Row/col helpers
        def add(row: int, col: int, widget: QWidget, span: int = 1):
            grid.addWidget(widget, row, col, 1, span)

        # Metadata fields --------------------------------------------------
        title = QLineEdit();   add(0, 1, title, 2)  # span 2 columns
        title.setPlaceholderText("Book Title")
        author = QLineEdit();  add(0, 3, author)
        author.setPlaceholderText("Author Name")

        album = QLineEdit();   add(1, 1, album, 2)
        album.setPlaceholderText("Usually same as Title")
        narrator = QLineEdit(); add(1, 3, narrator)
        narrator.setPlaceholderText("Narrator Name")

        year = QLineEdit();    add(2, 1, year)
        year.setPlaceholderText("Year")
        genres = QLineEdit();  add(2, 3, genres)
        genres.setPlaceholderText("Fiction; Thriller")

        series = QLineEdit();  add(3, 1, series)
        series.setPlaceholderText("Series Name")
        series_pos = QLineEdit(); add(3, 3, series_pos)
        series_pos.setPlaceholderText("e.g., 1 or 01")

        series_sort = QLineEdit(); add(4, 1, series_sort)
        series_sort.setPlaceholderText("Series Sort (ALBUMSORT)")
        desc = QLineEdit();    add(4, 3, desc)
        desc.setPlaceholderText("Description (COMMENT)")

        # Output settings --------------------------------------------------
        out_group = QGroupBox("Output Settings")
        out_form = QFormLayout(out_group)

        bitrate = QComboBox(); bitrate.addItems(["64 (Default)", "96", "128", "192", "256", "320"])
        out_form.addRow("Bitrate (kbps)", bitrate)

        sample = QComboBox(); sample.addItems(["Auto (Pass‑through)", "44100", "48000"])
        out_form.addRow("Sample Rate (Hz)", sample)

        channels = QComboBox(); channels.addItems(["Mono (Default)", "Stereo"])
        out_form.addRow("Channels", channels)

        output_dir = QLineEdit(); browse_btn = QPushButton("Browse…")
        h_dir = QHBoxLayout(); h_dir.addWidget(output_dir, 1); h_dir.addWidget(browse_btn)
        out_form.addRow("Output Directory", h_dir)

        patt_box = QWidget(); patt_lyt = QVBoxLayout(patt_box); patt_lyt.setContentsMargins(0, 0, 0, 0)
        patt_lyt.addWidget(QRadioButton("Book Title (Year).m4b"))
        patt_lyt.addWidget(QRadioButton("Author ‑ Book Title.m4b"))
        out_form.addRow("Output Filename Pattern", patt_box)

        est_label = QLabel("Estimated Output Size:  —  MB")
        out_form.addRow(est_label)

        # Add to main right‑side layout
        v.addWidget(meta_out)
        v.addWidget(out_group)

        preview_btn = QPushButton("Preview (30s)")
        preview_btn.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        preview_btn.setFixedWidth(120)
        v.addWidget(preview_btn, alignment=Qt.AlignmentFlag.AlignRight)

        return right

    # ..............................................................
    def _build_status_bar(self) -> QWidget:
        bar = QWidget()
        h = QHBoxLayout(bar)
        h.setContentsMargins(0, 0, 0, 0)

        self.progress = QProgressBar()
        self.progress.setRange(0, 100)
        self.progress.setValue(0)
        self.progress.setTextVisible(False)
        h.addWidget(self.progress, 1)

        self.status_label = QLabel("Idle")
        self.status_label.setMinimumWidth(120)
        h.addWidget(self.status_label)

        process_btn = QPushButton("Process Audiobook")
        h.addWidget(process_btn)

        return bar


# ---------------------------------------------------------------------------
# Entrypoint
# ---------------------------------------------------------------------------

if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec())
```
## **Prototype UI Images**

[HTML Prototype (Represents nearest final production version)](https://drive.google.com/file/d/1Ksuk-GRq_DClVRcKlKgTvo5GZtbbZ0EM/view?usp=drive_link)
[PySide 6 Prototype (Approximate representation of the HTML version)](https://drive.google.com/file/d/1fCAZ5ZtAqI1Z2R4kz44c6qbK2o2Bjc5R/view?usp=drive_link)

# **Original Script**
NOT INTENDED TO BE USED AS FINAL PRODUCTION CODE!
This script was used for a couple of years to manually target audio files (to be encoded) via terminal. The script had to exist in the same folder as the target audio files and was intended for one file at a time. We can use this script to inspire production code in ABB.

```bash
#!/bin/bash

for input_file in *.m4b *.mp3 *.m4a; do
    if [ -f "$input_file" ]; then
        # Extract current properties once and store in variables
        current_bit_rate=$(ffprobe -v error -select_streams a -show_entries stream=bit_rate -of default=noprint_wrappers=1:nokey=1 "$input_file")
        current_sample_rate=$(ffprobe -v error -select_streams a -show_entries stream=sample_rate -of default=noprint_wrappers=1:nokey=1 "$input_file")
        current_channels=$(ffprobe -v error -select_streams a -show_entries stream=channels -of default=noprint_wrappers=1:nokey=1 "$input_file")
        artist=$(ffprobe -v error -show_entries format_tags=artist -of default=noprint_wrappers=1:nokey=1 "$input_file")
        title=$(ffprobe -v error -show_entries format_tags=title -of default=noprint_wrappers=1:nokey=1 "$input_file")
        
        # Determine conversion requirements
        convert_bit_rate=64k
        convert_sample_rate=48k
        convert_channels=1
        
        [ "$current_bit_rate" -le 64000 ] && convert_bit_rate=""
        [ "$current_sample_rate" -le 48000 ] && convert_sample_rate=""
        [ "$current_channels" -le 1 ] && convert_channels=""
        
        # Skip file if no conversion needed
        if [ -z "$convert_bit_rate" ] && [ -z "$convert_sample_rate" ] && [ -z "$convert_channels" ]; then
            echo "Skipping $input_file (already meets preferred settings)"
            continue
        fi
        
        # Create output directory and file name
        output_dir="$(dirname "$input_file")/$artist"
        mkdir -p "$output_dir"
        output_file="${output_dir}/${artist} - ${title}.m4b"
        
        # Construct ffmpeg command
        #ffmpeg_command="ffmpeg -i \"$input_file\" -vn -c:a libfdk_aac -b:a $convert_bit_rate" #original command
        ffmpeg_command="ffmpeg -i \"$input_file\" -map 0:a -map 0:v? -c:a libfdk_aac -c:v copy -b:a $convert_bit_rate" #maps all audio and video (if exists)
        [ -n "$convert_sample_rate" ] && ffmpeg_command+=" -ar $convert_sample_rate"
        [ -n "$convert_channels" ] && ffmpeg_command+=" -ac $convert_channels"
        ffmpeg_command+=" -map_metadata 0 -map_chapters 0 -y \"$output_file\""
        
        # Execute the conversion
        eval "$ffmpeg_command"
        
        echo "Converted $input_file"
    fi
done
```