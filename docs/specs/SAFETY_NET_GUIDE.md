# Phase 0 Safety Net - Trip Wire Infrastructure Guide

## What is the Trip Wire Safety Net?

The safety net is a **multi-layered validation system** that prevents regressions during refactoring by automatically catching breaking changes before they reach production. It consists of:

1. **CI/CD Pipeline** - Automated testing on every code change
2. **Golden Run Baseline** - Bit-perfect reference output for regression detection  
3. **Hybrid Test Strategy** - Fast feedback + comprehensive validation
4. **Automated Quality Gates** - Linting, coverage, and integration checks

**Purpose**: Provide immediate feedback when refactoring breaks existing functionality, allowing safe incremental changes to the codebase.

## How to Use the Safety Net

### 🚀 Quick Start Validation

Before making **any** refactoring changes, validate the safety net is working:

```bash
# 1. Ensure all tests pass
python -m pytest

# 2. Run the golden baseline test
pytest tests/integration/test_golden.py -v

# 3. Check code quality
ruff check src

# 4. Generate coverage report
pytest --cov=abb --cov-report=html
open htmlcov/index.html  # View coverage in browser
```

**Expected Outcome**: All tests green ✅, golden test passes, no lint errors, coverage >70%

### 🔄 Development Workflow

#### Before Starting Refactoring:
```bash
# Validate current state
git status                    # Ensure clean working directory
pytest tests/integration/test_golden.py -v  # Confirm golden baseline works
```

#### During Development:
```bash
# Fast feedback loop (runs in ~30 seconds)
pytest tests/unit -v         # Unit tests only
pytest tests/integration/test_file_import_integration.py -v  # Core integration

# Before committing changes
pytest                        # Full test suite
ruff check src               # Code quality check
```

#### Before Creating Pull Request:
```bash
# Trigger full CI validation
git commit -m "your changes [ci full]"  # Forces complete test suite
git push origin your-branch
```

### 📊 Test Categories

The safety net uses a **hybrid testing strategy** with different execution triggers:

| Test Type            | Speed | When It Runs                     | Purpose                          |
| -------------------- | ----- | -------------------------------- | -------------------------------- |
| **Unit Tests**       | ~10s  | Every push/PR                    | Fast feedback on logic changes   |
| **Fast Integration** | ~30s  | Every push/PR                    | Core workflow validation         |
| **Full Suite**       | ~2min | PRs to dev, main pushes, nightly | Comprehensive regression testing |
| **Golden Baseline**  | ~30s  | Main branch, `[ci golden]`       | Bit-perfect output validation    |

## When the Trip Wire Activates

### 🔴 CI Trigger Conditions

The safety net activates automatically under these conditions:

#### Fast Tests (Always Run):
- Any push to any branch
- Any pull request creation/update
- **Runs**: Unit tests + fast integration tests + linting

#### Full Test Suite:
- Pull requests targeting `dev` branch
- Pushes to `main` branch  
- Nightly at 2:00 UTC
- Manual trigger with commit message `[ci full]`
- **Runs**: All tests + coverage reporting

#### Golden Baseline Tests:
- Pushes to `main` branch
- Manual trigger with commit message `[ci golden]`
- **Runs**: Bit-perfect output validation

### 🚨 Failure Scenarios

The trip wire will **block your changes** if:

1. **Unit/Integration Tests Fail** - Logic regressions detected
2. **Golden Test Fails** - Output no longer matches baseline
3. **Lint Errors** - Code quality standards violated
4. **Coverage Drops** - Test coverage falls below threshold

## Why the Safety Net is Critical

### 🛡️ Regression Prevention

**Before Safety Net:**
- Manual testing only
- No automated validation
- Breaking changes discovered by users
- Difficult to isolate when breakage occurred

**With Safety Net:**
- Immediate feedback on every change
- Bit-perfect validation of core functionality
- Automatic blocking of problematic changes
- Clear failure attribution

### 📈 Refactoring Confidence

The safety net enables **fearless refactoring** by:

1. **Early Detection** - Catch issues in minutes, not days
2. **Precise Validation** - Know exactly what broke and when
3. **Rollback Safety** - Easy to revert to last known good state
4. **Incremental Progress** - Make small, validated changes

### 🎯 Quality Assurance

Maintains code quality through:
- **Automated Linting** - Consistent code style
- **Coverage Tracking** - Ensure tests keep pace with code
- **Integration Validation** - Real workflow testing
- **Golden Standard** - Preserve exact functionality

## Commands Reference

### Essential Commands

```bash
# Basic validation
pytest                                    # Run all tests
pytest tests/unit -v                     # Unit tests only  
pytest tests/integration/test_golden.py -v  # Golden baseline test
ruff check src                           # Lint code

# Coverage analysis
pytest --cov=abb --cov-report=html      # Generate HTML coverage report
pytest --cov=abb --cov-report=xml       # Generate XML for CI

# Force specific CI behavior
git commit -m "changes [ci full]"       # Force full test suite
git commit -m "changes [ci golden]"     # Force golden test
```

### Test Filtering

```bash
# Run only fast tests
pytest -m fast

# Run only slow tests  
pytest -m slow

# Run specific test categories
pytest tests/unit/services/            # Service layer tests
pytest tests/integration/              # Integration tests
pytest tests/features/                 # Feature tests
```

## Troubleshooting Common Issues

### ❌ Golden Test Failure

**Symptom**: `test_golden.py` fails with hash mismatch

**Diagnosis**:
```bash
# Check if golden files exist
ls -la tests/data/golden/

# Manually run golden test with verbose output
pytest tests/integration/test_golden.py -v -s
```

**Solutions**:
1. **Expected change**: Update golden baseline after verifying change is intentional
2. **Regression**: Revert code changes and investigate what broke
3. **Environment issue**: Ensure FFmpeg is available and working

### ❌ CI Build Failures

**Symptom**: GitHub Actions showing red ❌

**Check logs**:
1. Go to GitHub → Actions tab
2. Click on failed workflow
3. Expand failing step logs

**Common fixes**:
```bash
# Lint errors
ruff check src                          # See what needs fixing
ruff check src --fix                    # Auto-fix what's possible

# Test failures  
pytest tests/unit -v                    # Run failing test locally
pytest --pdb                            # Debug failing test
```

### ❌ Dependency Issues

**Symptom**: Import errors or missing packages

**Fix**:
```bash
# Ensure all dependencies installed
pip install -r requirements.txt
pip install pytest pytest-cov ruff

# Check Python version
python --version                        # Should be 3.9+
```

### ❌ Permission/Path Issues

**Symptom**: File not found or permission errors

**Check**:
```bash
# Verify working directory
pwd                                     # Should be project root
ls -la tests/data/golden/              # Verify golden files exist

# Check file permissions
chmod +r tests/data/golden/*           # Ensure files are readable
```

## Safety Net Health Check

Run this complete validation to ensure the safety net is working properly:

```bash
#!/bin/bash
echo "🔍 Safety Net Health Check"
echo "=========================="

echo "✅ 1. Unit Tests"
pytest tests/unit -q || echo "❌ Unit tests failed"

echo "✅ 2. Integration Tests" 
pytest tests/integration -q || echo "❌ Integration tests failed"

echo "✅ 3. Golden Baseline"
pytest tests/integration/test_golden.py -q || echo "❌ Golden test failed"

echo "✅ 4. Code Quality"
ruff check src || echo "❌ Lint errors found"

echo "✅ 5. Coverage Check"
pytest --cov=abb --cov-report=term --cov-fail-under=70 -q || echo "❌ Coverage below 70%"

echo ""
echo "🎉 If all checks passed, safety net is operational!"
echo "🚀 Ready for safe refactoring"
```

---

**Remember**: The safety net is only effective if you use it. Run tests frequently, especially before and after making changes. The few seconds spent on validation can save hours of debugging later.

# Reminders for running the safety net

**During development (every 15-20 min)**
  `pytest tests/unit -v && ruff check src`    # Fast feedback (15s)

**Before push (your suggestion from safety guide)**  
  `pytest && ruff check src`                  # Full validation (1-2min)

**End of session (your current pattern)**
  `scripts/run_reports.sh`                    # Comprehensive reporting (3min)