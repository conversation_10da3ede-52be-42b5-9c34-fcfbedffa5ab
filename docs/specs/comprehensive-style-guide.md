# **Audiobook Boss - Comprehensive UI Style Guide**

## **1. General Principles**

* **Consistency:** All UI elements must follow guidelines in this document for a consistent look and feel.
* **Layout Priority:** *Preventing scrolling issues is the highest priority concern.* The UI must display all content without requiring scrollbars at the 1280x820 size.
* **Reference Adherence:** Implementations **must** visually match the HTML prototype (preferred for visual styling) within PySide6 capabilities.
* **Clarity:** UI should be intuitive and easy to understand.
* **Responsiveness:** The UI must reflow reasonably when resized. Avoid fixed positions; use layouts and stretch factors correctly.

## **2. Layout & Dimensions**

* **Main Window (MainWindow):**  
  * Initial Size: 1280 x 820 pixels
  * Minimum Size: 1100 x 700 pixels (prevents scroll issues)
  * Structure hierarchy:  
    ```
    QMainWindow
     │
     ├── Central Widget (QWidget)
     │    └── Root Layout (QVBoxLayout)
     │         ├── Title Bar (QLabel with styling)
     │         │
     │         ├── Main Content Area (QSplitter - HORIZONTAL)
     │         │    ├── Input Panel (QWidget with QVBoxLayout - 33% width)
     │         │    └── Metadata/Output Panel (QWidget with QVBoxLayout - 67% width)
     │         │
     │         └── Status Bar (QWidget with QHBoxLayout)
    ```

* **CRITICAL - Size Proportions:**
  * Splitter Ratio: 33% left panel, 67% right panel (set with `setStretchFactor(0, 1)` and `setStretchFactor(1, 2)`)
  * Initial splitter sizes must be set with:
    ```python
    total_width = self.width()
    splitter.setSizes([int(total_width * 0.33), int(total_width * 0.67)])
    ```
  * The splitter handle should be visible and functional.
 
* **Panel Heights:**
  * Left and right panels must be configured with proper size policies to avoid scrolling:
    ```python
    left_panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
    right_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
    ```
  * Set appropriate minimum heights but avoid maximum heights:
    ```python
    left_panel.setMinimumHeight(500)
    right_panel.setMinimumHeight(500)
    ```

* **Cover Art Widget (CoverArtWidget):**  
  * Fixed Size: 180 x 180 pixels
  * Implementation: `self.setFixedSize(QSize(180, 180))`

* **Status Bar:** 
  * Height: Fixed 60px or determined by content
  * Spans the full width below the splitter
  * Fixed widgets should have appropriate size policies:
    ```python
    process_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
    process_btn.setMinimumWidth(140)
    ```

## **3. Typography**

* **Font Family:**  
  * Primary: System default font provided by PySide6/Qt for macOS
  * Attempt to use "Inter" only if easily configurable without complex bundling
  
* **Font Sizes & Weights:**  
  * Window Title: Default QMainWindow title style
  * Section Headings: Standard QGroupBox title font
  * Labels: Standard QLabel font size
  * Input Fields: Standard widget font size
  * Button Text: Standard button font size
  * Helper Text: Standard QLabel font size, potentially italic
  * Property Values: Consider using Monospace font for property values

## **4. Color Palette**

* **Backgrounds:**  
  * Main Window/Area: #f3f4f6 (Light Gray)
  * Panels: #ffffff (White)
  * DropZone (Resting): #fafafa (Off-white)
  * DropZone (Hover): #f0f0f0 (Slightly darker off-white)
  * CoverArtWidget Placeholder: #e5e7eb (Gray)
  * QProgressBar Background: #e5e7eb (Gray)
  
* **Text:**  
  * Standard Labels/Text: #374151 (Dark Gray)
  * Secondary/Helper Text: #6b7280 (Medium Gray)
  * DropZone Placeholder Text: #6b7280 (Medium Gray)
  * CoverArtWidget Placeholder Text: #9ca3af (Light Gray)
  * Button Text (Primary/Secondary): #ffffff (White)
  
* **Borders:**  
  * Panel/GroupBox Borders: #e5e7eb (Light Gray), 1px solid
  * Input Fields: #d1d5db (Gray), 1px solid
  * DropZone Border: #d1d5db (Gray), 2px dashed
  * CoverArtWidget Border: #d1d5db (Gray), 1px solid
  
* **Buttons:**  
  * Primary (Process): Background #3b82f6 (Blue), Hover: #2563eb (Darker Blue)
  * Secondary (Browse, Load Cover): Background #6b7280 (Gray), Hover: #4b5563 (Darker Gray)
  
* **Other:**  
  * QProgressBar Foreground: #3b82f6 (Blue)
  * Input Field Focus Ring: #a5b4fc (Light Blue) or similar blue shade

## **5. Spacing & Padding**

* **General Margins:** 
  * Root layout: `root.setContentsMargins(12, 12, 12, 12)`
  
* **Layout Spacing:** 
  * Consistent spacing: `layout.setSpacing(8)` or `layout.setSpacing(10)`
  
* **Widget Padding:**  
  * DropZone: Significant internal padding (24px)
  * Input Fields: ~8px padding (adjust via QSS if needed)
  * Buttons: ~8px vertical, 16px horizontal (standard Qt button padding usually works)
  
* **QFormLayout/QGridLayout Spacing:** 
  * Use consistent horizontal and vertical spacing
  * `grid.setHorizontalSpacing(6)`
  * `grid.setVerticalSpacing(4)`

## **6. Panel Layout Details**

### **6.1 Input Panel Layout (Left Side)**

```
Input Panel (33% of splitter width)
 │
 ├── Title Label: "Input Audio Files" (QLabel)
 │
 ├── Drag & Drop Area (Custom QLabel)
 │
 ├── File Order Label (QLabel)
 │
 ├── File List (QListWidget - STRETCHES to fill space)
 │
 ├── Selected File Properties (QGroupBox with QFormLayout)
 │
 └── Folder Monitoring Section (QGroupBox - future feature, dimmed)
```

* **Critical Size Policies:**
  * FileList: `setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)`
  * FileList: `setMinimumHeight(150)` to prevent squishing
  * Properties Section: `setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)`

### **6.2 Metadata & Output Panel Layout (Right Side)**

```
Metadata & Output Panel (67% of splitter width)
 │
 ├── Title Label: "Metadata & Output" (QLabel)
 │
 ├── Metadata Grid (QGridLayout)
 │    ├── Cover Art (Left Column, spans multiple rows)
 │    └── Metadata Fields (Right Columns - arranged in grid)
 │
 ├── Output Settings Section (QGroupBox)
 │    ├── Audio Settings (Grid Layout - 3 columns)
 │    ├── Output Directory (HBox Layout with field and button)
 │    ├── Output Filename Pattern (Radio Buttons in group)
 │    └── Estimated Output Size (QLabel)
 │
 └── Preview Button (Right-aligned QPushButton)
```

* **Cover Art Positioning:**
  ```python
  # Position cover art spanning 5 rows in the grid
  grid.addWidget(cover_art, 0, 0, 5, 1)
  ```

* **Metadata Fields Arrangement:**
  Fields should be arranged in a grid pattern as in the HTML mockup.

### **6.3 Status Bar Layout**

```
Status Bar
 │
 ├── Thumbnail Area (Fixed size, left-aligned)
 │
 ├── Progress Details (Horizontal layout, expanding)
 │    ├── Status Text (Top)
 │    ├── Progress Bar (Middle)
 │    └── Step Description (Bottom)
 │
 └── Process Button (Fixed size, right-aligned)
```

## **7. Widget Dictionary & Implementation**

### **7.1 Custom Widgets Required**

* **DropZone (Custom QLabel):**
  ```python
  class DropZone(QLabel):
      def __init__(self, text: str = "Drag & Drop files here or Click to Select") -> None:
          super().__init__(text)
          self.setAlignment(Qt.AlignmentFlag.AlignCenter)
          self.setFrameStyle(QFrame.Shape.StyledPanel | QFrame.Shadow.Raised)
          self.setStyleSheet(
              "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
              "font-size: 13px; padding: 24px; background-color: #fafafa;"
          )
          self.setAcceptDrops(True)
  ```

* **CoverArt (Custom QLabel):**
  ```python
  class CoverArt(QLabel):
      def __init__(self):
          super().__init__("Drag Image Here")
          self.setAlignment(Qt.AlignmentFlag.AlignCenter)
          self.setFixedSize(QSize(180, 180))
          self.setStyleSheet(
              "background: #e5e7eb; color: #9ca3af; border: 1px solid #d1d5db;"
              "font-size: 12px; border-radius: 4px;"
          )
          self.setAcceptDrops(True)
  ```

### **7.2 Standard Widget Implementation**

* **File List:**
  ```python
  self.file_list = QListWidget()
  self.file_list.setObjectName("fileListWidget")
  self.file_list.setDragDropMode(QAbstractItemView.InternalMove)
  self.file_list.setSelectionMode(QAbstractItemView.ExtendedSelection)
  self.file_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
  self.file_list.setMinimumHeight(150)
  ```

* **Properties Group:**
  ```python
  props_box = QGroupBox("Selected File Properties")
  props_box.setObjectName("propertiesGroup")
  props_form = QFormLayout(props_box)
  for lbl in ("Bitrate", "Sample Rate", "Channels", "File Size", "Combined Size"):
      value_label = QLabel("—")
      value_label.setObjectName(f"value_{lbl.lower().replace(' ', '_')}")
      value_label.setStyleSheet("font-family: monospace;")
      props_form.addRow(f"{lbl}:", value_label)
  ```

* **Process Button:**
  ```python
  process_btn = QPushButton("Process Audiobook")
  process_btn.setObjectName("processButton")
  process_btn.setProperty("buttonStyle", "primary")
  process_btn.setMinimumWidth(140)
  process_btn.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
  ```

* **Progress Bar:**
  ```python
  self.progress = QProgressBar()
  self.progress.setRange(0, 100)
  self.progress.setValue(0)
  self.progress.setTextVisible(False)
  self.progress.setObjectName("progressBar")
  self.progress.setMinimumHeight(10)
  self.progress.setMaximumHeight(10)
  ```

## **8. Critical Size Policies & Constraints**

This section is **THE MOST IMPORTANT** for avoiding scrolling issues that have plagued previous implementations.

### **8.1 QSplitter Configuration**

```python
# Critical splitter configuration to prevent scrolling issues
splitter = QSplitter(Qt.Orientation.Horizontal)
splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

# Set proportional sizes (33% left, 67% right)
left_panel = self._build_left_panel()
right_panel = self._build_right_panel()
splitter.addWidget(left_panel)
splitter.addWidget(right_panel)

# Set stretch factors
splitter.setStretchFactor(0, 1)  # Left panel (Input)
splitter.setStretchFactor(1, 2)  # Right panel (Metadata & Output)

# Handle initial sizing
total_width = self.width()
splitter.setSizes([int(total_width * 0.33), int(total_width * 0.67)])
```

### **8.2 Panel Size Policies**

```python
# Left panel (must stretch vertically)
left_panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)

# File list (must expand vertically to take available space)
file_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

# Right panel (must handle overflow with scroll if needed)
right_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
```

### **8.3 Widget Minimum Heights**

```python
# Set minimum heights to prevent squishing
file_list.setMinimumHeight(150)
left_panel.setMinimumHeight(500)
right_panel.setMinimumHeight(500)
```

### **8.4 Layout Margins and Spacing**

```python
# Root layout margins
root_layout.setContentsMargins(12, 12, 12, 12)
root_layout.setSpacing(8)

# Panel layout margins (can be zero if the panel has its own border)
left_layout.setContentsMargins(10, 10, 10, 10)
left_layout.setSpacing(8)

# Grid layout spacing
grid.setHorizontalSpacing(6)
grid.setVerticalSpacing(4)
```

## **9. Qt Style Sheets (QSS)**

Apply these using `app.setStyleSheet(...)` or `widget.setStyleSheet(...)`. 

```css
/* General Widget Styling */
QWidget {
    /* Default font can be set here if needed, but rely on system default first */
    /* font-family: "Inter", sans-serif; */
}

QLabel {
    color: #374151; /* Default text color */
    background-color: transparent; /* Ensure labels don't have white background */
}

QLineEdit, QTextEdit, QComboBox {
    border: 1px solid #d1d5db; /* Gray-300 */
    border-radius: 5px;
    padding: 6px 8px; /* Adjust as needed */
    background-color: #ffffff;
    color: #374151;
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
    border: 1px solid #6366f1; /* Indigo-500 */
}

QLineEdit[readOnly="true"] {
    background-color: #f3f4f6; /* Gray-100 */
    color: #6b7280; /* Gray-500 */
    border: 1px solid #e5e7eb; /* Gray-200 */
}

QGroupBox {
    background-color: #ffffff;
    border: 1px solid #e5e7eb; /* Gray-200 */
    border-radius: 6px;
    margin-top: 10px; /* Space for title */
    padding: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 0 5px 5px 5px; /* Adjust padding around title */
    color: #374151;
    /* font-weight: bold; */ /* Optional */
}

QPushButton {
    border-radius: 5px;
    padding: 8px 16px; /* Adjust as needed */
    font-weight: 500; /* Medium weight - may not work on all platforms */
    border: none; /* Assuming solid background provides definition */
}

QPushButton[buttonStyle="primary"] {
    background-color: #3b82f6; /* Blue-500 */
    color: white;
}
QPushButton[buttonStyle="primary"]:hover {
    background-color: #2563eb; /* Blue-600 */
}
QPushButton[buttonStyle="primary"]:pressed {
    background-color: #1d4ed8; /* Blue-700 */
}
QPushButton[buttonStyle="primary"]:disabled {
    background-color: #93c5fd; /* Blue-300 */
}

QPushButton[buttonStyle="secondary"] {
    background-color: #6b7280; /* Gray-500 */
    color: white;
}
QPushButton[buttonStyle="secondary"]:hover {
    background-color: #4b5563; /* Gray-600 */
}
QPushButton[buttonStyle="secondary"]:pressed {
    background-color: #374151; /* Gray-700 */
}
QPushButton[buttonStyle="secondary"]:disabled {
    background-color: #d1d5db; /* Gray-300 */
}

QListWidget#fileListWidget {
  border: 1px solid #d1d5db;
  border-radius: 5px;
  background-color: #ffffff;
  padding: 5px; /* Padding inside the list view */
}

/* Style for DropZone (assuming it's a QLabel with object name) */
QLabel#dropZoneLabel {
    border: 2px dashed #d1d5db;
    border-radius: 6px;
    background-color: #fafafa;
    color: #6b7280;
    qproperty-alignment: 'AlignCenter';
    padding: 24px; /* Internal padding */
    /* Minimum height might be useful */
    /* min-height: 100px; */
}

/* Style for CoverArt (assuming it's a QLabel with object name) */
QLabel#coverArtLabel {
    border: 1px solid #d1d5db;
    background-color: #e5e7eb; /* Placeholder background */
    color: #9ca3af; /* Placeholder text color */
    qproperty-alignment: 'AlignCenter';
    /* Fixed size is set in code (180x180) */
}

QProgressBar {
    border: none; /* Remove default border */
    background-color: #e5e7eb; /* Groove color */
    border-radius: 5px;
    min-height: 10px;
    max-height: 10px;
    text-align: center; /* Although text is hidden */
}

QProgressBar::chunk {
    background-color: #3b82f6; /* Progress color */
    border-radius: 5px;
    /* No margin needed if radii match */
}

/* Style for Splitter Handle (Optional - can make it more visible) */
QSplitter::handle:horizontal {
    background-color: #d1d5db; /* Light gray handle */
    width: 3px; /* Make handle slightly thicker */
    margin: 1px 0;
}
QSplitter::handle:horizontal:hover {
    background-color: #9ca3af; /* Darker on hover */
}
```

## **10. Implementation Strategy**

To ensure the UI matches the mockup exactly, implement in the following order:

1. Create the skeleton window with main layout structure
2. Implement the left panel (Input panel) with proper sizing
3. Implement the right panel (Metadata & Output) with proper sizing
4. Implement the status bar
5. Apply styling through style sheets
6. Test resizing behavior thoroughly
7. Connect basic signals and slots for UI interaction

**CRITICAL:** If any panel content requires scrolling in a window at 1280x820, STOP and fix the layout issues - this indicates a problem with the size policies or constraints.

## **11. UI Validation Checklist**

* **Layout & Structure:**  
  * [ ] Main window opens at 1280x820.  
  * [ ] Main layout uses QVBoxLayout.  
  * [ ] QSplitter correctly divides Left/Right panels horizontally.  
  * [ ] Splitter ratio initially favors the Right panel (~1:2).  
  * [ ] Status bar is positioned correctly at the bottom.  
  * [ ] Left/Right panel contents use appropriate layouts (QVBoxLayout, QFormLayout, QGridLayout).  
  * [ ] **CRITICAL:** No scrollbars appear at 1280x820 window size
  * [ ] Window resizing behaves reasonably (widgets resize/reflow, no major overlaps).  

* **Widget Implementation:**  
  * [ ] Correct Qt Widgets used for each element (e.g., QListWidget for file list, QLineEdit for text input)?  
  * [ ] Custom widgets (DropZone, CoverArtWidget) implemented correctly?  
  * [ ] Object names set for widgets requiring specific QSS styling (e.g., #fileListWidget, #dropZoneLabel)?  
  * [ ] Custom properties set for buttons (buttonStyle="primary" / "secondary")?  
  * [ ] Size policies correctly set for all widgets (especially those that should expand)?
  * [ ] Minimum heights set appropriately to prevent squishing?

* **Styling & Appearance:**  
  * [ ] Background colors match the palette (Main area, Panels)?  
  * [ ] Border styles match (Panels, Inputs, DropZone, CoverArt)?  
  * [ ] Border radii applied correctly (Inputs, Buttons, Progress Bar)?  
  * [ ] Text colors match the palette for different contexts?  
  * [ ] Button colors (background, text, hover) match primary/secondary styles?  
  * [ ] DropZone has correct dashed border, padding, and alignment?  
  * [ ] CoverArtWidget has correct fixed size, border, and placeholder style?  
  * [ ] QProgressBar has correct background/foreground colors and height?  
  * [ ] Spacing between elements looks consistent and follows guidelines?  
  * [ ] Padding within elements (inputs, buttons) looks appropriate?

## **12. Skeleton Code Sample**

```python
# mainwindow.py (Simplified example)
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QSplitter, QLabel, QListWidget,
    QFormLayout, QGroupBox, QPushButton, QProgressBar, QHBoxLayout,
    QGridLayout, QLineEdit, QTextEdit, QComboBox, QSizePolicy, QAbstractItemView
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QPixmap

class DropZone(QLabel):
    """Custom drag-and-drop area for files."""
    def __init__(self, text: str = "Drag & Drop files here or Click to Select") -> None:
        super().__init__(text)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setStyleSheet(
            "border: 2px dashed #d1d5db; border-radius: 6px; color: #6b7280;"
            "font-size: 13px; padding: 24px; background-color: #fafafa;"
        )
        self.setAcceptDrops(True)
        
    # Drag/drop event handlers would be implemented here

class CoverArt(QLabel):
    """Square placeholder for cover art."""
    def __init__(self):
        super().__init__("Drag Image Here")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFixedSize(QSize(180, 180))
        self.setStyleSheet(
            "background: #e5e7eb; color: #9ca3af; border: 1px solid #d1d5db;"
            "font-size: 12px; border-radius: 4px;"
        )
        self.setAcceptDrops(True)
        
    # Drag/drop event handlers would be implemented here

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Audiobook Boss")
        self.resize(1280, 820)
        # Set minimum size to prevent squishing
        self.setMinimumSize(1100, 700)
        self._build_ui()
        
    def _build_ui(self):
        # Central widget
        central = QWidget()
        self.setCentralWidget(central)
        
        # Root layout
        root = QVBoxLayout(central)
        root.setContentsMargins(12, 12, 12, 12)
        root.setSpacing(8)
        
        # App title
        title = QLabel("Audiobook Boss")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-weight: bold; font-size: 18px; padding: 8px;")
        root.addWidget(title)
        
        # Main content splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        root.addWidget(splitter, 1)  # 1 = stretch factor
        
        # Add panels to splitter
        left_panel = self._build_left_panel()
        right_panel = self._build_right_panel()
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set stretch factors
        splitter.setStretchFactor(0, 1)  # Left panel (Input)
        splitter.setStretchFactor(1, 2)  # Right panel (Metadata & Output)
        
        # Handle initial sizing
        total_width = self.width()
        splitter.setSizes([int(total_width * 0.33), int(total_width * 0.67)])
        
        # Status bar
        status_bar = self._build_status_bar()
        root.addWidget(status_bar)
    
    def _build_left_panel(self):
        # Left panel container
        panel = QWidget()
        panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        panel.setMinimumHeight(500)
        panel.setStyleSheet("background-color: white; border: 1px solid #e5e7eb; border-radius: 8px;")
        
        # Panel layout
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)
        
        # Panel title
        title = QLabel("Input Audio Files")
        title.setStyleSheet("font-weight: bold; font-size: 16px;")
        layout.addWidget(title)
        
        # Drag & drop area
        drop_zone = DropZone()
        drop_zone.setObjectName("dropZoneLabel")
        layout.addWidget(drop_zone)
        
        # File order label
        file_order = QLabel("File Order:")
        layout.addWidget(file_order)
        
        # File list
        file_list = QListWidget()
        file_list.setObjectName("fileListWidget")
        file_list.setDragDropMode(QAbstractItemView.InternalMove)
        file_list.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        file_list.setMinimumHeight(150)
        layout.addWidget(file_list, 1)  # 1 = stretch factor
        
        # Properties group
        props_box = QGroupBox("Selected File Properties")
        props_form = QFormLayout(props_box)
        for lbl in ("Bitrate", "Sample Rate", "Channels", "File Size", "Combined Size"):
            value_label = QLabel("—")
            value_label.setStyleSheet("font-family: monospace;")
            props_form.addRow(f"{lbl}:", value_label)
        layout.addWidget(props_box)
        
        # Folder monitoring (future)
        folder_box = QGroupBox("Folder Monitoring (Future)")
        folder_layout = QVBoxLayout(folder_box)
        folder_status = QLabel("Monitored Folder: N/A")
        folder_btn = QPushButton("Change Watched Folder")
        folder_btn.setEnabled(False)
        folder_btn.setProperty("buttonStyle", "secondary")
        folder_layout.addWidget(folder_status)
        folder_layout.addWidget(folder_btn)
        folder_box.setStyleSheet("color: #9ca3af;")  # Dimmed
        layout.addWidget(folder_box)
        
        return panel
    
    def _build_right_panel(self):
        # Right panel would be implemented similarly to left panel
        # with appropriate metadata fields, cover art, etc.
        # This is just a placeholder
        panel = QWidget()
        panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        panel.setMinimumHeight(500)
        panel.setStyleSheet("background-color: white; border: 1px solid #e5e7eb; border-radius: 8px;")
        
        # Implementation details would go here...
        
        return panel
    
    def _build_status_bar(self):
        # Status bar would be implemented with progress indicators
        # and process button
        # This is just a placeholder
        bar = QWidget()
        layout = QHBoxLayout(bar)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Implementation details would go here...
        
        return bar
```

## **13. Final Advice**

* **Test Critical Workflows First:** Verify layout behavior with size policies before adding detailed styling.
* **Validate Scrolling Regularly:** Check for scrollbars appearing at the minimum window size.
* **Use Object Names Consistently:** Set object names for all widgets needing specific styling.
* **Leverage Qt's Layout System:** Never use fixed positions; always rely on proper layout management.
* **Maintain Clean Widget Hierarchy:** Keep the widget tree organized to make styling and behavior predictable.
