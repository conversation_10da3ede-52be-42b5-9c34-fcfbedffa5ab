# Audiobook Boss v6 Refactoring Plan v2
## Executive Summary

**Current State**: Audiobook Boss v6 is functional but suffers from architectural complexity:
- 4-5 layer signal chains make debugging difficult
- Metadata logic scattered across 4+ modules
- 699-line MainWindow exceeds maintainability limits
- No CI/CD or automated testing infrastructure
- Heavy Qt coupling prevents isolated testing

**Enhanced Goal**: Safely refactor to a maintainable architecture through incremental, validated changes with automated safety nets.

## Phased Refactoring Plan

### ✅ Phase 0: Safety Net Infrastructure (Week 0 - CRITICAL) 
DONE - See docs/SAFETY_NET_GUIDE.md
**Success Criteria**: CI/CD running, baseline established, no code changes

🤖 **SUB-AGENT OPPORTUNITY**: Spawn agent to "Set up GitHub Actions CI/CD - create workflow file, configure pytest and ruff, ensure builds pass"

#### Step 0.1: GitHub Actions Setup

```
Agent Instructions:
1. Create .github/workflows/ci.yml that:
   - Runs on all pushes and PRs
   - Executes: pytest -q
   - Executes: pytest --cov=abb --cov-report=xml
   - Executes: ruff check .
   - Uploads coverage XML as artifact
2. Create minimal tests/test_smoke.py with assert True
3. Ensure workflow passes on current codebase
```

#### Step 0.2: Golden Run Baseline

```
Agent Instructions:
1. Create tests/data/golden/ directory
2. Add sample MP3 file (30 seconds, with metadata)
3. Run current app to convert MP3 → M4B
4. Store output in golden/ directory
5. Calculate SHA256 hash, save in golden.json
6. Document exact FFmpeg command used
```

 
---
### ✅ Phase 1A: Metadata Handler Wrapper (Week 1)

**Success Criteria**: New handler wraps existing functions, no behavior changes

#### [X] Step 1A.1: Create UnifiedMetadataHandler Facade

```
Agent Instructions:
1. Create src/abb/services/unified_metadata_handler.py
2. Import and wrap existing functions:
   - From metadata_utils: extract_metadata, extract_tags, extract_cover
   - From FFmpegCommandBuilder: ABB_TO_FFMPEG_METADATA_MAP_GENERAL
3. Implement facade pattern:
   class UnifiedMetadataHandler:
       def load_from_file(self, path): # calls extract_metadata
       def update_field(self, field, value): # manages state
       def get_for_ffmpeg(self): # returns formatted tags
4. Add ABB_NEW_META environment variable check (default: False)
```

#### [X] Step 1A.2: Initial Testing

```
Agent Instructions:
1. Create tests/unit/services/test_unified_metadata_handler.py
2. Test against golden MP3 file
3. Verify all metadata fields extracted correctly
4. Ensure wrapper doesn't change behavior
5. Add to CI pipeline
```

### Phase 1B: Metadata Migration (Week 1)

**Success Criteria**: All code uses UnifiedMetadataHandler when ABB_NEW_META=True

🤖 **SUB-AGENT OPPORTUNITY**: Spawn agent to "Find all metadata usage - trace every metadata function call, list all callers with line numbers"

#### [X] Step 1B.1: Update MetadataService

```
Agent Instructions:
1. In MetadataService, add UnifiedMetadataHandler instance
2. When ABB_NEW_META=True:
   - Replace internal logic with handler calls
   - Consolidate duplicate methods
   - Reduce signals to: metadata_loaded, metadata_updated, metadata_error
3. Keep backward compatibility when flag is False
4. Update tests to cover both paths
```

#### [X] Step 1B.2: Update Other Components

```
Agent Instructions:
1. Update MainController to check ABB_NEW_META
2. Updat e FFmpegCommandBuilder to use handler for mappings
3. Remove duplicate constants from all files
4. Run full test suite with flag True and False
5. Compare golden run output with both settings
```

### ✅ Phase 2: Service Interfaces & Qt Decoupling (Week 2) ✅

**Success Criteria**: Services work without Qt when using callbacks
IMPORTANT UPDATE: This is technically completed but overall test coverage decreased tp 44% (from 67%) due to the new modules created in this phase. We need to write testing to cover what we've done in phase 2 thus far and I'm a little concerned that the agents did not first write test to validate behavior before they implemented the new source code modules.

#### Step 2.1: Define Pure Python Protocols

```
Agent Instructions:
1. Create src/abb/services/interfaces.py with Protocols:
   from typing import Protocol, Callable, List, Dict, Any
   
   class IFileService(Protocol):
       def add_files(self, paths: List[str], callback: Callable) -> None: ...
       def remove_file(self, index: int, callback: Callable) -> None: ...
       def get_files(self) -> List[Dict[str, Any]]: ...
   
   class IMetadataHandler(Protocol):
       def load_from_file(self, path: str, callback: Callable) -> None: ...
       def update_field(self, field: str, value: Any) -> None: ...
       def get_for_ffmpeg(self) -> Dict[str, str]: ...
   
   class IProcessingService(Protocol):
       def process(self, files: List[str], settings: Dict, 
                  progress_callback: Callable, complete_callback: Callable) -> None: ...
       def cancel(self) -> None: ...
```

#### Step 2.2: Implement Core Services

```
Agent Instructions:
1. Create pure Python implementations:
   - FileServiceCore (no Qt imports)
   - ProcessingServiceCore (uses subprocess, not QProcess)
2. Services accept callbacks for state changes
3. No framework dependencies in core services
```

#### Step 2.3: Qt Adapter Layer

```
Agent Instructions:
1. Create adapters that wrap pure services:
   class QtFileServiceAdapter(QObject):
       def __init__(self, service: IFileService):
           self._service = service
       
       def add_files(self, paths):
           self._service.add_files(paths, self._on_files_added)
       
       def _on_files_added(self, files):
           self.files_added_signal.emit(files)
2. MainController uses adapters when available
3. Add feature flag ABB_PURE_SERVICES
```

### Phase 3: Signal Chain Simplification (Week 2-3)

**Success Criteria**: Maximum 3 hops for any user action

🤖 **SUB-AGENT OPPORTUNITY**: Spawn agent to "Map all signal chains - find every signal connection, count hops, identify all chains > 3 hops"

#### Step 3.1: Direct Controller Pattern

```
Agent Instructions:
1. Update widgets to accept controller reference:
   - LeftPanelWidget.__init__(self, controller, parent=None)
   - RightPanelWidget.__init__(self, controller, parent=None)
2. Replace long chains with direct calls:
   # Before: widget → signal → window → signal → controller
   # After: widget → controller.method() → signal → widget
3. Maximum pattern: UI → Controller → Service → UI
4. Update tests for new patterns
```

#### Step 3.2: Remove Signal Relays

```
Agent Instructions:
1. Find all signal-to-signal connections in MainWindow
2. Remove intermediate relays
3. Connect source directly to destination
4. Document remaining chains > 3 hops for future work
5. PR must be < 400 LOC (split if needed)
```

### Phase 4: MainWindow Reduction (Week 3)

**Success Criteria**: MainWindow < 600 lines

#### Step 4.1: Extract Setup Logic

```
Agent Instructions:
1. Create src/abb/ui/main_window_setup.py
2. Move all setup methods:
   - Menu creation (≈100 lines)
   - Toolbar setup (≈50 lines)
   - Signal connections (≈75 lines)
3. MainWindow imports and calls setup functions
4. Add line count check to CI (fail if > 600)
```

#### Step 4.2: Delegate to Panels

```
Agent Instructions:
1. Move business logic to panels:
   - File operations → LeftPanelWidget
   - Processing control → RightPanelWidget
2. MainWindow only coordinates panels
3. Each panel manages its own state
4. Update integration tests
```

### Phase 5: Test Simplification (Week 3-4)

**Success Criteria**: Average < 3 mocks per test file

🤖 **SUB-AGENT OPPORTUNITY**: Spawn agent to "Audit test mocking - count mocks per file, identify why each mock exists, suggest alternatives"

#### Step 5.1: Reduce Mock Usage

```
Agent Instructions:
1. For each test with > 5 mocks:
   - Use real objects where possible
   - Test behavior, not method calls
   - Create minimal fixtures
2. Add integration tests for workflows:
   - tests/integration/test_full_flow.py
   - Use real services with temp files
   - Mock only FFmpeg subprocess
```

#### Step 5.2: Golden Run Validation

```
Agent Instructions:
1. Create test that runs full conversion
2. Compare output hash with golden.json
3. Run after each major refactoring phase
4. Add to CI as regression test
```

### Phase 6: Feature Completion (Week 4)

**Success Criteria**: All planned features working

#### Step 6.1: Cancel Processing

```
Agent Instructions:
1. Add cancellation to ProcessingServiceCore:
   - Set _cancelled flag
   - Check flag in processing loop
   - Clean up partial files
2. Update UI with cancel button state
3. Test cancellation at multiple points
```

#### Step 6.2: Error Categorization

```
Agent Instructions:
1. Create error categories enum:
   class ErrorCategory(Enum):
       FILE_NOT_FOUND = "File not found"
       PERMISSION_DENIED = "Permission denied"
       FFMPEG_ERROR = "FFmpeg processing failed"
       DISK_FULL = "Insufficient disk space"
2. Update services to use categories
3. Show user-friendly messages in status bar
```

#### Step 6.3: Preview Feature

```
Agent Instructions:
1. Add preview mode to ProcessingServiceCore:
   - Generate 30-second sample
   - Use -t 30 FFmpeg flag
   - Save to temp directory
2. Add preview button to UI
3. Test with various formats
```

### Phase 7: Tech Debt Cleanup (Week 5)

**Success Criteria**: Clean code analysis passes

#### Step 7.1: Strict Linting

```
Agent Instructions:
1. Run ruff check --fix src/
2. Run mypy --strict src/ (allow failures initially)
3. Fix all linting issues
4. Document unfixable issues in docs/tech_debt.md
```

#### Step 7.2: Final Validation

```
Agent Instructions:
1. Run full test suite
2. Validate against golden run
3. Check all metrics:
   - MainWindow < 600 lines
   - No signal chains > 3 hops
   - Test mock average < 3
4. Create final PR
```

## Implementation Guidelines

### Safety Rules
1. **Feature Flags**: ABB_NEW_META, ABB_PURE_SERVICES for gradual rollout
2. **PR Size**: Maximum 400 LOC per PR
3. **CI Must Pass**: No merging with failing tests
4. **Golden Run**: Validate after each phase

### Testing Strategy
1. **Before Changes**: Write tests for current behavior
2. **During Changes**: Update tests incrementally
3. **After Changes**: Add integration tests
4. **Regression**: Always run golden comparison

### Risk Mitigation
1. **Incremental**: Small, reversible changes
2. **Parallel Paths**: Old and new code coexist
3. **Validation**: Automated testing at each step
4. **Rollback**: Feature flags allow quick reversion

## Success Metrics

- **Week 0**: CI/CD operational, baseline established
- **Week 1**: Metadata unified (behind flag)
- **Week 2**: Services decoupled, signals simplified
- **Week 3**: MainWindow reduced, tests improved
- **Week 4**: Features complete
- **Week 5**: Tech debt addressed, ready for release

## Next Steps

1. Create Phase 0 infrastructure immediately
2. Review this plan with team
3. Create tracking issues for each phase
4. Begin Phase 0 implementation

Remember: **Safety first, speed second**. A broken refactoring helps no one.